# LocationV1 - Network Database Setup Guide

## 🎯 **Overview**

This setup allows multiple PCs to share the same MySQL database:
- **Server PC**: Hosts the MySQL database
- **Client PCs**: Connect to the server database
- **All PCs see the same data in real-time**

## 🖥️ **Server PC Setup (Your Main PC)**

### **Step 1: Configure MySQL for Network Access**

1. **Open MySQL Command Line** or MySQL Workbench
2. **Run these commands:**
   ```sql
   CREATE DATABASE IF NOT EXISTS locationdb5;
   CREATE USER 'locationapp'@'%' IDENTIFIED BY 'location123';
   GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%';
   FLUSH PRIVILEGES;
   ```

### **Step 2: Configure Windows Firewall**
```bash
# Run as Administrator
setup-server-pc.bat
```

### **Step 3: Find Your IP Address**
- Run `ipconfig` in Command Prompt
- Note your IPv4 address (e.g., *************)
- Give this IP to other PC users

## 💻 **Client PC Setup (Other PCs)**

### **Step 1: Get Server IP Address**
- Get the IP address from the server PC owner

### **Step 2: Create Client Configuration**
```bash
create-client-config.bat
```
- Enter the server IP when prompted
- This creates `hibernate-client.cfg.xml`

### **Step 3: Update Application**
1. Replace `src/main/resources/hibernate.cfg.xml` with `hibernate-client.cfg.xml`
2. Rebuild the application:
   ```bash
   mvn clean package
   ```
3. Create new executable:
   ```bash
   build-simple-fix.bat
   ```

## 🔧 **Testing the Setup**

### **Test Database Connection**
```bash
mysql -h SERVER_IP_ADDRESS -u locationapp -p locationdb5
# Password: location123
```

### **Test Application**
1. Start application on server PC
2. Add a test client or vehicle
3. Start application on client PC
4. Verify you see the same data

## 📋 **Network Requirements**

- **Same Network**: All PCs must be on the same local network
- **Port 3306**: Must be open on server PC
- **MySQL Running**: Server PC must have MySQL running
- **Stable Connection**: Reliable network connection between PCs

## 🚀 **Benefits**

✅ **Real-time data sharing**
✅ **Single source of truth**
✅ **Automatic synchronization**
✅ **Centralized backup**
✅ **No data conflicts**

## 🛠️ **Troubleshooting**

### **Can't Connect to Server**
1. Check server IP address
2. Verify MySQL is running on server
3. Check Windows Firewall settings
4. Test with: `ping SERVER_IP`

### **Access Denied Error**
1. Verify user 'locationapp' exists
2. Check password is 'location123'
3. Ensure user has network access permissions

### **Application Won't Start**
1. Check hibernate-client.cfg.xml has correct IP
2. Verify MySQL connector is included
3. Check server PC is running and accessible

## 📞 **Quick Setup Summary**

### **Server PC (Your PC):**
1. Run `setup-server-pc.bat`
2. Execute MySQL commands
3. Share your IP address

### **Client PCs (Other PCs):**
1. Run `create-client-config.bat`
2. Enter server IP
3. Replace hibernate.cfg.xml
4. Rebuild application

**Result: All PCs share the same database! 🎉**
