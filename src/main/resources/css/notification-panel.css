/* Notification Panel Styles */

.notification-header {
    -fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%);
    -fx-background-radius: 10px 10px 0 0;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.stats-container {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15px;
}

.stat-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-padding: 20px;
    -fx-min-width: 120px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.stat-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 3);
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
}

.stat-number {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #667eea;
}

.stat-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

.filter-container {
    -fx-background-color: #ffffff;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1px 0;
}

.quick-actions {
    -fx-background-color: #f8f9fa;
    -fx-padding: 10px 20px;
}

.btn-action {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 20px;
    -fx-background-radius: 20px;
    -fx-padding: 8px 15px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.btn-action:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
}

.notifications-list {
    -fx-background-color: #ffffff;
}

.notification-item {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-spacing: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.notification-item:hover {
    -fx-background-color: #f8f9fa;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.notification-item.unread {
    -fx-border-color: #667eea;
    -fx-border-width: 2px;
    -fx-background-color: #f8f9ff;
}

.notification-item.urgent {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
    -fx-background-color: #fff5f5;
}

.notification-header-row {
    -fx-alignment: center-left;
    -fx-spacing: 10px;
}

.notification-icon {
    -fx-font-size: 20px;
    -fx-min-width: 30px;
    -fx-alignment: center;
}

.notification-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.notification-time {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

.notification-message {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
    -fx-wrap-text: true;
}

.notification-actions {
    -fx-alignment: center-right;
    -fx-spacing: 8px;
}

.notification-type-badge {
    -fx-background-radius: 12px;
    -fx-padding: 4px 8px;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.notification-type-badge.rappel-retour {
    -fx-background-color: #ffc107;
}

.notification-type-badge.location-confirmee {
    -fx-background-color: #28a745;
}

.notification-type-badge.paiement-du {
    -fx-background-color: #dc3545;
}

.notification-type-badge.maintenance-due {
    -fx-background-color: #fd7e14;
}

.notification-type-badge.welcome-admin {
    -fx-background-color: #6f42c1;
}

.notification-type-badge.system-alert {
    -fx-background-color: #dc3545;
}

.btn-notification-action {
    -fx-background-color: transparent;
    -fx-border-color: #6c757d;
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
    -fx-background-radius: 15px;
    -fx-padding: 4px 10px;
    -fx-font-size: 11px;
    -fx-cursor: hand;
}

.btn-notification-action:hover {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
}

.btn-notification-action.primary {
    -fx-border-color: #667eea;
    -fx-text-fill: #667eea;
}

.btn-notification-action.primary:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
}

.pagination-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
}

.empty-state {
    -fx-alignment: center;
    -fx-spacing: 20px;
    -fx-padding: 50px;
}

.empty-state-icon {
    -fx-font-size: 48px;
    -fx-text-fill: #adb5bd;
}

.empty-state-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
}

.empty-state-message {
    -fx-font-size: 14px;
    -fx-text-fill: #adb5bd;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

/* Button Styles */
.btn-primary {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.btn-primary:hover {
    -fx-background-color: #5a6fd8;
}

.btn-secondary {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.btn-secondary:hover {
    -fx-background-color: #5a6268;
}

/* ComboBox and TextField Styles */
.combo-box, .text-field {
    -fx-border-color: #ced4da;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8px 12px;
}

.combo-box:focused, .text-field:focused {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(three-pass-box, rgba(102, 126, 234, 0.25), 0, 0, 0, 0);
}

/* Scrollbar Styles */
.scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-border-color: transparent;
    -fx-background-radius: 0;
    -fx-border-radius: 0;
}

.scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 5;
    -fx-border-radius: 5;
}

.scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #adb5bd;
}

.scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #6c757d;
}
