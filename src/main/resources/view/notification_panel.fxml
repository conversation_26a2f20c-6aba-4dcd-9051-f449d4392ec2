<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.NotificationPanelController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%); -fx-background-radius: 10px 10px 0 0;">
         <children>
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="🔔 Centre de Notifications" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnMarkAllRead" mnemonicParsing="false" onAction="#handleMarkAllAsRead"
                    style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;"
                    text="Tout marquer comme lu" />
            <Button fx:id="btnRefresh" mnemonicParsing="false" onAction="#handleRefresh"
                    style="-fx-background-color: #667eea; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;"
                    text="🔄 Actualiser" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Statistics Cards -->
      <HBox spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15px;">
         <children>
            <VBox alignment="CENTER" style="-fx-background-color: white; -fx-background-radius: 10px; -fx-padding: 20px; -fx-min-width: 120px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
               <children>
                  <Label fx:id="lblTotalNotifications" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #667eea;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-font-weight: 500;" text="Total" />
               </children>
            </VBox>
            <VBox alignment="CENTER" style="-fx-background-color: white; -fx-background-radius: 10px; -fx-padding: 20px; -fx-min-width: 120px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
               <children>
                  <Label fx:id="lblUnreadNotifications" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #dc3545;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-font-weight: 500;" text="Non lues" />
               </children>
            </VBox>
            <VBox alignment="CENTER" style="-fx-background-color: white; -fx-background-radius: 10px; -fx-padding: 20px; -fx-min-width: 120px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
               <children>
                  <Label fx:id="lblTodayNotifications" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-font-weight: 500;" text="Aujourd'hui" />
               </children>
            </VBox>
            <VBox alignment="CENTER" style="-fx-background-color: white; -fx-background-radius: 10px; -fx-padding: 20px; -fx-min-width: 120px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
               <children>
                  <Label fx:id="lblUrgentNotifications" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #ffc107;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-font-weight: 500;" text="Urgentes" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Filter and Search -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-container">
         <children>
            <Label text="Filtrer par:" />
            <ComboBox fx:id="cmbNotificationType" prefWidth="200.0" promptText="Type de notification" />
            <ComboBox fx:id="cmbNotificationStatus" prefWidth="150.0" promptText="Statut" />
            <TextField fx:id="txtSearch" prefWidth="250.0" promptText="Rechercher dans les notifications..." />
            <Button fx:id="btnClearFilters" mnemonicParsing="false" onAction="#handleClearFilters" styleClass="btn-secondary" text="Effacer" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <!-- Quick Action Buttons -->
      <HBox spacing="10.0" styleClass="quick-actions">
         <children>
            <Label text="Actions rapides:" />
            <Button fx:id="btnRappelRetour" mnemonicParsing="false" onAction="#handleRappelRetour" styleClass="btn-action" text="🚗 Rappels de retour" />
            <Button fx:id="btnLocationConfirmee" mnemonicParsing="false" onAction="#handleLocationConfirmee" styleClass="btn-action" text="✅ Locations confirmées" />
            <Button fx:id="btnPaiementsDus" mnemonicParsing="false" onAction="#handlePaiementsDus" styleClass="btn-action" text="💰 Paiements dus" />
            <Button fx:id="btnMaintenanceDue" mnemonicParsing="false" onAction="#handleMaintenanceDue" styleClass="btn-action" text="🔧 Maintenance" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </HBox>

      <!-- Notifications List -->
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox fx:id="notificationsContainer" spacing="5.0" styleClass="notifications-list">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>

      <!-- Pagination -->
      <HBox alignment="CENTER" spacing="10.0" styleClass="pagination-container">
         <children>
            <Button fx:id="btnPrevPage" mnemonicParsing="false" onAction="#handlePrevPage" text="← Précédent" />
            <Label fx:id="lblPageInfo" text="Page 1 sur 1" />
            <Button fx:id="btnNextPage" mnemonicParsing="false" onAction="#handleNextPage" text="Suivant →" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </children>

</VBox>
