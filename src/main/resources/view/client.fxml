<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.ClientController">
    <content>
        <VBox minHeight="-Infinity" minWidth="-Infinity" prefHeight="1588.0" prefWidth="1068.0" spacing="24" style="-fx-background-color: transparent;">

    <padding><Insets bottom="200" left="24" right="24" top="24" /></padding>

            <!-- Modern Header Section -->
            <VBox spacing="20" style="-fx-background-color: linear-gradient(to right, #1a3c40, #2d5561); -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="8" HBox.hgrow="ALWAYS">
                        <Label style="-fx-text-fill: white; -fx-font-size: 28px; -fx-font-weight: 700;" text="Gestion des Clients" />
                        <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 16px;" text="Gérez efficacement votre base de clients" />
                    </VBox>
                    <HBox alignment="CENTER_RIGHT" spacing="12">
                        <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-cursor: hand;" text="🔄 Actualiser" />
                        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: rgba(16,185,129,0.9); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-weight: 600; -fx-cursor: hand;" text="📊 Exporter" />
                    </HBox>
                </HBox>
            </VBox>

            <!-- Main Content Area -->
            <HBox alignment="TOP_LEFT" spacing="20">
                <!-- Left Panel - Client List -->
                <VBox prefWidth="600" spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" HBox.hgrow="ALWAYS">
                    <!-- Search and Filter -->
                    <VBox spacing="12">
                        <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Recherche et Filtres" />
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <TextField fx:id="txtSearch" promptText="Rechercher par nom, prénom, CIN..." style="-fx-background-radius: 8; -fx-padding: 10 14; -fx-pref-width: 300;" />
                            <Button fx:id="btnSearch" onAction="#handleSearch" style="-fx-background-color: #567FAB; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-cursor: hand;" text="🔍 Rechercher" />
                            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 8; -fx-padding: 10 16; -fx-cursor: hand;" text="Effacer" />
                        </HBox>
                    </VBox>

                    <!-- Client Table -->
                    <VBox spacing="12">
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Liste des Clients" />
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #64748b;" text="Total: 0" />
                        </HBox>

                        <TableView fx:id="clientTable" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-table-header-border-color: transparent;" VBox.vgrow="ALWAYS">
                            <columns>
                                <TableColumn fx:id="idColumn" prefWidth="50" text="ID" />
                                <TableColumn fx:id="nomColumn" prefWidth="120" text="Nom" />
                                <TableColumn fx:id="prenomColumn" prefWidth="120" text="Prénom" />
                                <TableColumn fx:id="cinColumn" prefWidth="100" text="CIN" />
                                <TableColumn fx:id="telephoneColumn" prefWidth="100" text="Téléphone" />
                                <TableColumn fx:id="emailColumn" text="Email" />
                                <TableColumn fx:id="permisColumn" prefWidth="100" text="Permis" />
                                <TableColumn fx:id="adresseColumn" prefWidth="150" text="Adresse" />
                            </columns>
                            <columnResizePolicy>
                                <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                            </columnResizePolicy>
                        </TableView>

                        <HBox alignment="CENTER_RIGHT" spacing="10">
                            <Button fx:id="btnAddClient" onAction="#handleAjouter" style="-fx-background-color: #1a3c40; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="➕ Ajouter" />
                            <Button fx:id="btnUpdateClient" onAction="#handleModifier" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="✏️ Modifier" />
                            <Button fx:id="btnDeleteClient" onAction="#handleSupprimer" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="🗑️ Supprimer" />
                            <Button fx:id="btnViewClient" onAction="#handleViewClient" style="-fx-background-color: #6366f1; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="👁️ Visualiser" />
                        </HBox>
                    </VBox>
                </VBox>

                <!-- Right Panel - Client Details/Form -->
                <VBox minHeight="-Infinity" minWidth="-Infinity" prefHeight="1252.0" prefWidth="400" spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                    <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Détails du Client" />

                    <ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;">
                        <VBox spacing="12">
                            <!-- Personal Info Section -->
                            <VBox spacing="8" style="-fx-background-color: #f8fafc; -fx-padding: 16; -fx-background-radius: 12;">
                                <Label style="-fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="Informations Personnelles" />
                                <GridPane hgap="10" vgap="8">
                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Nom:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                    <TextField fx:id="txtNomClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Prénom:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                    <TextField fx:id="txtPrenomClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="CIN:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                    <TextField fx:id="txtCinClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Téléphone:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                    <TextField fx:id="txtTelephoneClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                                    <TextField fx:id="txtEmailClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="4" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Permis:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                                    <TextField fx:id="txtPermisClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="5" />

                                    <Label style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" text="Adresse:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                                    <TextField fx:id="txtAdresseClientForm" style="-fx-background-radius: 6; -fx-padding: 8 12;" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                           <columnConstraints>
                              <ColumnConstraints />
                              <ColumnConstraints />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                           </rowConstraints>
                                </GridPane>
                            </VBox>

                            <!-- Documents Section -->
                            <VBox spacing="12">
                                <Label style="-fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="Documents" />

                                <VBox spacing="8">
                                    <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #4b5563;" text="Permis Recto" />
                                    <HBox alignment="CENTER_LEFT" spacing="8">
                                        <ImageView fx:id="imgPermisRecto" fitHeight="120" fitWidth="180" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8;" />
                                        <Button fx:id="btnUploadPermisRecto" onAction="#handleUploadPermisRecto" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 6; -fx-padding: 8 12; -fx-cursor: hand;" text="Choisir..." />
                                    </HBox>
                                </VBox>

                                <VBox spacing="8">
                                    <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #4b5563;" text="Permis Verso" />
                                    <HBox alignment="CENTER_LEFT" spacing="8">
                                        <ImageView fx:id="imgPermisVerso" fitHeight="120" fitWidth="180" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8;" />
                                        <Button fx:id="btnUploadPermisVerso" onAction="#handleUploadPermisVerso" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 6; -fx-padding: 8 12; -fx-cursor: hand;" text="Choisir..." />
                                    </HBox>
                                </VBox>

                                <VBox spacing="8">
                                    <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #4b5563;" text="CIN Recto" />
                                    <HBox alignment="CENTER_LEFT" spacing="8">
                                        <ImageView fx:id="imgCinRecto" fitHeight="120" fitWidth="180" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8;" />
                                        <Button fx:id="btnUploadCinRecto" onAction="#handleUploadCinRecto" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 6; -fx-padding: 8 12; -fx-cursor: hand;" text="Choisir..." />
                                    </HBox>
                                </VBox>

                                <VBox spacing="8">
                                    <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #4b5563;" text="CIN Verso" />
                                    <HBox alignment="CENTER_LEFT" spacing="8">
                                        <ImageView fx:id="imgCinVerso" fitHeight="120" fitWidth="180" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8;" />
                                        <Button fx:id="btnUploadCinVerso" onAction="#handleUploadCinVerso" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 6; -fx-padding: 8 12; -fx-cursor: hand;" text="Choisir..." />
                                    </HBox>
                                </VBox>
                            </VBox>
                        </VBox>
                    </ScrollPane>

                    <HBox alignment="CENTER_RIGHT" spacing="10">
                        <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="Annuler" />
                        <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-cursor: hand;" text="💾 Enregistrer" />
                    </HBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>
