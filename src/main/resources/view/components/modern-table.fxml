<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<!-- Modern Table Component Template -->
<VBox spacing="16" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../styles/modern-theme.css" />
    </stylesheets>
    
    <!-- Table Header -->
    <HBox alignment="CENTER_LEFT" spacing="16" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12 12 0 0; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);">
        <VBox spacing="4" HBox.hgrow="ALWAYS">
            <Label styleClass="text-heading-3" style="-fx-font-size: 18px; -fx-font-weight: 600; -fx-text-fill: #1e293b;" text="Données" />
            <Label styleClass="text-caption" style="-fx-text-fill: #64748b;" text="Gérez vos informations" />
        </VBox>
        
        <!-- Search and Actions -->
        <HBox spacing="12" alignment="CENTER_RIGHT">
            <TextField promptText="Rechercher..." styleClass="modern-text-field" 
                      style="-fx-min-width: 200; -fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 8 12;" />
            <Button styleClass="btn-secondary" text="🔍" style="-fx-min-width: 40; -fx-padding: 8;" />
            <Separator orientation="VERTICAL" style="-fx-background-color: #e2e8f0;" />
            <Button styleClass="btn-primary" text="➕ Ajouter" />
            <Button styleClass="btn-secondary" text="📊 Exporter" />
        </HBox>
    </HBox>
    
    <!-- Modern Table -->
    <TableView styleClass="modern-table-view" style="-fx-background-color: white; -fx-background-radius: 0 0 12 12; -fx-border-color: #e2e8f0; -fx-border-width: 0 1 1 1; -fx-border-radius: 0 0 12 12;">
        <placeholder>
            <VBox alignment="CENTER" spacing="16" style="-fx-padding: 40;">
                <Label style="-fx-font-size: 48px; -fx-text-fill: #e2e8f0;" text="📋" />
                <VBox alignment="CENTER" spacing="8">
                    <Label styleClass="text-heading-3" style="-fx-text-fill: #64748b;" text="Aucune donnée disponible" />
                    <Label styleClass="text-caption" style="-fx-text-fill: #9ca3af;" text="Commencez par ajouter des éléments" />
                </VBox>
                <Button styleClass="btn-primary" text="➕ Ajouter le premier élément" />
            </VBox>
        </placeholder>
    </TableView>
    
    <!-- Table Footer -->
    <HBox alignment="CENTER_LEFT" spacing="16" style="-fx-background-color: #f8fafc; -fx-padding: 16 20; -fx-background-radius: 0 0 12 12; -fx-border-color: #e2e8f0; -fx-border-width: 1 1 0 1;">
        <Label styleClass="text-caption" style="-fx-text-fill: #64748b;" text="Total: 0 éléments" />
        <Region HBox.hgrow="ALWAYS" />
        
        <!-- Pagination -->
        <HBox spacing="8" alignment="CENTER_RIGHT">
            <Button styleClass="btn-secondary" text="‹" style="-fx-min-width: 32; -fx-padding: 6;" />
            <Label styleClass="text-caption" style="-fx-text-fill: #64748b; -fx-padding: 6 12;" text="Page 1 sur 1" />
            <Button styleClass="btn-secondary" text="›" style="-fx-min-width: 32; -fx-padding: 6;" />
        </HBox>
    </HBox>
</VBox>
