<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<!-- Modern Form Component Template -->
<VBox spacing="24" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../styles/modern-theme.css" />
    </stylesheets>
    
    <!-- Form Header -->
    <VBox spacing="8" styleClass="modern-card-header">
        <Label styleClass="text-heading-2" style="-fx-font-size: 24px; -fx-font-weight: 600; -fx-text-fill: #1e293b;" text="Formulaire" />
        <Label styleClass="text-body" style="-fx-text-fill: #64748b;" text="Remplissez les informations requises" />
    </VBox>
    
    <!-- Form Fields -->
    <VBox spacing="20" styleClass="modern-card" style="-fx-padding: 32;">
        <!-- Text Field Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Nom *" />
            <TextField styleClass="modern-text-field" promptText="Entrez le nom" />
            <Label style="-fx-text-fill: #ef4444; -fx-font-size: 12px; -fx-visible: false;" text="Ce champ est requis" />
        </VBox>
        
        <!-- Email Field Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Email *" />
            <HBox spacing="12" alignment="CENTER_LEFT" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-border-width: 1.5;">
                <Label style="-fx-text-fill: #6b7280; -fx-font-size: 16px;" text="📧" />
                <TextField styleClass="modern-text-field" promptText="<EMAIL>" style="-fx-background-color: transparent; -fx-border-width: 0; -fx-padding: 0;" HBox.hgrow="ALWAYS" />
            </HBox>
        </VBox>
        
        <!-- Phone Field Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Téléphone" />
            <HBox spacing="12" alignment="CENTER_LEFT" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-border-width: 1.5;">
                <Label style="-fx-text-fill: #6b7280; -fx-font-size: 16px;" text="📱" />
                <TextField styleClass="modern-text-field" promptText="+33 6 12 34 56 78" style="-fx-background-color: transparent; -fx-border-width: 0; -fx-padding: 0;" HBox.hgrow="ALWAYS" />
            </HBox>
        </VBox>
        
        <!-- ComboBox Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Catégorie" />
            <ComboBox promptText="Sélectionnez une catégorie" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-border-width: 1.5; -fx-min-width: 200;" />
        </VBox>
        
        <!-- TextArea Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Description" />
            <TextArea promptText="Entrez une description..." style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-border-width: 1.5; -fx-min-height: 100;" />
        </VBox>
        
        <!-- File Upload Example -->
        <VBox spacing="8">
            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Document" />
            <HBox spacing="12" alignment="CENTER_LEFT" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-border-width: 1.5; -fx-border-style: dashed;">
                <VBox alignment="CENTER" spacing="8" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: #6b7280; -fx-font-size: 24px;" text="📁" />
                    <Label style="-fx-text-fill: #6b7280; -fx-font-size: 14px;" text="Glissez un fichier ici ou" />
                    <Button styleClass="btn-secondary" text="Parcourir" />
                </VBox>
            </HBox>
        </VBox>
    </VBox>
    
    <!-- Form Actions -->
    <HBox spacing="12" alignment="CENTER_RIGHT" style="-fx-padding: 0 32;">
        <Button styleClass="btn-secondary" text="Annuler" prefWidth="120" />
        <Button styleClass="btn-primary" text="Enregistrer" prefWidth="120" />
    </HBox>
    
    <!-- Success/Error Messages -->
    <VBox spacing="12" style="-fx-padding: 0 32;">
        <!-- Success Message (hidden by default) -->
        <HBox spacing="12" alignment="CENTER_LEFT" style="-fx-background-color: rgba(16,185,129,0.1); -fx-padding: 12 16; -fx-background-radius: 8; -fx-visible: false;">
            <Label style="-fx-text-fill: #059669; -fx-font-size: 16px;" text="✅" />
            <Label style="-fx-text-fill: #059669; -fx-font-size: 14px; -fx-font-weight: 500;" text="Enregistrement réussi !" />
        </HBox>
        
        <!-- Error Message (hidden by default) -->
        <HBox spacing="12" alignment="CENTER_LEFT" style="-fx-background-color: rgba(239,68,68,0.1); -fx-padding: 12 16; -fx-background-radius: 8; -fx-visible: false;">
            <Label style="-fx-text-fill: #dc2626; -fx-font-size: 16px;" text="❌" />
            <Label style="-fx-text-fill: #dc2626; -fx-font-size: 14px; -fx-font-weight: 500;" text="Erreur lors de l'enregistrement" />
        </HBox>
    </VBox>
</VBox>
