<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>

<StackPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LoginController">

    <!-- Background -->
    <VBox style="-fx-background-color: linear-gradient(to bottom right, #667eea, #764ba2); -fx-min-height: 800px;">
        <!-- Animated background elements -->
        <Region style="-fx-background-color: rgba(255,255,255,0.1); -fx-background-radius: 50%; -fx-min-width: 200; -fx-min-height: 200; -fx-translate-x: -100; -fx-translate-y: -50;" />
        <Region style="-fx-background-color: rgba(255,255,255,0.05); -fx-background-radius: 50%; -fx-min-width: 300; -fx-min-height: 300; -fx-translate-x: 200; -fx-translate-y: 100;" />
    </VBox>

    <!-- Main Content -->
    <VBox alignment="CENTER" spacing="40" maxWidth="450" style="-fx-padding: 40;">
        <!-- Logo Section -->
        <VBox alignment="CENTER" spacing="20">
            <StackPane>
                <Circle fill="rgba(255,255,255,0.2)" radius="60" style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 20, 0, 0, 10);" />
                <Circle fill="linear-gradient(to bottom, #4facfe, #00f2fe)" radius="50" />
                <VBox alignment="CENTER" spacing="2">
                    <Label style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';" text="🚗" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';" text="LV" />
                </VBox>
            </StackPane>
            <VBox alignment="CENTER" spacing="8">
                <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700; -fx-font-family: 'Segoe UI';" text="LocationV1" />
                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 16px; -fx-font-family: 'Segoe UI';" text="Système de Gestion de Location" />
            </VBox>
        </VBox>

        <!-- Login Form -->
        <VBox spacing="30" style="-fx-background-color: rgba(255,255,255,0.95); -fx-padding: 40; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 30, 0, 0, 10);">
            <VBox alignment="CENTER" spacing="8">
                <Label style="-fx-font-size: 24px; -fx-font-weight: 600; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="Connexion Admin / Agent" />
                <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Accédez à votre espace de gestion" />
            </VBox>

            <VBox spacing="24">
                <!-- Username Field -->
                <VBox spacing="8">
                    <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Segoe UI';" text="Nom d'utilisateur" />
                    <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 12; -fx-background-radius: 12; -fx-padding: 16; -fx-border-width: 1.5;">
                        <Label style="-fx-text-fill: #6b7280; -fx-font-size: 16px;" text="👤" />
                        <TextField fx:id="usernameField" promptText="admin ou agent1" style="-fx-background-color: transparent; -fx-border-width: 0; -fx-padding: 0; -fx-font-size: 15px; -fx-font-family: 'Segoe UI';" HBox.hgrow="ALWAYS" />
                    </HBox>
                </VBox>

                <!-- Password Field -->
                <VBox spacing="8">
                    <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Segoe UI';" text="Mot de passe" />
                    <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-background-color: #f9fafb; -fx-border-color: #d1d5db; -fx-border-radius: 12; -fx-background-radius: 12; -fx-padding: 16; -fx-border-width: 1.5;">
                        <Label style="-fx-text-fill: #6b7280; -fx-font-size: 16px;" text="🔒" />
                        <PasswordField fx:id="passwordField" promptText="Votre mot de passe" style="-fx-background-color: transparent; -fx-border-width: 0; -fx-padding: 0; -fx-font-size: 15px; -fx-font-family: 'Segoe UI';" HBox.hgrow="ALWAYS" />
                    </HBox>
                </VBox>

                <!-- Login Button -->
                <Button fx:id="loginButton" onAction="#handleLogin" prefHeight="50" maxWidth="Infinity"
                        style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: 600; -fx-background-radius: 12; -fx-cursor: hand; -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.4), 15, 0, 0, 5); -fx-font-family: 'Segoe UI';"
                        text="Se connecter" />
            </VBox>
        </VBox>

        <!-- Login Info -->
        <VBox alignment="CENTER" spacing="8" style="-fx-padding: 20 0 0 0;">
            <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 14px; -fx-font-weight: 500; -fx-font-family: 'Segoe UI';" text="Système de gestion pour administrateurs et agents" />
            <Label style="-fx-text-fill: rgba(255,255,255,0.6); -fx-font-size: 12px; -fx-font-family: 'Segoe UI';" text="Utilisez vos identifiants pour accéder au système" />
        </VBox>
    </VBox>
</StackPane>

    <!-- Footer -->

