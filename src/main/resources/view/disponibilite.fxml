<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: #f8fafc; -fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DisponibiliteController">
    <content>
        <VBox spacing="25.0" style="-fx-background-color: #f8fafc; -fx-padding: 30;" prefHeight="1000">
            <padding><Insets bottom="50.0" /></padding>
            
            <!-- Header Section -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="8">
                        <Label text="📅 Calendrier de Disponibilité" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                        <Label fx:id="vehicleInfoLabel" text="Chargement des informations du véhicule..." style="-fx-font-size: 16px; -fx-text-fill: #64748b;" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <VBox spacing="8" alignment="CENTER_RIGHT">
                        <Label fx:id="currentStatusLabel" text="Statut: Chargement..." style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #059669;" />
                        <Label fx:id="nextAvailableLabel" text="" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" />
                    </VBox>
                </HBox>
                
                <!-- Status Legend -->
                <HBox spacing="25" alignment="CENTER_LEFT" style="-fx-padding: 15 0 0 0;">
                    <HBox spacing="8" alignment="CENTER_LEFT">
                        <Label style="-fx-background-color: #10b981; -fx-min-width: 18; -fx-min-height: 18; -fx-background-radius: 4;" />
                        <Label text="Disponible" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 600;" />
                    </HBox>
                    <HBox spacing="8" alignment="CENTER_LEFT">
                        <Label style="-fx-background-color: #f59e0b; -fx-min-width: 18; -fx-min-height: 18; -fx-background-radius: 4;" />
                        <Label text="Réservé" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 600;" />
                    </HBox>
                    <HBox spacing="8" alignment="CENTER_LEFT">
                        <Label style="-fx-background-color: #ef4444; -fx-min-width: 18; -fx-min-height: 18; -fx-background-radius: 4;" />
                        <Label text="En cours location" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 600;" />
                    </HBox>
                    <HBox spacing="8" alignment="CENTER_LEFT">
                        <Label style="-fx-background-color: #9ca3af; -fx-min-width: 18; -fx-min-height: 18; -fx-background-radius: 4;" />
                        <Label text="Indisponible" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 600;" />
                    </HBox>
                </HBox>
            </VBox>
            
            <!-- Calendar Section -->
            <VBox spacing="20" style="-fx-background-color: white; -fx-padding: 35; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <!-- Calendar Header -->
                <HBox alignment="CENTER" spacing="30">
                    <Button fx:id="btnPrevMonth" onAction="#handlePrevMonth" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600; -fx-cursor: hand;" text="◀ Mois Précédent" />
                    <Label fx:id="lblCalendarMonth" style="-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Janvier 2024" />
                    <Button fx:id="btnNextMonth" onAction="#handleNextMonth" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600; -fx-cursor: hand;" text="Mois Suivant ▶" />
                </HBox>
                
                <!-- Calendar Grid -->
                <GridPane fx:id="calendarGrid" hgap="3" vgap="3" alignment="CENTER" style="-fx-padding: 20;">
                    <columnConstraints>
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                        <ColumnConstraints minWidth="45" prefWidth="45" />
                    </columnConstraints>
                </GridPane>
            </VBox>
            
            <!-- Selected Date Info Panel -->
            <VBox fx:id="selectedDateInfo" spacing="15" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);" visible="false">
                <Label text="📋 Informations de la Date Sélectionnée" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                <VBox spacing="8">
                    <Label fx:id="lblSelectedDate" style="-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #374151;" />
                    <Label fx:id="lblSelectedDateStatus" style="-fx-font-size: 14px; -fx-text-fill: #64748b;" />
                    <Label fx:id="lblSelectedDateDetails" style="-fx-font-size: 13px; -fx-text-fill: #6b7280;" wrapText="true" />
                </VBox>
                <HBox spacing="10" alignment="CENTER_LEFT">
                    <Button text="Masquer" onAction="#hideSelectedDateInfo" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" />
                </HBox>
            </VBox>
            
            <!-- Quick Stats Panel -->
            <HBox spacing="20" alignment="CENTER">
                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;" alignment="CENTER">
                    <Label text="📊 Ce Mois" style="-fx-font-size: 13px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="monthlyStatsLabel" text="0 locations" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                </VBox>
                
                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;" alignment="CENTER">
                    <Label text="💰 Revenus Mois" style="-fx-font-size: 13px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="monthlyRevenueLabel" text="0.00 €" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #059669;" />
                </VBox>
                
                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;" alignment="CENTER">
                    <Label text="📈 Taux Occupation" style="-fx-font-size: 13px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="occupancyRateLabel" text="0%" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #dc2626;" />
                </VBox>
            </HBox>
            
            <!-- Action Buttons -->
            <HBox spacing="15" alignment="CENTER">
                <Button text="🔄 Actualiser" onAction="#handleRefresh" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
                <Button text="📊 Voir Historique" onAction="#handleViewHistory" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
                <Button text="❌ Fermer" onAction="#handleClose" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
            </HBox>
            
        </VBox>
    </content>
</ScrollPane>
