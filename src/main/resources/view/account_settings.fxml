<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.AccountSettingsController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%); -fx-background-radius: 10px 10px 0 0;">
         <children>
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="⚙️ Paramètres du Compte" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnSaveSettings" mnemonicParsing="false" onAction="#handleSaveSettings" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;" text="💾 Sauvegarder" />
            <Button fx:id="btnResetToDefaults" mnemonicParsing="false" onAction="#handleResetToDefaults" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;" text="🔄 Réinitialiser" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>

               <!-- General Notification Settings -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Paramètres Généraux des Notifications" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkDesktopNotifications" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="Activer les notifications de bureau" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Affiche les notifications dans la barre des tâches" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkEmailNotifications" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="Activer les notifications par email" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Envoie des emails pour les notifications importantes" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkSoundEnabled" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="Activer les sons de notification" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Joue un son lors de nouvelles notifications" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Specific Notification Types -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Types de Notifications" />
                     
                     <GridPane hgap="20.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" />
                        </columnConstraints>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                           <children>
                              <CheckBox fx:id="chkRappelRetour" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="🚗 Rappels de retour de véhicule" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Notifications avant la date de retour prévue" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                           <children>
                              <CheckBox fx:id="chkLocationConfirmee" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="✅ Locations confirmées" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Confirmation de nouvelles locations" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="1">
                           <children>
                              <CheckBox fx:id="chkLocationReserve" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="📅 Nouvelles réservations" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Notifications de nouvelles réservations" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                           <children>
                              <CheckBox fx:id="chkPaiementDu" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="💰 Paiements en attente" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Alertes pour les paiements dus" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="2">
                           <children>
                              <CheckBox fx:id="chkPaiementRecu" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="💳 Paiements reçus" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Confirmation de paiements reçus" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
                           <children>
                              <CheckBox fx:id="chkMaintenanceDue" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="🔧 Maintenance requise" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Rappels de maintenance des véhicules" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="3">
                           <children>
                              <CheckBox fx:id="chkVehiculeDisponible" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="🚙 Véhicules disponibles" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Notifications de disponibilité" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="3">
                           <children>
                              <CheckBox fx:id="chkWelcomeAdmin" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="👋 Messages de bienvenue" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Messages d'accueil administrateur" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="4">
                           <children>
                              <CheckBox fx:id="chkSystemAlert" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="⚠️ Alertes système" />
                              <Label style="-fx-text-fill: #6c757d; -fx-font-size: 11px; -fx-padding: 0 0 0 25px;" text="Alertes importantes du système" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        <rowConstraints>
                           <RowConstraints />
                           <RowConstraints />
                           <RowConstraints />
                           <RowConstraints />
                           <RowConstraints />
                        </rowConstraints>
                     </GridPane>
                  </children>
               </VBox>

               <!-- Timing Settings -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Paramètres de Timing" />
                     
                     <GridPane hgap="20.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" />
                        </columnConstraints>
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Rappel de retour (jours avant):" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Spinner fx:id="spnRappelRetourDays" editable="true" initialValue="1" max="30" min="0" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Nombre de jours avant la date de retour pour envoyer le rappel" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Maintenance (jours avant):" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Spinner fx:id="spnMaintenanceDays" editable="true" initialValue="7" max="90" min="1" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Nombre de jours avant la maintenance pour envoyer le rappel" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Contrat expirant (jours avant):" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Spinner fx:id="spnContractExpiringDays" editable="true" initialValue="30" max="365" min="1" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Nombre de jours avant l'expiration du contrat" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <rowConstraints>
                           <RowConstraints />
                           <RowConstraints />
                           <RowConstraints />
                        </rowConstraints>
                     </GridPane>
                  </children>
               </VBox>

               <!-- Quiet Hours -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Heures de Silence" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <CheckBox fx:id="chkRespectQuietHours" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="Respecter les heures de silence" />
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Début:" />
                                 <Spinner fx:id="spnQuietHoursStart" editable="true" initialValue="22" max="23" min="0" prefWidth="80.0" />
                                 <Label text=":" />
                                 <Spinner fx:id="spnQuietMinutesStart" editable="true" initialValue="0" max="59" min="0" prefWidth="80.0" />
                                 
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057; -fx-padding: 0 0 0 20px;" text="Fin:" />
                                 <Spinner fx:id="spnQuietHoursEnd" editable="true" initialValue="8" max="23" min="0" prefWidth="80.0" />
                                 <Label text=":" />
                                 <Spinner fx:id="spnQuietMinutesEnd" editable="true" initialValue="0" max="59" min="0" prefWidth="80.0" />
                                 
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Aucune notification ne sera affichée pendant ces heures" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Display Settings -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Paramètres d'Affichage" />
                     
                     <GridPane hgap="20.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" />
                        </columnConstraints>
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Nombre max de notifications à afficher:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Spinner fx:id="spnMaxNotifications" editable="true" initialValue="50" max="200" min="10" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="Limite le nombre de notifications affichées dans la liste" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        
                        <CheckBox fx:id="chkAutoMarkAsRead" mnemonicParsing="false" selected="true" style="-fx-font-size: 14px;" text="Marquer automatiquement comme lu après:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Spinner fx:id="spnAutoMarkDays" editable="true" initialValue="30" max="365" min="1" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <Label style="-fx-text-fill: #6c757d; -fx-font-size: 12px;" text="jours (pour éviter l'accumulation de notifications)" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <rowConstraints>
                           <RowConstraints />
                           <RowConstraints />
                        </rowConstraints>
                     </GridPane>
                  </children>
               </VBox>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
