<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.ProfileViewController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%); -fx-background-radius: 10px 10px 0 0;">
         <children>
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="👤 Mon Profil" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnEditProfile" mnemonicParsing="false" onAction="#handleEditProfile"
                    style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;"
                    text="✏️ Modifier" />
            <Button fx:id="btnAccountSettings" mnemonicParsing="false" onAction="#handleAccountSettings"
                    style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;"
                    text="⚙️ Paramètres" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               
               <!-- Profile Card -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Informations Personnelles" />

                     <GridPane hgap="20.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                        </columnConstraints>

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Nom d'utilisateur:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="lblUsername" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Rôle:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <Label fx:id="lblRole" style="-fx-background-color: #667eea; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;" text="-" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Prénom:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="lblFirstName" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Nom:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <Label fx:id="lblLastName" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="lblEmail" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Téléphone:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <Label fx:id="lblPhone" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="3" GridPane.rowIndex="2" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Statut:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="lblStatus" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Dernière connexion:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                        <Label fx:id="lblLastLogin" style="-fx-text-fill: #212529; -fx-font-size: 14px;" text="-" GridPane.columnIndex="3" GridPane.rowIndex="3" />
                     </GridPane>
                  </children>
               </VBox>

               <!-- Account Statistics -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Statistiques du Compte" />

                     <HBox spacing="20.0">
                        <children>
                           <VBox alignment="CENTER" style="-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px; -fx-min-width: 120px;">
                              <children>
                                 <Label fx:id="lblAccountAge" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #667eea;" text="0" />
                                 <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-text-alignment: center;" text="Jours depuis création" />
                              </children>
                           </VBox>

                           <VBox alignment="CENTER" style="-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px; -fx-min-width: 120px;">
                              <children>
                                 <Label fx:id="lblTotalLogins" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="0" />
                                 <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-text-alignment: center;" text="Connexions totales" />
                              </children>
                           </VBox>

                           <VBox alignment="CENTER" style="-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px; -fx-min-width: 120px;">
                              <children>
                                 <Label fx:id="lblNotificationsReceived" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #ffc107;" text="0" />
                                 <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-text-alignment: center;" text="Notifications reçues" />
                              </children>
                           </VBox>

                           <VBox alignment="CENTER" style="-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px; -fx-min-width: 120px;">
                              <children>
                                 <Label fx:id="lblActionsPerformed" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #dc3545;" text="0" />
                                 <Label style="-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-text-alignment: center;" text="Actions effectuées" />
                              </children>
                           </VBox>
                        </children>
                     </HBox>
                  </children>
               </VBox>

               <!-- Security Settings -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Sécurité" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Mot de passe:" />
                                 <Label styleClass="field-value" text="••••••••" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Button fx:id="btnChangePassword" mnemonicParsing="false" onAction="#handleChangePassword" styleClass="btn-secondary" text="Changer le mot de passe" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Authentification à deux facteurs:" />
                                 <Label fx:id="lblTwoFactorStatus" styleClass="field-value" text="Désactivée" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Button fx:id="btnToggleTwoFactor" mnemonicParsing="false" onAction="#handleToggleTwoFactor" styleClass="btn-secondary" text="Activer" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Recent Activity -->
               <VBox styleClass="profile-card">
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <children>
                           <Label styleClass="section-title" text="Activité Récente" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button fx:id="btnViewAllActivity" mnemonicParsing="false" onAction="#handleViewAllActivity" styleClass="btn-link" text="Voir tout" />
                        </children>
                     </HBox>
                     
                     <VBox fx:id="activityContainer" spacing="10.0">
                        <!-- Activity items will be added dynamically -->
                     </VBox>
                  </children>
               </VBox>

               <!-- Preferences -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Préférences" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Langue:" />
                                 <ComboBox fx:id="cmbLanguage" prefWidth="150.0" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Thème:" />
                                 <ComboBox fx:id="cmbTheme" prefWidth="150.0" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkAutoSave" mnemonicParsing="false" text="Sauvegarde automatique" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkShowTips" mnemonicParsing="false" text="Afficher les conseils au démarrage" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Action Buttons -->
               <HBox alignment="CENTER" spacing="15.0" styleClass="action-buttons">
                  <children>
                     <Button fx:id="btnSavePreferences" mnemonicParsing="false" onAction="#handleSavePreferences" styleClass="btn-primary" text="💾 Sauvegarder les préférences" />
                     <Button fx:id="btnExportData" mnemonicParsing="false" onAction="#handleExportData" styleClass="btn-secondary" text="📤 Exporter mes données" />
                     <Button fx:id="btnDeleteAccount" mnemonicParsing="false" onAction="#handleDeleteAccount" styleClass="btn-danger" text="🗑️ Supprimer le compte" />
                  </children>
               </HBox>
            </VBox>
         </content>
      </ScrollPane>
   </children>

</VBox>
