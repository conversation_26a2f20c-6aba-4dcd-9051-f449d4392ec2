<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.ProfileViewController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="profile-header">
         <children>
            <Label styleClass="page-title" text="👤 Mon Profil" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnEditProfile" mnemonicParsing="false" onAction="#handleEditProfile" styleClass="btn-primary" text="✏️ Modifier" />
            <Button fx:id="btnAccountSettings" mnemonicParsing="false" onAction="#handleAccountSettings" styleClass="btn-secondary" text="⚙️ Paramètres" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               
               <!-- Profile Card -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Informations Personnelles" />
                     
                     <GridPane hgap="20.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                        </columnConstraints>
                        
                        <Label styleClass="field-label" text="Nom d'utilisateur:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="lblUsername" styleClass="field-value" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label styleClass="field-label" text="Rôle:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <Label fx:id="lblRole" styleClass="field-value role-badge" text="-" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <Label styleClass="field-label" text="Prénom:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="lblFirstName" styleClass="field-value" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label styleClass="field-label" text="Nom:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <Label fx:id="lblLastName" styleClass="field-value" text="-" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                        
                        <Label styleClass="field-label" text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="lblEmail" styleClass="field-value" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label styleClass="field-label" text="Téléphone:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <Label fx:id="lblPhone" styleClass="field-value" text="-" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                        
                        <Label styleClass="field-label" text="Statut:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="lblStatus" styleClass="field-value status-badge" text="-" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label styleClass="field-label" text="Dernière connexion:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                        <Label fx:id="lblLastLogin" styleClass="field-value" text="-" GridPane.columnIndex="3" GridPane.rowIndex="3" />
                     </GridPane>
                  </children>
               </VBox>

               <!-- Account Statistics -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Statistiques du Compte" />
                     
                     <HBox spacing="20.0">
                        <children>
                           <VBox alignment="CENTER" styleClass="stat-item">
                              <children>
                                 <Label fx:id="lblAccountAge" styleClass="stat-number" text="0" />
                                 <Label styleClass="stat-label" text="Jours depuis création" />
                              </children>
                           </VBox>
                           
                           <VBox alignment="CENTER" styleClass="stat-item">
                              <children>
                                 <Label fx:id="lblTotalLogins" styleClass="stat-number" text="0" />
                                 <Label styleClass="stat-label" text="Connexions totales" />
                              </children>
                           </VBox>
                           
                           <VBox alignment="CENTER" styleClass="stat-item">
                              <children>
                                 <Label fx:id="lblNotificationsReceived" styleClass="stat-number" text="0" />
                                 <Label styleClass="stat-label" text="Notifications reçues" />
                              </children>
                           </VBox>
                           
                           <VBox alignment="CENTER" styleClass="stat-item">
                              <children>
                                 <Label fx:id="lblActionsPerformed" styleClass="stat-number" text="0" />
                                 <Label styleClass="stat-label" text="Actions effectuées" />
                              </children>
                           </VBox>
                        </children>
                     </HBox>
                  </children>
               </VBox>

               <!-- Security Settings -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Sécurité" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Mot de passe:" />
                                 <Label styleClass="field-value" text="••••••••" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Button fx:id="btnChangePassword" mnemonicParsing="false" onAction="#handleChangePassword" styleClass="btn-secondary" text="Changer le mot de passe" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Authentification à deux facteurs:" />
                                 <Label fx:id="lblTwoFactorStatus" styleClass="field-value" text="Désactivée" />
                                 <Region HBox.hgrow="ALWAYS" />
                                 <Button fx:id="btnToggleTwoFactor" mnemonicParsing="false" onAction="#handleToggleTwoFactor" styleClass="btn-secondary" text="Activer" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Recent Activity -->
               <VBox styleClass="profile-card">
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <children>
                           <Label styleClass="section-title" text="Activité Récente" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button fx:id="btnViewAllActivity" mnemonicParsing="false" onAction="#handleViewAllActivity" styleClass="btn-link" text="Voir tout" />
                        </children>
                     </HBox>
                     
                     <VBox fx:id="activityContainer" spacing="10.0">
                        <!-- Activity items will be added dynamically -->
                     </VBox>
                  </children>
               </VBox>

               <!-- Preferences -->
               <VBox styleClass="profile-card">
                  <children>
                     <Label styleClass="section-title" text="Préférences" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Langue:" />
                                 <ComboBox fx:id="cmbLanguage" prefWidth="150.0" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <Label styleClass="field-label" text="Thème:" />
                                 <ComboBox fx:id="cmbTheme" prefWidth="150.0" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkAutoSave" mnemonicParsing="false" text="Sauvegarde automatique" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="15.0">
                              <children>
                                 <CheckBox fx:id="chkShowTips" mnemonicParsing="false" text="Afficher les conseils au démarrage" />
                                 <Region HBox.hgrow="ALWAYS" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Action Buttons -->
               <HBox alignment="CENTER" spacing="15.0" styleClass="action-buttons">
                  <children>
                     <Button fx:id="btnSavePreferences" mnemonicParsing="false" onAction="#handleSavePreferences" styleClass="btn-primary" text="💾 Sauvegarder les préférences" />
                     <Button fx:id="btnExportData" mnemonicParsing="false" onAction="#handleExportData" styleClass="btn-secondary" text="📤 Exporter mes données" />
                     <Button fx:id="btnDeleteAccount" mnemonicParsing="false" onAction="#handleDeleteAccount" styleClass="btn-danger" text="🗑️ Supprimer le compte" />
                  </children>
               </HBox>
            </VBox>
         </content>
      </ScrollPane>
   </children>

   <stylesheets>
      <URL value="@../css/profile-view.css" />
   </stylesheets>
</VBox>
