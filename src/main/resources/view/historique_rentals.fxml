<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" prefHeight="1580.0" prefWidth="942.0" style="-fx-background: transparent; -fx-background-color: #f5f7fa;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueRentalsController">
    <content>
        <VBox minWidth="900" prefHeight="900.0" prefWidth="1064.0" spacing="28" style="-fx-background-color: white; -fx-padding: 32; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 12, 0, 0, 2);">
            <!-- Header Section -->
            <VBox spacing="12">
                <HBox alignment="CENTER_LEFT" spacing="12">
                    <ImageView fitHeight="54.0" fitWidth="54.0" preserveRatio="true">
                        <image>
                            <Image url="@../rental-history-icon.png" />
                        </image>
                    </ImageView>
                    <Label style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="Historique des Locations" />
                </HBox>
                <Separator style="-fx-border-color: #e2e8f0; -fx-border-width: 1;" />
            </VBox>

            <!-- Filter Section -->
            <VBox spacing="16" style="-fx-background-color: #f8fafc; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;">
                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #4a5568;" text="FILTRES" />

                <GridPane hgap="16" vgap="12">
                    <columnConstraints>
                        <ColumnConstraints hgrow="ALWAYS" />
                        <ColumnConstraints hgrow="ALWAYS" />
                        <ColumnConstraints hgrow="ALWAYS" />
                        <ColumnConstraints hgrow="ALWAYS" />
                    </columnConstraints>

                    <TextField fx:id="searchClientField" promptText="Client..." style="-fx-background-radius: 8; -fx-pref-height: 40;" GridPane.columnIndex="0" />
                    <TextField fx:id="searchVehiculeField" promptText="Véhicule..." style="-fx-background-radius: 8; -fx-pref-height: 40;" GridPane.columnIndex="1" />
                    <DatePicker fx:id="dateDebutFilter" promptText="Date début" style="-fx-background-radius: 8; -fx-pref-height: 40;" GridPane.columnIndex="2" />
                    <DatePicker fx:id="dateFinFilter" promptText="Date fin" style="-fx-background-radius: 8; -fx-pref-height: 40;" GridPane.columnIndex="3" />
               <rowConstraints>
                  <RowConstraints />
               </rowConstraints>
                </GridPane>

                <HBox alignment="CENTER_RIGHT" spacing="12">
                    <Button onAction="#handleFilter" style="-fx-background-color: #4299e1; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 20; -fx-font-weight: bold; -fx-cursor: hand;" text="Filtrer" />
                    <Button onAction="#handleClearFilters" style="-fx-background-color: #a0aec0; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 20; -fx-font-weight: bold; -fx-cursor: hand;" text="Effacer" />
                </HBox>
            </VBox>

            <!-- Table Section -->
            <VBox spacing="12" style="-fx-min-height: 400;">
                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #4a5568;" text="LISTE DES LOCATIONS" />

                <TableView fx:id="rentalHistoryTable" style="-fx-background-color: transparent; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-border-width: 1;">
                    <columns>
                        <TableColumn fx:id="colId" style="-fx-alignment: CENTER; -fx-font-size: 13px; -fx-font-weight: bold;" text="ID" />
                        <TableColumn fx:id="colClient" style="-fx-font-size: 13px; -fx-font-weight: bold;" text="Client" />
                        <TableColumn fx:id="colVehicule" style="-fx-font-size: 13px; -fx-font-weight: bold;" text="Véhicule" />
                        <TableColumn fx:id="colDateDebut" style="-fx-font-size: 13px; -fx-font-weight: bold;" text="Date Début" />
                        <TableColumn fx:id="colDateFin" style="-fx-font-size: 13px; -fx-font-weight: bold;" text="Date Fin" />
                        <TableColumn fx:id="colStatut" style="-fx-alignment: CENTER; -fx-font-size: 13px; -fx-font-weight: bold;" text="Statut" />
                        <TableColumn fx:id="colPrix" style="-fx-alignment: CENTER_RIGHT; -fx-font-size: 13px; -fx-font-weight: bold;" text="Prix Total" />
                    </columns>

                    <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                    </columnResizePolicy>

                    <style>
                        -fx-table-cell-border-color: transparent;
                        -fx-selection-bar: #ebf8ff;
                        -fx-selection-bar-non-focused: #ebf8ff;
                    </style>
                </TableView>
            </VBox>

            <!-- Details Section -->
            <VBox spacing="8" style="-fx-background-color: #f8fafc; -fx-background-radius: 12; -fx-padding: 16; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;">
                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #4a5568;" text="DÉTAILS DE LA LOCATION" />
                <Label fx:id="detailsLabel" style="-fx-font-size: 14px; -fx-text-fill: #4a5568; -fx-wrap-text: true;" text="Sélectionnez une location pour voir les détails." />
            </VBox>

            <!-- Footer Padding -->
            <padding><Insets bottom="40" /></padding>
        </VBox>
    </content>
</ScrollPane>
