<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueLocationsEnhancedController">
   <top>
      <VBox style="-fx-background-color: linear-gradient(to right, #3b82f6, #1d4ed8); -fx-padding: 25;">
         <HBox alignment="CENTER_LEFT" spacing="20">
            <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: bold;" text="📊 Historique Complet des Locations" />
            <Region HBox.hgrow="ALWAYS" />
            <MenuButton style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-weight: bold;" text="📊 Exporter">
               <items>
                  <MenuItem fx:id="exportLocationsCSV" onAction="#handleExportCSV" text="📄 Export CSV" />
                  <MenuItem fx:id="exportLocationsExcel" onAction="#handleExportExcel" text="📊 Export Excel" />
                  <MenuItem fx:id="exportLocationsPDF" onAction="#handleExportPDF" text="📋 Rapport PDF" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="exportActive" onAction="#handleExportActive" text="🔄 Locations Actives" />
                  <MenuItem fx:id="exportCompleted" onAction="#handleExportCompleted" text="✅ Terminées" />
                  <MenuItem fx:id="exportProfitability" onAction="#handleExportProfitability" text="💰 Rentabilité" />
               </items>
            </MenuButton>
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
         </HBox>
         
         <!-- Advanced Filters Section -->
         <VBox spacing="12" style="-fx-padding: 15 0 0 0;">
            <!-- Primary Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="🔍 Filtres:" />
               <ComboBox fx:id="filterStatusCombo" promptText="Statut Location" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterVehicleCombo" promptText="Véhicule" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterClientCombo" promptText="Client" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 150;" />
               <DatePicker fx:id="filterDateFrom" promptText="Date début" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
               <DatePicker fx:id="filterDateTo" promptText="Date fin" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            </HBox>
            
            <!-- Advanced Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="⚙️ Avancés:" />
               <TextField fx:id="filterPriceMin" promptText="Prix min" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
               <TextField fx:id="filterPriceMax" promptText="Prix max" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
               <ComboBox fx:id="filterDurationCombo" promptText="Durée" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 120;" />
               <ComboBox fx:id="filterPaymentCombo" promptText="Paiement" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 120;" />
               <CheckBox fx:id="filterOverdueOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="En retard uniquement" />
               <CheckBox fx:id="filterProfitableOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="Rentables uniquement" />
               <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✖ Effacer" />
            </HBox>
         </VBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.75" orientation="HORIZONTAL">
         <!-- Main Content Area -->
         <VBox spacing="15" style="-fx-padding: 20;">
            <!-- Enhanced Statistics Dashboard -->
            <HBox spacing="15">
               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #dbeafe; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #3b82f6; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1d4ed8;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #1e40af; -fx-font-weight: bold;" text="📊 TOTAL LOCATIONS" />
                  <Label fx:id="lblLocationGrowth" style="-fx-font-size: 10px; -fx-text-fill: #1d4ed8;" text="↗ +0% ce mois" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #dcfce7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #16a34a; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalRevenue" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #15803d;" text="0.00 DH" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #166534; -fx-font-weight: bold;" text="💰 REVENUS TOTAL" />
                  <Label fx:id="lblRevenueGrowth" style="-fx-font-size: 10px; -fx-text-fill: #15803d;" text="↗ +0% ce mois" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #fef3c7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #f59e0b; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblActiveLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #d97706;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #92400e; -fx-font-weight: bold;" text="🔄 EN COURS" />
                  <Label fx:id="lblActiveRevenue" style="-fx-font-size: 10px; -fx-text-fill: #d97706;" text="0.00 DH en cours" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #f3e8ff; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #8b5cf6; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblAvgDuration" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #7c3aed;" text="0j" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #6d28d9; -fx-font-weight: bold;" text="📅 DURÉE MOYENNE" />
                  <Label fx:id="lblAvgPrice" style="-fx-font-size: 10px; -fx-text-fill: #7c3aed;" text="0.00 DH/jour moy." />
               </VBox>
            </HBox>
            
            <!-- Performance Analytics -->
            <HBox spacing="15">
               <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="🚗 Top Véhicules" />
                  <VBox fx:id="topVehiclesChart" spacing="8" />
               </VBox>
               
               <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="👥 Top Clients" />
                  <VBox fx:id="topClientsChart" spacing="8" />
               </VBox>
               
               <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📈 Tendances" />
                  <VBox fx:id="trendsChart" spacing="8" />
               </VBox>
            </HBox>
            
            <!-- Enhanced Locations Table -->
            <VBox spacing="12" VBox.vgrow="ALWAYS">
               <HBox alignment="CENTER_LEFT" spacing="15">
                  <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📋 Historique des Locations" />
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="searchField" onKeyReleased="#handleSearch" promptText="🔍 Rechercher client, véhicule..." style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-min-width: 200;" />
                  <Button fx:id="btnBulkActions" onAction="#handleBulkActions" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16;" text="⚡ Actions Groupées" />
               </HBox>
               
               <TableView fx:id="locationsTable" prefHeight="350.0" style="-fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12;" VBox.vgrow="ALWAYS">
                  <columns>
                     <TableColumn fx:id="colSelect" prefWidth="40.0" text="☑" />
                     <TableColumn fx:id="colId" prefWidth="60.0" text="ID" />
                     <TableColumn fx:id="colClient" prefWidth="130.0" text="Client" />
                     <TableColumn fx:id="colVehicle" prefWidth="120.0" text="Véhicule" />
                     <TableColumn fx:id="colStartDate" prefWidth="85.0" text="Début" />
                     <TableColumn fx:id="colEndDate" prefWidth="85.0" text="Fin Prévue" />
                     <TableColumn fx:id="colActualEnd" prefWidth="85.0" text="Fin Réelle" />
                     <TableColumn fx:id="colDuration" prefWidth="70.0" text="Durée" />
                     <TableColumn fx:id="colStatus" prefWidth="80.0" text="Statut" />
                     <TableColumn fx:id="colTotal" prefWidth="80.0" text="Total" />
                     <TableColumn fx:id="colPayment" prefWidth="80.0" text="Paiement" />
                     <TableColumn fx:id="colProfit" prefWidth="70.0" text="Profit" />
                     <TableColumn fx:id="colActions" prefWidth="140.0" text="Actions" />
                  </columns>
               </TableView>
            </VBox>
         </VBox>
         
         <!-- Location Details Sidebar -->
         <VBox spacing="15" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 0 1;">
            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📋 Détails de la Location" />
            
            <!-- Location Detail Card -->
            <VBox fx:id="locationDetailCard" spacing="12" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
               <Label fx:id="lblSelectedLocation" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Sélectionnez une location" />
               <VBox fx:id="locationDetails" spacing="8" />
            </VBox>
            
            <!-- Quick Actions -->
            <VBox spacing="10">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="⚡ Actions Rapides" />
               <VBox spacing="8">
                  <Button fx:id="btnViewContract" onAction="#handleViewContract" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="📋 Voir Contrat" maxWidth="Infinity" />
                  <Button fx:id="btnEditLocation" onAction="#handleEditLocation" style="-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="✏️ Modifier" maxWidth="Infinity" />
                  <Button fx:id="btnViewPayments" onAction="#handleViewPayments" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="💳 Voir Paiements" maxWidth="Infinity" />
                  <Button fx:id="btnContactClient" onAction="#handleContactClient" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="📞 Contacter Client" maxWidth="Infinity" />
                  <Button fx:id="btnDuplicateLocation" onAction="#handleDuplicateLocation" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="📋 Dupliquer" maxWidth="Infinity" />
               </VBox>
            </VBox>
            
            <!-- Location Timeline -->
            <VBox spacing="10">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📅 Chronologie" />
               <ScrollPane prefHeight="200" style="-fx-background-color: white; -fx-background-radius: 8; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-border-width: 1;">
                  <VBox fx:id="locationTimeline" spacing="8" style="-fx-padding: 12;" />
               </ScrollPane>
            </VBox>
         </VBox>
      </SplitPane>
   </center>
   
   <bottom>
      <VBox style="-fx-background-color: #f8fafc; -fx-border-color: #e5e7eb; -fx-border-width: 1 0 0 0;">
         <!-- Statistics Summary -->
         <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-padding: 12 20;">
            <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-weight: bold;" text="Total: 0 locations" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblSelectedCount" style="-fx-font-size: 12px; -fx-text-fill: #3b82f6;" text="Sélectionnées: 0" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblFilteredRevenue" style="-fx-font-size: 12px; -fx-text-fill: #059669; -fx-font-weight: bold;" text="Revenus filtrés: 0.00 DH" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblProfitMargin" style="-fx-font-size: 12px; -fx-text-fill: #dc2626; -fx-font-weight: bold;" text="Marge: 0%" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblLastUpdate" style="-fx-font-size: 11px; -fx-text-fill: #9ca3af;" text="Dernière mise à jour: --:--" />
         </HBox>
         
         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="12" style="-fx-padding: 12 20;">
            <Button fx:id="btnNewLocation" onAction="#handleNewLocation" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="➕ Nouvelle Location" />
            <Button fx:id="btnAnalytics" onAction="#handleAnalytics" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="📊 Analyses Avancées" />
            <Region HBox.hgrow="ALWAYS" />

         </HBox>
      </VBox>
   </bottom>
</BorderPane>
