<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<StackPane fx:controller="controller.ClientDetailController" style="-fx-background-color: rgba(0,0,0,0.15);" xmlns:fx="http://javafx.com/fxml/1">
    <VBox fx:id="root" alignment="TOP_CENTER" spacing="0" style="-fx-padding: 30;">
        <VBox spacing="0" style="-fx-background-color: white; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 20, 0, 0, 6);">
            <!-- Header -->
            <HBox style="-fx-padding: 20 24 16 24; -fx-background-color: #f8fafc; -fx-background-radius: 16 16 0 0;">
                <Label text="Détails du Client" style="-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;" />
                <Region HBox.hgrow="ALWAYS" />
                <Button text="✖" onAction="#closeWindow" style="-fx-background-color: transparent; -fx-text-fill: #dc2626; -fx-font-size: 20px; -fx-font-weight: bold; -fx-cursor: hand;" />
            </HBox>

            <!-- Main Content -->
            <HBox spacing="0" style="-fx-padding: 0;">
                <!-- Left Panel - Documents -->
                <VBox style="-fx-background-color: #f9fafb; -fx-padding: 24; -fx-spacing: 16; -fx-min-width: 360;">
                    <Label text="Documents" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" />

                    <VBox spacing="24" style="-fx-padding: 8;">
                        <!-- Permis Recto -->
                        <VBox spacing="6">
                            <Label text="Permis de Conduire (Recto)" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" />
                            <VBox style="-fx-background-color: #ffffff; -fx-background-radius: 12; -fx-border-radius: 12; -fx-border-color: #e5e7eb; -fx-border-width: 1; -fx-padding: 12;">
                                <ImageView fx:id="imgPermisRecto" fitWidth="280" fitHeight="160" style="-fx-background-color: #f3f4f6; -fx-background-radius: 8; -fx-cursor: hand;" onMouseClicked="#handleZoomPermisRecto" />
                                <Button text="Télécharger" onAction="#handleDownloadPermisRecto" style="-fx-background-color: transparent; -fx-text-fill: #3b82f6; -fx-font-size: 13px; -fx-underline: true; -fx-cursor: hand; -fx-padding: 8 0 0 0;" />
                            </VBox>
                        </VBox>

                        <!-- Permis Verso -->
                        <VBox spacing="6">
                            <Label text="Permis de Conduire (Verso)" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" />
                            <VBox style="-fx-background-color: #ffffff; -fx-background-radius: 12; -fx-border-radius: 12; -fx-border-color: #e5e7eb; -fx-border-width: 1; -fx-padding: 12;">
                                <ImageView fx:id="imgPermisVerso" fitWidth="280" fitHeight="160" style="-fx-background-color: #f3f4f6; -fx-background-radius: 8; -fx-cursor: hand;" onMouseClicked="#handleZoomPermisVerso" />
                                <Button text="Télécharger" onAction="#handleDownloadPermisVerso" style="-fx-background-color: transparent; -fx-text-fill: #3b82f6; -fx-font-size: 13px; -fx-underline: true; -fx-cursor: hand; -fx-padding: 8 0 0 0;" />
                            </VBox>
                        </VBox>
                    </VBox>
                </VBox>

                <!-- Right Panel - Client Info -->
                <VBox style="-fx-padding: 24; -fx-spacing: 16; -fx-min-width: 360;">
                    <GridPane hgap="16" vgap="12" style="-fx-padding: 8;">
                        <Label text="Informations Personnelles" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a; GridPane.columnSpan=2;" GridPane.rowIndex="0" />

                        <Label text="Nom:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                        <Label fx:id="lblNom" style="-fx-font-size: 14px;" GridPane.rowIndex="1" GridPane.columnIndex="1" />

                        <Label text="Prénom:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                        <Label fx:id="lblPrenom" style="-fx-font-size: 14px;" GridPane.rowIndex="2" GridPane.columnIndex="1" />

                        <Label text="CIN:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="3" GridPane.columnIndex="0" />
                        <Label fx:id="lblCin" style="-fx-font-size: 14px;" GridPane.rowIndex="3" GridPane.columnIndex="1" />

                        <Label text="Téléphone:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="4" GridPane.columnIndex="0" />
                        <Label fx:id="lblTelephone" style="-fx-font-size: 14px;" GridPane.rowIndex="4" GridPane.columnIndex="1" />

                        <Label text="Email:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="5" GridPane.columnIndex="0" />
                        <Label fx:id="lblEmail" style="-fx-font-size: 14px;" GridPane.rowIndex="5" GridPane.columnIndex="1" />

                        <Label text="Permis:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="6" GridPane.columnIndex="0" />
                        <Label fx:id="lblPermis" style="-fx-font-size: 14px;" GridPane.rowIndex="6" GridPane.columnIndex="1" />

                        <Label text="Adresse:" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" GridPane.rowIndex="7" GridPane.columnIndex="0" />
                        <Label fx:id="lblAdresse" style="-fx-font-size: 14px;" GridPane.rowIndex="7" GridPane.columnIndex="1" />
                    </GridPane>

                    <!-- CIN Documents Section -->
                    <VBox spacing="16" style="-fx-padding: 16 8 8 8;">
                        <Label text="Carte d'Identité Nationale" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" />

                        <HBox spacing="24" alignment="CENTER">
                            <!-- CIN Recto -->
                            <VBox spacing="6">
                                <Label text="Recto" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" />
                                <VBox style="-fx-background-color: #ffffff; -fx-background-radius: 12; -fx-border-radius: 12; -fx-border-color: #e5e7eb; -fx-border-width: 1; -fx-padding: 12;">
                                    <ImageView fx:id="imgCinRecto" fitWidth="160" fitHeight="100" style="-fx-background-color: #f3f4f6; -fx-background-radius: 8; -fx-cursor: hand;" onMouseClicked="#handleZoomCinRecto" />
                                    <Button text="Télécharger" onAction="#handleDownloadCinRecto" style="-fx-background-color: transparent; -fx-text-fill: #3b82f6; -fx-font-size: 13px; -fx-underline: true; -fx-cursor: hand; -fx-padding: 8 0 0 0;" />
                                </VBox>
                            </VBox>

                            <!-- CIN Verso -->
                            <VBox spacing="6">
                                <Label text="Verso" style="-fx-font-weight: bold; -fx-text-fill: #4b5563;" />
                                <VBox style="-fx-background-color: #ffffff; -fx-background-radius: 12; -fx-border-radius: 12; -fx-border-color: #e5e7eb; -fx-border-width: 1; -fx-padding: 12;">
                                    <ImageView fx:id="imgCinVerso" fitWidth="160" fitHeight="100" style="-fx-background-color: #f3f4f6; -fx-background-radius: 8; -fx-cursor: hand;" onMouseClicked="#handleZoomCinVerso" />
                                    <Button text="Télécharger" onAction="#handleDownloadCinVerso" style="-fx-background-color: transparent; -fx-text-fill: #3b82f6; -fx-font-size: 13px; -fx-underline: true; -fx-cursor: hand; -fx-padding: 8 0 0 0;" />
                                </VBox>
                            </VBox>
                        </HBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </VBox>
</StackPane>