<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HelpSupportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%); -fx-background-radius: 10px 10px 0 0;">
         <children>
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="❓ Aide et Support" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnContactSupport" mnemonicParsing="false" onAction="#handleContactSupport" 
                    style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 8px 16px; -fx-font-weight: 500; -fx-cursor: hand;" 
                    text="📞 Contacter le Support" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>

               <!-- Quick Help -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Aide Rapide" />
                     
                     <HBox spacing="15.0">
                        <children>
                           <Button fx:id="btnUserGuide" mnemonicParsing="false" onAction="#handleUserGuide" 
                                   style="-fx-background-color: #667eea; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 15px; -fx-cursor: hand;" 
                                   text="📖 Guide Utilisateur" />
                           <Button fx:id="btnVideoTutorials" mnemonicParsing="false" onAction="#handleVideoTutorials" 
                                   style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 15px; -fx-cursor: hand;" 
                                   text="🎥 Tutoriels Vidéo" />
                           <Button fx:id="btnFAQ" mnemonicParsing="false" onAction="#handleFAQ" 
                                   style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 15px; -fx-cursor: hand;" 
                                   text="❓ FAQ" />
                           <Button fx:id="btnKeyboardShortcuts" mnemonicParsing="false" onAction="#handleKeyboardShortcuts" 
                                   style="-fx-background-color: #ffc107; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 15px; -fx-cursor: hand;" 
                                   text="⌨️ Raccourcis Clavier" />
                        </children>
                     </HBox>
                  </children>
               </VBox>

               <!-- System Information -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Informations Système" />
                     
                     <GridPane hgap="20.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                        </columnConstraints>
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Version de l'application:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="lblAppVersion" style="-fx-text-fill: #212529;" text="LocationV12 v1.0.0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Version Java:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="lblJavaVersion" style="-fx-text-fill: #212529;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Système d'exploitation:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="lblOSVersion" style="-fx-text-fill: #212529;" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Base de données:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="lblDatabaseStatus" style="-fx-text-fill: #28a745;" text="Connectée" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </GridPane>
                  </children>
               </VBox>

               <!-- Contact Information -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Informations de Contact" />
                     
                     <VBox spacing="10.0">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="10.0">
                              <children>
                                 <Label style="-fx-font-size: 16px;" text="📧" />
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Email Support:" />
                                 <Label style="-fx-text-fill: #667eea; -fx-cursor: hand;" text="<EMAIL>" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="10.0">
                              <children>
                                 <Label style="-fx-font-size: 16px;" text="📞" />
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Téléphone:" />
                                 <Label style="-fx-text-fill: #212529;" text="+212 5XX-XXX-XXX" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="10.0">
                              <children>
                                 <Label style="-fx-font-size: 16px;" text="🌐" />
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Site Web:" />
                                 <Label style="-fx-text-fill: #667eea; -fx-cursor: hand;" text="www.locationv12.com" />
                              </children>
                           </HBox>
                           
                           <HBox alignment="CENTER_LEFT" spacing="10.0">
                              <children>
                                 <Label style="-fx-font-size: 16px;" text="⏰" />
                                 <Label style="-fx-font-weight: 600; -fx-text-fill: #495057;" text="Heures de support:" />
                                 <Label style="-fx-text-fill: #212529;" text="Lundi - Vendredi: 9h00 - 18h00" />
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Troubleshooting -->
               <VBox style="-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 1px; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #212529; -fx-padding: 0 0 15px 0;" text="Dépannage Rapide" />
                     
                     <VBox spacing="15.0">
                        <children>
                           <VBox spacing="5.0" style="-fx-border-color: #e9ecef; -fx-border-width: 0 0 1px 0; -fx-padding: 0 0 10px 0;">
                              <children>
                                 <Label style="-fx-font-weight: bold; -fx-text-fill: #495057;" text="🔄 L'application est lente" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-wrap-text: true;" text="• Redémarrez l'application\n• Vérifiez votre connexion internet\n• Contactez le support si le problème persiste" />
                              </children>
                           </VBox>
                           
                           <VBox spacing="5.0" style="-fx-border-color: #e9ecef; -fx-border-width: 0 0 1px 0; -fx-padding: 0 0 10px 0;">
                              <children>
                                 <Label style="-fx-font-weight: bold; -fx-text-fill: #495057;" text="🔐 Problèmes de connexion" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-wrap-text: true;" text="• Vérifiez vos identifiants\n• Assurez-vous que votre compte est actif\n• Contactez l'administrateur si nécessaire" />
                              </children>
                           </VBox>
                           
                           <VBox spacing="5.0" style="-fx-border-color: #e9ecef; -fx-border-width: 0 0 1px 0; -fx-padding: 0 0 10px 0;">
                              <children>
                                 <Label style="-fx-font-weight: bold; -fx-text-fill: #495057;" text="🔔 Notifications ne s'affichent pas" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-wrap-text: true;" text="• Vérifiez les paramètres de notification\n• Assurez-vous que les notifications de bureau sont activées\n• Redémarrez l'application" />
                              </children>
                           </VBox>
                           
                           <VBox spacing="5.0">
                              <children>
                                 <Label style="-fx-font-weight: bold; -fx-text-fill: #495057;" text="💾 Problèmes de sauvegarde" />
                                 <Label style="-fx-text-fill: #6c757d; -fx-wrap-text: true;" text="• Vérifiez la connexion à la base de données\n• Assurez-vous d'avoir les permissions nécessaires\n• Contactez l'administrateur système" />
                              </children>
                           </VBox>
                        </children>
                     </VBox>
                  </children>
               </VBox>

               <!-- Actions -->
               <HBox alignment="CENTER" spacing="15.0" style="-fx-padding: 20px;">
                  <children>
                     <Button fx:id="btnGenerateReport" mnemonicParsing="false" onAction="#handleGenerateReport" 
                             style="-fx-background-color: #667eea; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 20px; -fx-font-weight: 500; -fx-cursor: hand;" 
                             text="📊 Générer Rapport de Diagnostic" />
                     <Button fx:id="btnCheckUpdates" mnemonicParsing="false" onAction="#handleCheckUpdates" 
                             style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 20px; -fx-font-weight: 500; -fx-cursor: hand;" 
                             text="🔄 Vérifier les Mises à Jour" />
                     <Button fx:id="btnResetSettings" mnemonicParsing="false" onAction="#handleResetSettings" 
                             style="-fx-background-color: #ffc107; -fx-text-fill: white; -fx-border-radius: 6px; -fx-background-radius: 6px; -fx-padding: 10px 20px; -fx-font-weight: 500; -fx-cursor: hand;" 
                             text="⚙️ Réinitialiser Paramètres" />
                  </children>
               </HBox>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
