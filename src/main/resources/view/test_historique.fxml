<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: #f8fafc; -fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueController">
    <content>
        <VBox spacing="25.0" style="-fx-background-color: #f8fafc; -fx-padding: 30;" prefHeight="1200">
            <padding><Insets bottom="50.0" /></padding>

            <!-- Header Section -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="8">
                        <Label text="📊 Historique Détaillé" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                        <Label fx:id="vehicleInfoLabel" text="Chargement des informations du véhicule..." style="-fx-font-size: 16px; -fx-text-fill: #64748b;" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <VBox spacing="10" alignment="CENTER_RIGHT">
                        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600;" text="📊 Exporter" />
                        <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600;" text="🔄 Actualiser" />
                    </VBox>
                </HBox>
            </VBox>

            <!-- Statistics Cards -->
            <HBox spacing="20" alignment="CENTER">
                <VBox spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;" alignment="CENTER">
                    <Label text="📈 Total Locations" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="totalLocationsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                    <Label fx:id="totalLocationsSubLabel" text="locations enregistrées" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" />
                </VBox>

                <VBox spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;" alignment="CENTER">
                    <Label text="💰 Revenus Total" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="totalRevenueLabel" text="0.00 €" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #059669;" />
                    <Label fx:id="avgRevenueLabel" text="Moyenne: 0.00 €" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" />
                </VBox>

                <VBox spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;" alignment="CENTER">
                    <Label text="📅 Jours Loués" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="totalDaysLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #d97706;" />
                    <Label fx:id="avgDaysLabel" text="Moyenne: 0 jours" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" />
                </VBox>

                <VBox spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;" alignment="CENTER">
                    <Label text="⭐ Taux Utilisation" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="utilizationRateLabel" text="0%" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #8b5cf6;" />
                    <Label fx:id="statusLabel" text="Statut: Disponible" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" />
                </VBox>
            </HBox>

            <!-- Performance Indicators -->
            <HBox spacing="15" alignment="CENTER">
                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;" alignment="CENTER">
                    <Label text="🏆 Meilleur Mois" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="bestMonthLabel" text="N/A" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                </VBox>

                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;" alignment="CENTER">
                    <Label text="📊 Durée Moyenne" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="avgDurationLabel" text="0 jours" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                </VBox>

                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;" alignment="CENTER">
                    <Label text="🎯 Taux Retour" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="returnRateLabel" text="0%" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                </VBox>

                <VBox spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;" alignment="CENTER">
                    <Label text="💎 Client VIP" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" />
                    <Label fx:id="topClientLabel" text="N/A" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                </VBox>
            </HBox>

            <!-- Activity Table Section -->
            <VBox spacing="20" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="5">
                        <Label text="🕒 Historique des Locations" style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                        <Label fx:id="tableSubtitleLabel" text="Toutes les locations de ce véhicule" style="-fx-font-size: 14px; -fx-text-fill: #64748b;" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <VBox spacing="10" alignment="CENTER_RIGHT">
                        <ComboBox fx:id="filterComboBox" onAction="#handleFilterChange" promptText="Filtrer par statut" style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 13px;" />
                        <Label fx:id="tableCountLabel" text="0 locations trouvées" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" />
                    </VBox>
                </HBox>

                <TableView fx:id="historyTable" prefHeight="300" style="-fx-background-radius: 8;">
                    <columns>
                        <TableColumn fx:id="idColumn" text="ID" prefWidth="60" />
                        <TableColumn fx:id="clientColumn" text="Client" prefWidth="160" />
                        <TableColumn fx:id="dateDebutColumn" text="Date Début" prefWidth="120" />
                        <TableColumn fx:id="dateFinColumn" text="Date Fin" prefWidth="120" />
                        <TableColumn fx:id="durationColumn" text="Durée" prefWidth="90" />
                        <TableColumn fx:id="statusColumn" text="Statut" prefWidth="110" />
                        <TableColumn fx:id="priceColumn" text="Prix Total" prefWidth="100" />
                        <TableColumn fx:id="actionsColumn" text="Actions" prefWidth="100" />
                    </columns>
                </TableView>

                <!-- Table Summary -->
                <HBox spacing="20" alignment="CENTER_LEFT" style="-fx-padding: 10 0 0 0;">
                    <Label fx:id="tableSummaryLabel" text="Résumé: 0 locations • 0.00 € total • 0 jours" style="-fx-font-size: 13px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Button text="📄 Exporter Sélection" onAction="#handleExportSelection" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" />
                </HBox>
            </VBox>

            <!-- Detailed Information Panel -->
            <VBox fx:id="detailPanel" spacing="20" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);" visible="false">
                <HBox alignment="CENTER_LEFT" spacing="15">
                    <Label text="📋 Détails de la Location Sélectionnée" style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Button text="✖ Masquer" onAction="#hideDetailPanel" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" />
                </HBox>

                <GridPane hgap="25" vgap="15" style="-fx-padding: 10 0;">
                    <columnConstraints>
                        <ColumnConstraints minWidth="120" />
                        <ColumnConstraints minWidth="200" />
                        <ColumnConstraints minWidth="120" />
                        <ColumnConstraints minWidth="200" />
                    </columnConstraints>

                    <Label text="👤 Client:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="0" GridPane.columnIndex="0" />
                    <Label fx:id="detailClientLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="0" GridPane.columnIndex="1" />

                    <Label text="📞 Téléphone:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="0" GridPane.columnIndex="2" />
                    <Label fx:id="detailPhoneLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="0" GridPane.columnIndex="3" />

                    <Label text="📅 Date début:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                    <Label fx:id="detailStartDateLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="1" GridPane.columnIndex="1" />

                    <Label text="📅 Date fin prévue:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="1" GridPane.columnIndex="2" />
                    <Label fx:id="detailEndDateLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="1" GridPane.columnIndex="3" />

                    <Label text="🏁 Date retour réelle:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                    <Label fx:id="detailActualEndLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="2" GridPane.columnIndex="1" />

                    <Label text="⏱️ Durée totale:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="2" GridPane.columnIndex="2" />
                    <Label fx:id="detailDurationLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.rowIndex="2" GridPane.columnIndex="3" />

                    <Label text="💰 Prix total:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="3" GridPane.columnIndex="0" />
                    <Label fx:id="detailPriceLabel" style="-fx-text-fill: #059669; -fx-font-weight: bold; -fx-font-size: 16px;" GridPane.rowIndex="3" GridPane.columnIndex="1" />

                    <Label text="📊 Statut:" style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" GridPane.rowIndex="3" GridPane.columnIndex="2" />
                    <Label fx:id="detailStatusLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px; -fx-font-weight: bold;" GridPane.rowIndex="3" GridPane.columnIndex="3" />
                </GridPane>

                <!-- Additional Actions -->
                <HBox spacing="15" alignment="CENTER_LEFT" style="-fx-padding: 15 0 0 0;">
                    <Button text="📧 Contacter Client" onAction="#handleContactClient" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" />
                    <Button text="📄 Générer Facture" onAction="#handleGenerateInvoice" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" />
                    <Button text="📝 Modifier" onAction="#handleEditLocation" style="-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" />
                </HBox>
            </VBox>

            <!-- Action Buttons -->
            <HBox spacing="15" alignment="CENTER">
                <Button text="📅 Voir Calendrier" onAction="#handleViewCalendar" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
                <Button text="📊 Rapport Complet" onAction="#handleFullReport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
                <Button text="❌ Fermer" onAction="#handleClose" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" />
            </HBox>

        </VBox>
    </content>
</ScrollPane>

        
        