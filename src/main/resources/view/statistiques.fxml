<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.chart.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1">
    <content>
        <VBox spacing="24" style="-fx-background-color: transparent;" minHeight="800">
    <stylesheets>
        <URL value="@../styles/modern-theme.css" />
    </stylesheets>

    <padding><Insets bottom="80" left="24" right="24" top="24" /></padding>
    
    <!-- Header -->
    <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -fx-padding: 32; -fx-background-radius: 20;">
        <HBox alignment="CENTER_LEFT" spacing="20">
            <VBox spacing="8" HBox.hgrow="ALWAYS">
                <Label styleClass="text-heading-1" style="-fx-text-fill: white; -fx-font-size: 28px; -fx-font-weight: 700;" text="Statistiques" />
                <Label styleClass="text-body" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 16px;" text="Analyse des performances et tendances" />
            </VBox>
        </HBox>
    </VBox>
    
    <!-- Statistics Cards -->
    <HBox spacing="20" alignment="CENTER">
        <!-- Revenue Card -->
        <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4); -fx-min-width: 200;" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="linear-gradient(135deg, #10b981 0%, #059669 100%)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="💰" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label styleClass="text-caption" style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-font-weight: 600;" text="REVENUS TOTAUX" />
                    <Label styleClass="text-heading-2" style="-fx-text-fill: #1e293b; -fx-font-size: 32px; -fx-font-weight: 700;" text="€15,420" />
                    <Label style="-fx-text-fill: #10b981; -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ +8% ce mois" />
                </VBox>
            </HBox>
        </VBox>
        
        <!-- Locations Card -->
        <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4); -fx-min-width: 200;" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="📅" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label styleClass="text-caption" style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-font-weight: 600;" text="LOCATIONS ACTIVES" />
                    <Label styleClass="text-heading-2" style="-fx-text-fill: #1e293b; -fx-font-size: 32px; -fx-font-weight: 700;" text="24" />
                    <Label style="-fx-text-fill: #3b82f6; -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ +3 cette semaine" />
                </VBox>
            </HBox>
        </VBox>
        
        <!-- Satisfaction Card -->
        <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4); -fx-min-width: 200;" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(245,158,11,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="⭐" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label styleClass="text-caption" style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-font-weight: 600;" text="SATISFACTION" />
                    <Label styleClass="text-heading-2" style="-fx-text-fill: #1e293b; -fx-font-size: 32px; -fx-font-weight: 700;" text="4.8/5" />
                    <Label style="-fx-text-fill: #f59e0b; -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ +0.2 ce mois" />
                </VBox>
            </HBox>
        </VBox>
    </HBox>
    
    <!-- Charts Section -->
    <HBox spacing="20">
        <!-- Revenue Chart -->
        <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);" HBox.hgrow="ALWAYS">
            <Label styleClass="text-heading-3" style="-fx-text-fill: #1e293b; -fx-font-size: 18px; -fx-font-weight: 600;" text="Évolution des revenus" />
            <LineChart prefHeight="300">
                <xAxis>
                    <CategoryAxis side="BOTTOM" />
                </xAxis>
                <yAxis>
                    <NumberAxis side="LEFT" />
                </yAxis>
            </LineChart>
        </VBox>
        
        <!-- Vehicle Usage Chart -->
        <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);" HBox.hgrow="ALWAYS">
            <Label styleClass="text-heading-3" style="-fx-text-fill: #1e293b; -fx-font-size: 18px; -fx-font-weight: 600;" text="Utilisation des véhicules" />
            <PieChart prefHeight="300" />
        </VBox>
    </HBox>
    
    <!-- Recent Activity -->
    <VBox spacing="16" styleClass="modern-card" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
        <Label styleClass="text-heading-3" style="-fx-text-fill: #1e293b; -fx-font-size: 18px; -fx-font-weight: 600;" text="Activité récente" />
        <VBox spacing="12">
            <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-padding: 12; -fx-background-color: #f8fafc; -fx-background-radius: 8;">
                <Label style="-fx-text-fill: #10b981; -fx-font-size: 16px;" text="✅" />
                <VBox spacing="2" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: #1e293b; -fx-font-size: 14px; -fx-font-weight: 600;" text="Nouvelle location confirmée" />
                    <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px;" text="Client: Marie Dubois - Véhicule: Peugeot 308" />
                </VBox>
                <Label style="-fx-text-fill: #9ca3af; -fx-font-size: 12px;" text="Il y a 2h" />
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-padding: 12; -fx-background-color: #f8fafc; -fx-background-radius: 8;">
                <Label style="-fx-text-fill: #3b82f6; -fx-font-size: 16px;" text="🔄" />
                <VBox spacing="2" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: #1e293b; -fx-font-size: 14px; -fx-font-weight: 600;" text="Véhicule retourné" />
                    <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px;" text="Client: Jean Martin - Véhicule: Renault Clio" />
                </VBox>
                <Label style="-fx-text-fill: #9ca3af; -fx-font-size: 12px;" text="Il y a 4h" />
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-padding: 12; -fx-background-color: #f8fafc; -fx-background-radius: 8;">
                <Label style="-fx-text-fill: #f59e0b; -fx-font-size: 16px;" text="⚠️" />
                <VBox spacing="2" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: #1e293b; -fx-font-size: 14px; -fx-font-weight: 600;" text="Maintenance programmée" />
                    <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px;" text="Véhicule: BMW X3 - Révision 20,000 km" />
                </VBox>
                <Label style="-fx-text-fill: #9ca3af; -fx-font-size: 12px;" text="Demain" />
            </HBox>
        </VBox>
    </VBox>
        </VBox>
    </content>
</ScrollPane>
