<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.collections.FXCollections?>
<?import java.lang.String?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LocationController">
    <content>
        <VBox spacing="28.0" style="-fx-background-color: #f8fafc;" prefHeight="1400">
            <padding><Insets bottom="200.0" left="28.0" right="28.0" top="28.0" /></padding>
            <!-- Header Section -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 12; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;">
                <VBox spacing="5.0">
                    <Label style="-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="Gestion des Locations de Véhicules" />
                    <Label style="-fx-font-size: 15px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Gérez toutes les locations, leurs détails et options avancées" />
                </VBox>
                <Region HBox.hgrow="ALWAYS" />
                <HBox spacing="14.0">
                    <Button fx:id="btnRentalHistory" onAction="#handleRentalHistory" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Historique Locations" />
                    <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #475569; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-border-color: #d1d5db; -fx-border-width: 1; -fx-border-radius: 8;" text="🔄 Actualiser" />
                    <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #059669; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Exporter" />
                </HBox>
            </HBox>
            <!-- Main Content Area -->
            <HBox spacing="24.0">
                <!-- Left Panel - Location List -->
                <VBox prefWidth="750.0" spacing="18.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 12; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;" HBox.hgrow="ALWAYS">
                    <!-- Search and Filter -->
                    <VBox spacing="16.0">
                        <Label style="-fx-font-size: 19px; -fx-font-weight: bold; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="Recherche et Filtres" />
                        <HBox spacing="12.0">
                            <TextField fx:id="searchField" prefHeight="36.0" prefWidth="320.0" promptText="Rechercher par client, véhicule, statut..." style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-padding: 8 12; -fx-font-size: 14px; -fx-prompt-text-fill: #9ca3af;" />
                            <Button fx:id="btnSearch" onAction="#handleRechercher" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-weight: 600;" text="🔍" />
                            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-weight: 600;" text="Effacer" />
                        </HBox>
                    </VBox>
                    <!-- Location Table -->
                    <VBox spacing="12.0">
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <Label style="-fx-font-size: 17px; -fx-font-weight: bold; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="Liste des Locations" />
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="lblTotalCount" style="-fx-font-size: 13px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI'; -fx-background-color: #f1f5f9; -fx-padding: 4 8; -fx-background-radius: 12;" text="Total: 0" />
                        </HBox>
                        <TableView fx:id="locationTable" prefHeight="420.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-border-width: 1; -fx-table-header-border-color: #e2e8f0;" VBox.vgrow="ALWAYS">
                            <columns>
                                <TableColumn fx:id="idColumn" prefWidth="50.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="ID" />
                                <TableColumn fx:id="clientColumn" prefWidth="150.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Client" />
                                <TableColumn fx:id="vehiculeColumn" prefWidth="150.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Véhicule" />
                                <TableColumn fx:id="dateDebutColumn" prefWidth="120.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Date Début" />
                                <TableColumn fx:id="dateFinColumn" prefWidth="120.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Date Fin" />
                                <TableColumn fx:id="statutColumn" prefWidth="100.0" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Statut" />
                            </columns>
                        </TableView>
                        <!-- Action Buttons -->
                        <HBox alignment="CENTER_RIGHT" spacing="12.0">
                            <Button fx:id="btnAddLocation" onAction="#handleAjouter" style="-fx-background-color: #1e293b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="➕ Ajouter" />
                            <Button fx:id="btnUpdateLocation" onAction="#handleModifier" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="✏️ Modifier" />
                            <Button fx:id="btnDeleteLocation" onAction="#handleSupprimer" style="-fx-background-color: #dc2626; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="🗑️ Supprimer" />
                            <Button fx:id="btnCancelLocation" onAction="#handleAnnulerLocation" style="-fx-background-color: #ea580c; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="🚫 Annuler la location" />
                            <Button fx:id="btnExtendLocation" onAction="#handleProlongerLocation" style="-fx-background-color: #059669; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="⏩ Prolonger" />
                            <Button fx:id="btnViewDetails" onAction="#handleViewDetails" style="-fx-background-color: #7c3aed; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="👁️ Visualiser" />
                            <Button fx:id="btnExportContract" onAction="#handleExportContract" style="-fx-background-color: #9333ea; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-weight: 600;" text="📄 Exporter Contrat" />
                        </HBox>
                    </VBox>
                </VBox>
                <!-- Right Panel - Location Details/Form -->
                <VBox prefWidth="420.0" spacing="18.0">
                    <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 12; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;">
                        <Label style="-fx-font-size: 19px; -fx-font-weight: bold; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="Détails de la Location" />

                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Client" />
                            <ComboBox fx:id="txtClientForm" prefHeight="36.0" promptText="Sélectionner le client" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" maxWidth="Infinity" />
                        </VBox>

                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Véhicule" />
                            <ComboBox fx:id="txtVehiculeForm" prefHeight="36.0" promptText="Sélectionner le véhicule" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" maxWidth="Infinity" />
                        </VBox>

                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Date de Début" />
                                <DatePicker fx:id="txtDateDebutForm" prefHeight="36.0" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6;" />
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Date de Fin" />
                                <DatePicker fx:id="txtDateFinForm" prefHeight="36.0" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6;" />
                            </VBox>
                        </HBox>

                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Statut" />
                            <ComboBox fx:id="txtStatutForm" prefHeight="36.0" promptText="Statut" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" maxWidth="Infinity" />
                        </VBox>

                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Lieu de prise en charge" />
                                <TextField fx:id="pickupLocationField" prefHeight="36.0" promptText="Lieu de prise en charge" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8 12; -fx-font-size: 14px;" />
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Lieu de retour" />
                                <TextField fx:id="deliveryLocationField" prefHeight="36.0" promptText="Lieu de retour" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8 12; -fx-font-size: 14px;" />
                            </VBox>
                        </HBox>

                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Type d'assurance" />
                                <ComboBox fx:id="insuranceTypeComboBox" prefHeight="36.0" promptText="Type d'assurance" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" maxWidth="Infinity">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Standard" />
                                            <String fx:value="Tous risques" />
                                            <String fx:value="Premium" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Politique carburant" />
                                <ComboBox fx:id="fuelPolicyComboBox" prefHeight="36.0" promptText="Politique carburant" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" maxWidth="Infinity">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Plein à plein" />
                                            <String fx:value="Plein à vide" />
                                            <String fx:value="Retour identique" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>
                        </HBox>

                        <VBox spacing="10.0">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Options" />
                            <VBox spacing="8.0" style="-fx-background-color: #f8fafc; -fx-padding: 12; -fx-background-radius: 6; -fx-border-color: #e5e7eb; -fx-border-width: 1; -fx-border-radius: 6;">
                                <CheckBox fx:id="optionAssurance" text="Assurance supplémentaire" style="-fx-font-size: 13px; -fx-text-fill: #374151;" />
                                <CheckBox fx:id="optionGPS" text="GPS" style="-fx-font-size: 13px; -fx-text-fill: #374151;" />
                                <CheckBox fx:id="optionSiegeBebe" text="Siège bébé" style="-fx-font-size: 13px; -fx-text-fill: #374151;" />
                                <CheckBox fx:id="optionAdditionalDriver" text="Conducteur additionnel" style="-fx-font-size: 13px; -fx-text-fill: #374151;" />
                            </VBox>
                        </VBox>

                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="Notes" />
                            <TextArea fx:id="notesField" promptText="Notes supplémentaires" prefRowCount="3" style="-fx-background-color: #f8fafc; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-font-size: 14px;" />
                        </VBox>

                        <VBox spacing="8.0" style="-fx-background-color: #eff6ff; -fx-padding: 16; -fx-background-radius: 8; -fx-border-color: #bfdbfe; -fx-border-width: 1; -fx-border-radius: 8;">
                            <Label style="-fx-font-weight: bold; -fx-font-size: 15px; -fx-text-fill: #1e40af;" text="Résumé" />
                            <Label fx:id="totalPrixLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e40af;" text="0 DH" />
                        </VBox>

                        <HBox alignment="CENTER_RIGHT" spacing="12.0">
                            <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: white; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-border-width: 1; -fx-padding: 10 16; -fx-font-weight: 600;" text="Annuler" />
                            <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: #059669; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-weight: 600;" text="💾 Enregistrer" />
                        </HBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>