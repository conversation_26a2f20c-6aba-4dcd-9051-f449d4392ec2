<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.collections.FXCollections?>
<?import java.lang.String?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LocationController">
    <content>
        <VBox spacing="28.0" style="-fx-background-color: #f8fafc;" prefHeight="1400">
            <padding><Insets bottom="200.0" left="28.0" right="28.0" top="28.0" /></padding>
            <!-- Header Section -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 14; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                <VBox spacing="5.0">
                    <Label style="-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Gestion des Locations de Véhicules" />
                    <Label style="-fx-font-size: 15px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Gérez toutes les locations, leurs détails et options avancées" />
                </VBox>
                <Region HBox.hgrow="ALWAYS" />
                <HBox spacing="14.0">
                    <Button fx:id="btnRentalHistory" onAction="#handleRentalHistory" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Historique Locations" />
                    <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #475569; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI';" text="🔄 Actualiser" />
                    <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Exporter" />
                </HBox>
            </HBox>
            <!-- Main Content Area -->
            <HBox spacing="24.0">
                <!-- Left Panel - Location List -->
                <VBox prefWidth="750.0" spacing="18.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 14; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" HBox.hgrow="ALWAYS">
                    <!-- Search and Filter -->
                    <VBox spacing="16.0">
                        <Label style="-fx-font-size: 19px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Recherche et Filtres" />
                        <HBox spacing="12.0">
                            <TextField fx:id="searchField" prefHeight="28.0" prefWidth="320.0" promptText="Rechercher par client, véhicule, statut..." />
                            <Button fx:id="btnSearch" onAction="#handleRechercher" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 7;" text="🔍" />
                            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 7;" text="Effacer" />
                        </HBox>
                    </VBox>
                    <!-- Location Table -->
                    <VBox spacing="12.0">
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <Label style="-fx-font-size: 17px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Liste des Locations" />
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="lblTotalCount" style="-fx-font-size: 13px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Total: 0" />
                        </HBox>
                        <TableView fx:id="locationTable" prefHeight="420.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 7; -fx-table-header-border-color: #e2e8f0;" VBox.vgrow="ALWAYS">
                            <columns>
                                <TableColumn fx:id="idColumn" prefWidth="50.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="ID" />
                                <TableColumn fx:id="clientColumn" prefWidth="150.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Client" />
                                <TableColumn fx:id="vehiculeColumn" prefWidth="150.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Véhicule" />
                                <TableColumn fx:id="dateDebutColumn" prefWidth="120.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Date Début" />
                                <TableColumn fx:id="dateFinColumn" prefWidth="120.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Date Fin" />
                                <TableColumn fx:id="statutColumn" prefWidth="100.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Statut" />
                            </columns>
                        </TableView>
                        <!-- Action Buttons -->
                        <HBox alignment="CENTER_RIGHT" spacing="12.0">
                            <Button fx:id="btnAddLocation" onAction="#handleAjouter" style="-fx-background-color: #1a3c40; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="➕ Ajouter" />
                            <Button fx:id="btnUpdateLocation" onAction="#handleModifier" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="✏️ Modifier" />
                            <Button fx:id="btnDeleteLocation" onAction="#handleSupprimer" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="🗑️ Supprimer" />
                            <Button fx:id="btnCancelLocation" onAction="#handleAnnulerLocation" style="-fx-background-color: #f59e42; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="🚫 Annuler la location" />
                            <Button fx:id="btnExtendLocation" onAction="#handleProlongerLocation" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="⏩ Prolonger" />
                            <Button fx:id="btnViewDetails" onAction="#handleViewDetails" style="-fx-background-color: #6366f1; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="👁️ Visualiser" />
                            <Button fx:id="btnExportContract" onAction="#handleExportContract" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="📄 Exporter Contrat" />
                        </HBox>
                    </VBox>
                </VBox>
                <!-- Right Panel - Location Details/Form -->
                <VBox prefWidth="420.0" spacing="18.0">
                    <VBox spacing="10.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 14; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                        <Label style="-fx-font-size: 19px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Détails de la Location" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Client" />
                        <ComboBox fx:id="txtClientForm" promptText="Sélectionner le client" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Véhicule" />
                        <ComboBox fx:id="txtVehiculeForm" promptText="Sélectionner le véhicule" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Date de Début" />
                        <DatePicker fx:id="txtDateDebutForm" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Date de Fin" />
                        <DatePicker fx:id="txtDateFinForm" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Statut" />
                        <ComboBox fx:id="txtStatutForm" promptText="Statut" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Lieu de prise en charge" />
                        <TextField fx:id="pickupLocationField" promptText="Lieu de prise en charge" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Lieu de retour" />
                        <TextField fx:id="deliveryLocationField" promptText="Lieu de retour" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Type d'assurance" />
                        <ComboBox fx:id="insuranceTypeComboBox" promptText="Type d'assurance">
                            <items>
                                <FXCollections fx:factory="observableArrayList">
                                    <String fx:value="Standard" />
                                    <String fx:value="Tous risques" />
                                    <String fx:value="Premium" />
                                </FXCollections>
                            </items>
                        </ComboBox>
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Politique carburant" />
                        <ComboBox fx:id="fuelPolicyComboBox" promptText="Politique carburant">
                            <items>
                                <FXCollections fx:factory="observableArrayList">
                                    <String fx:value="Plein à plein" />
                                    <String fx:value="Plein à vide" />
                                    <String fx:value="Retour identique" />
                                </FXCollections>
                            </items>
                        </ComboBox>
                        <CheckBox fx:id="optionAssurance" text="Assurance supplémentaire" />
                        <CheckBox fx:id="optionGPS" text="GPS" />
                        <CheckBox fx:id="optionSiegeBebe" text="Siège bébé" />
                        <CheckBox fx:id="optionAdditionalDriver" text="Conducteur additionnel" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #3b82f6;" text="Notes" />
                        <TextArea fx:id="notesField" promptText="Notes supplémentaires" prefRowCount="2" />
                        <Label style="-fx-font-weight: bold; -fx-font-size: 15px; -fx-text-fill: #f59e42;" text="Résumé" />
                        <Label fx:id="totalPrixLabel" style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="0 DH" />
                        <HBox alignment="CENTER_RIGHT" spacing="12.0">
                            <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 7; -fx-padding: 8 16;" text="Annuler" />
                            <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 16;" text="💾 Enregistrer" />
                        </HBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>