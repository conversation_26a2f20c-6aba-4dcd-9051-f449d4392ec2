<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import javafx.collections.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: #f8fafc;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LocationController">
    <content>
        <VBox prefHeight="1400" spacing="28.0" style="-fx-background-color: #f8fafc;">
            <padding><Insets bottom="200.0" left="28.0" right="28.0" top="28.0" /></padding>

            <!-- Header Section with Glassmorphism Effect -->
            <VBox spacing="16.0" style="-fx-background-color: white; -fx-background-radius: 16; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
                <padding><Insets bottom="24.0" left="24.0" right="24.0" top="24.0" /></padding>

                <HBox alignment="CENTER_LEFT" spacing="24.0">
                    <VBox spacing="8.0">
                        <Label style="-fx-font-size: 26px; -fx-font-weight: 800; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="🚗 Gestion des Locations" />
                        <Label style="-fx-font-size: 15px; -fx-text-fill: #64748b; -fx-font-family: 'Inter', 'Segoe UI';" text="Gérez toutes les locations avec style et efficacité" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />

                    <HBox spacing="12.0">
                        <Button fx:id="btnRentalHistory" onAction="#handleRentalHistory" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 12; -fx-padding: 12 20; -fx-font-family: 'Inter', 'Segoe UI'; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(139,92,246,0.3), 8, 0, 0, 4);" text="📊 Historique" />
                        <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-font-size: 14px; -fx-background-radius: 12; -fx-padding: 12 20; -fx-font-family: 'Inter', 'Segoe UI'; -fx-font-weight: 600; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;" text="🔄 Actualiser" />
                        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 12; -fx-padding: 12 20; -fx-font-family: 'Inter', 'Segoe UI'; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 8, 0, 0, 4);" text="📊 Exporter" />
                    </HBox>
                </HBox>
            </VBox>

            <!-- Main Content Area -->
            <HBox spacing="24.0">
                <!-- Left Panel - Enhanced Table View -->
                <VBox prefWidth="800.0" spacing="20.0" HBox.hgrow="ALWAYS">

                    <!-- Search Card -->
                    <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
                        <Label style="-fx-font-size: 18px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="🔍 Recherche Avancée" />

                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <TextField fx:id="searchField" prefHeight="44.0" prefWidth="400.0" promptText="Rechercher par client, véhicule, statut..." style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 10; -fx-padding: 12; -fx-font-size: 14px; -fx-prompt-text-fill: #64748b;" />
                            <Button fx:id="btnSearch" onAction="#handleRechercher" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 16; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 8, 0, 0, 2);" text="🔍" />
                            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 16; -fx-font-weight: 600;" text="✨ Effacer" />
                        </HBox>
                    </VBox>

                    <!-- Table Card -->
                    <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);" VBox.vgrow="ALWAYS">
                        <HBox alignment="CENTER_LEFT" spacing="16.0">
                            <Label style="-fx-font-size: 18px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="📋 Liste des Locations" />
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="lblTotalCount" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-family: 'Inter', 'Segoe UI'; -fx-background-color: #f1f5f9; -fx-padding: 6 12; -fx-background-radius: 20;" text="Total: 0" />
                        </HBox>

                        <TableView fx:id="locationTable" prefHeight="460.0" style="-fx-background-color: transparent; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-table-header-border-color: transparent; -fx-control-inner-background: white;" VBox.vgrow="ALWAYS">
                            <columns>
                                <TableColumn fx:id="idColumn" prefWidth="60.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="ID" />
                                <TableColumn fx:id="clientColumn" prefWidth="180.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="👤 Client" />
                                <TableColumn fx:id="vehiculeColumn" prefWidth="180.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="🚗 Véhicule" />
                                <TableColumn fx:id="dateDebutColumn" prefWidth="130.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="📅 Début" />
                                <TableColumn fx:id="dateFinColumn" prefWidth="130.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="📅 Fin" />
                                <TableColumn fx:id="statutColumn" prefWidth="120.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="📊 Statut" />
                            </columns>
                        </TableView>

                        <!-- Enhanced Action Buttons -->
                        <HBox alignment="CENTER" spacing="8.0">
                            <Button fx:id="btnAddLocation" onAction="#handleAjouter" style="-fx-background-color: #1e293b; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(30,41,59,0.3), 6, 0, 0, 2);" text="➕ Ajouter" />
                            <Button fx:id="btnUpdateLocation" onAction="#handleModifier" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 6, 0, 0, 2);" text="✏️ Modifier" />
                            <Button fx:id="btnDeleteLocation" onAction="#handleSupprimer" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(239,68,68,0.3), 6, 0, 0, 2);" text="🗑️ Supprimer" />
                            <Button fx:id="btnCancelLocation" onAction="#handleAnnulerLocation" style="-fx-background-color: #f59e42; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(245,158,66,0.3), 6, 0, 0, 2);" text="🚫 Annuler" />
                        </HBox>
                  <HBox alignment="CENTER" layoutX="34.0" layoutY="867.0" spacing="8.0">
                     <children>
                        <Button fx:id="btnExtendLocation1" onAction="#handleProlongerLocation" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 6, 0, 0, 2);" text="⏩ Prolonger" />
                        <Button fx:id="btnViewDetails1" onAction="#handleViewDetails" style="-fx-background-color: #6366f1; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(99,102,241,0.3), 6, 0, 0, 2);" text="👁️ Voir" />
                        <Button fx:id="btnExportContract1" onAction="#handleExportContract" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(139,92,246,0.3), 6, 0, 0, 2);" text="📄 Contrat" />
                     </children>
                  </HBox>
                    </VBox>
                </VBox>

                <!-- Right Panel - Modern Form -->
                <VBox prefWidth="480.0" spacing="20.0">
                    <VBox spacing="24.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">

                        <!-- Form Header -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="📝 Détails de Location" />
                            <Separator style="-fx-background-color: linear-gradient(to right, #3b82f6, #8b5cf6);" />
                        </VBox>

                        <!-- Client Section -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="👤 Client" />
                            <ComboBox fx:id="txtClientForm" maxWidth="Infinity" prefHeight="40.0" promptText="Sélectionner le client" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                        </VBox>

                        <!-- Vehicle Section -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="🚗 Véhicule" />
                            <ComboBox fx:id="txtVehiculeForm" maxWidth="Infinity" prefHeight="40.0" promptText="Sélectionner le véhicule" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                        </VBox>

                        <!-- Date Section -->
                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📅 Début" />
                                <DatePicker fx:id="txtDateDebutForm" maxWidth="Infinity" prefHeight="40.0" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8;" />
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📅 Fin" />
                                <DatePicker fx:id="txtDateFinForm" maxWidth="Infinity" prefHeight="40.0" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8;" />
                            </VBox>
                        </HBox>

                        <!-- Status -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📊 Statut" />
                            <ComboBox fx:id="txtStatutForm" maxWidth="Infinity" prefHeight="40.0" promptText="Sélectionner le statut" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                        </VBox>

                        <!-- Location Fields -->
                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📍 Prise en charge" />
                                <TextField fx:id="pickupLocationField" prefHeight="40.0" promptText="Lieu de départ" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📍 Retour" />
                                <TextField fx:id="deliveryLocationField" prefHeight="40.0" promptText="Lieu de retour" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>
                        </HBox>

                        <!-- Insurance & Fuel -->
                        <HBox spacing="12.0">
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="🛡️ Assurance" />
                                <ComboBox fx:id="insuranceTypeComboBox" maxWidth="Infinity" prefHeight="40.0" promptText="Type d'assurance" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Standard" />
                                            <String fx:value="Tous risques" />
                                            <String fx:value="Premium" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>
                            <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="⛽ Carburant" />
                                <ComboBox fx:id="fuelPolicyComboBox" maxWidth="Infinity" prefHeight="40.0" promptText="Politique carburant" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Plein à plein" />
                                            <String fx:value="Plein à vide" />
                                            <String fx:value="Retour identique" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>
                        </HBox>

                        <!-- Options Section -->
                        <VBox spacing="10.0">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="⚙️ Options" />
                            <VBox spacing="8.0" style="-fx-background-color: #f8fafc; -fx-padding: 16; -fx-background-radius: 8; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 8;">
                                <CheckBox fx:id="optionAssurance" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 500;" text="🛡️ Assurance supplémentaire" />
                                <CheckBox fx:id="optionGPS" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 500;" text="🗺️ GPS" />
                                <CheckBox fx:id="optionSiegeBebe" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 500;" text="👶 Siège bébé" />
                                <CheckBox fx:id="optionAdditionalDriver" style="-fx-font-size: 13px; -fx-text-fill: #374151; -fx-font-weight: 500;" text="👥 Conducteur additionnel" />
                            </VBox>
                        </VBox>

                        <!-- Notes -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="📝 Notes" />
                            <TextArea fx:id="notesField" prefRowCount="3" promptText="Notes supplémentaires..." style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                        </VBox>

                        <!-- Price Summary -->
                        <VBox spacing="8.0" style="-fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -fx-padding: 20; -fx-background-radius: 12;">
                            <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-family: 'Inter', 'Segoe UI';" text="💰 Total" />
                            <Label fx:id="totalPrixLabel" style="-fx-font-size: 28px; -fx-font-weight: 800; -fx-text-fill: white; -fx-font-family: 'Inter', 'Segoe UI';" text="0 DH" />
                        </VBox>

                        <!-- Action Buttons -->
                        <HBox alignment="CENTER" spacing="12.0">
                            <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 10; -fx-padding: 12 24; -fx-font-weight: 600; -fx-font-size: 14px;" text="❌ Annuler" />
                            <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: linear-gradient(135deg, #10b981, #047857); -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-weight: 600; -fx-font-size: 14px; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.4), 10, 0, 0, 4);" text="💾 Enregistrer" />
                        </HBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>
