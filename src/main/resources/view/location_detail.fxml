<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.effect.*?>
<?import javafx.geometry.*?>

<VBox fx:id="root" alignment="TOP_LEFT" prefHeight="700.0" prefWidth="1000.0" spacing="20.0" style="-fx-background-color: #f8fafc; -fx-padding: 25;" xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LocationDetailController">

    <!-- Header -->
    <Label style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1e293b;" text="Détails de la Location" />
    <Separator style="-fx-border-color: #e2e8f0;" />

    <!-- Main Content (Side-by-Side Sections) -->
    <HBox alignment="TOP_LEFT" spacing="20.0">
        <!-- Vehicle Card (Left) -->
        <VBox style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 10, 0, 0, 2);" prefWidth="320">
            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="Véhicule Loué" />
            <ImageView fx:id="imgVehicule" fitHeight="160" fitWidth="240" style="-fx-background-color: #f1f5f9; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 5, 0, 0, 2);" />
            <GridPane hgap="10.0" vgap="8.0" style="-fx-padding: 10 0 0 0;">
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Marque:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <Label fx:id="lblVehiculeMarque" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Modèle:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <Label fx:id="lblVehiculeModele" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Immatriculation:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                <Label fx:id="lblVehiculeImmat" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="État:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                <Label fx:id="lblVehiculeEtat" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Carburant:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                <Label fx:id="lblVehiculeCarburant" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Prix/jour:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                <Label fx:id="lblVehiculePrixJour" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Métrage (km):" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                <Label fx:id="lblVehiculeMetrage" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="120" />
                    <ColumnConstraints hgrow="ALWAYS" />
                </columnConstraints>
                <rowConstraints>
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                </rowConstraints>
            </GridPane>
        </VBox>

        <!-- Client & Location Cards (Right) -->
        <VBox spacing="20.0" prefWidth="600">
            <!-- Client Card -->
            <VBox style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 10, 0, 0, 2);">
                <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="Client" />
                <GridPane hgap="10.0" vgap="8.0">
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Nom:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                    <Label fx:id="lblClientNom" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Prénom:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                    <Label fx:id="lblClientPrenom" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="CIN:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                    <Label fx:id="lblClientCIN" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Téléphone:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                    <Label fx:id="lblClientTel" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                    <Label fx:id="lblClientEmail" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                    <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="120" />
                        <ColumnConstraints hgrow="ALWAYS" />
                    </columnConstraints>
                    <rowConstraints>
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                    </rowConstraints>
                </GridPane>
            </VBox>

            <!-- Location Card -->
            <VBox style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 10, 0, 0, 2);">
                <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="Détails de la Location" />
                <GridPane hgap="10.0" vgap="8.0">
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Date début:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                    <Label fx:id="lblLocationDebut" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Date fin prévue:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                    <Label fx:id="lblLocationFinPrevue" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Date fin réelle:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                    <Label fx:id="lblLocationFinReelle" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Statut:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                    <Label fx:id="lblStatus" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Prix total:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                    <Label fx:id="lblPrixTotal" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Pénalité:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                    <Label fx:id="lblPenalite" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                    <Label style="-fx-font-weight: bold; -fx-text-fill: #64748b;" text="Créé par:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                    <Label fx:id="lblCreatedBy" style="-fx-text-fill: #1e293b;" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                    <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="120" />
                        <ColumnConstraints hgrow="ALWAYS" />
                    </columnConstraints>
                    <rowConstraints>
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                    </rowConstraints>
                </GridPane>
            </VBox>
        </VBox>
    </HBox>

    <!-- Buttons -->
    <HBox alignment="CENTER_RIGHT" spacing="12.0" style="-fx-padding: 20 0 0 0;">
        <Button fx:id="btnExportContract" onAction="#handleExportContract" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 24; -fx-cursor: hand;" text="📄 Exporter Contrat" />
        <Button fx:id="btnModify" onAction="#handleModify" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 24; -fx-cursor: hand;" text="✏️ Modifier" />
        <Button onAction="#closeWindow" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 24; -fx-cursor: hand;" text="Fermer" />
    </HBox>
</VBox>