<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane prefHeight="1007.0" prefWidth="1485.0" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.VehicleMaintenanceController">
   <top>
      <VBox style="-fx-background-color: linear-gradient(to right, #f59e0b, #d97706); -fx-padding: 20;">
         <HBox alignment="CENTER_LEFT" spacing="20">
            <Label style="-fx-text-fill: white; -fx-font-size: 28px; -fx-font-weight: bold;" text="🔧 Gestion de la Maintenance des Véhicules" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
            <Button fx:id="btnAddMaintenance" onAction="#handleAddMaintenance" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="➕ Nouvelle Maintenance" />
            <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="📊 Exporter" />
         </HBox>

         <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 10 0 0 0;">
            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px;" text="Filtrer par:" />
            <ComboBox fx:id="filterVehicleCombo" promptText="Véhicule" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            <ComboBox fx:id="filterStatusCombo" promptText="Statut" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            <ComboBox fx:id="filterTypeCombo" promptText="Type Maintenance" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            <DatePicker fx:id="filterDatePicker" promptText="Date" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✖ Effacer" />
         </HBox>
      </VBox>
   </top>

   <center>
      <SplitPane dividerPositions="0.6790289952798382" orientation="HORIZONTAL">
         <!-- Main Content Area -->
         <VBox spacing="15" style="-fx-padding: 20;">
            <!-- Statistics Cards -->
            <HBox spacing="15">
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #fef3c7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #fbbf24; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblOverdueMaintenance" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #d97706;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #92400e;" text="Maintenance En Retard" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #fee2e2; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #f87171; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblOldVehicles" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #dc2626;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #991b1b;" text="Véhicules +5 ans" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #dbeafe; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #60a5fa; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblUpcomingMaintenance" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2563eb;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #1d4ed8;" text="Maintenance Prochaine" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #dcfce7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #4ade80; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalMaintenances" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #16a34a;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #15803d;" text="Total Maintenances" />
               </VBox>
            </HBox>
            
            <!-- Maintenance Schedule Table -->
            <VBox spacing="10" VBox.vgrow="ALWAYS">
               <HBox alignment="CENTER_LEFT" spacing="10">
                  <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📋 Planning de Maintenance" />
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="searchField" onKeyReleased="#handleSearch" promptText="🔍 Rechercher..." style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-border-color: #d1d5db; -fx-border-radius: 8;" />
               </HBox>
               
               <TableView fx:id="maintenanceTable" style="-fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12;" VBox.vgrow="ALWAYS">
                  <columns>
                     <TableColumn fx:id="colVehicle" prefWidth="150" text="Véhicule" />
                     <TableColumn fx:id="colAge" prefWidth="80" text="Âge" />
                     <TableColumn fx:id="colType" prefWidth="120" text="Type Maintenance" />
                     <TableColumn fx:id="colLastMaintenance" prefWidth="120" text="Dernière Maintenance" />
                     <TableColumn fx:id="colNextMaintenance" prefWidth="120" text="Prochaine Maintenance" />
                     <TableColumn fx:id="colStatus" prefWidth="100" text="Statut" />
                     <TableColumn fx:id="colDaysOverdue" prefWidth="100" text="Jours de Retard" />
                     <TableColumn fx:id="colActions" prefWidth="150" text="Actions" />
                  </columns>
               </TableView>
            </VBox>
         </VBox>
         
         <!-- Vehicle Details Sidebar -->
         <VBox prefHeight="814.0" prefWidth="288.0" spacing="15" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 0 1;">
            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="🚗 Détails du Véhicule" />
            
            <!-- Vehicle Info Card -->
            <VBox fx:id="vehicleInfoCard" spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 15; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
               <Label fx:id="lblSelectedVehicle" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Aucun véhicule sélectionné" />
               <Label fx:id="lblVehicleAge" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="" />
               <Label fx:id="lblVehicleKm" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="" />
               <Label fx:id="lblLastMaintenanceInfo" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="" />
            </VBox>
            
            <!-- Maintenance History -->
            <VBox spacing="10">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📝 Historique de Maintenance" />
               <ListView fx:id="maintenanceHistoryList" prefHeight="200" style="-fx-background-radius: 8; -fx-border-color: #e5e7eb; -fx-border-radius: 8;" />
            </VBox>
            
            <!-- Failure History -->
            <VBox spacing="10">
               <HBox alignment="CENTER_LEFT" spacing="10">
                  <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="⚠️ Historique des Pannes" />
                  <Button fx:id="btnAddFailure" onAction="#handleAddFailure" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 4 8; -fx-font-size: 10px;" text="+ Panne" />
               </HBox>
               <ListView fx:id="failureHistoryList" prefHeight="150" style="-fx-background-radius: 8; -fx-border-color: #e5e7eb; -fx-border-radius: 8;" />
            </VBox>
            
            <!-- Quick Actions -->
            <VBox spacing="8">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="⚡ Actions Rapides" />
               <Button fx:id="btnScheduleMaintenance" onAction="#handleScheduleMaintenance" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 12;" text="📅 Programmer Maintenance" />
               <Button fx:id="btnViewMaintenanceDetails" onAction="#handleViewMaintenanceDetails" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 12;" text="👁️ Voir Détails" />
               <Button fx:id="btnGenerateReport" onAction="#handleGenerateReport" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 12;" text="📄 Rapport Maintenance" />
            </VBox>
         </VBox>
      </SplitPane>
   </center>
   
   <bottom>
      <HBox alignment="CENTER_RIGHT" spacing="12" style="-fx-background-color: #f8fafc; -fx-padding: 15; -fx-border-color: #e5e7eb; -fx-border-width: 1 0 0 0;">
         <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="Total: 0 maintenances" />
         <Region HBox.hgrow="ALWAYS" />
         <Button onAction="#closeWindow" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20;" text="Fermer" />
      </HBox>
   </bottom>
</BorderPane>
