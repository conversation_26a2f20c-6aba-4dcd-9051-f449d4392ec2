<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="controller.DashboardController"
            prefHeight="900.0" prefWidth="1400.0"
            style="-fx-background-color: #f8fafc; -fx-font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;">

    <!-- MODERN SIDEBAR -->
    <left>
        <VBox prefWidth="280.0" spacing="0"
              style="-fx-background-color: linear-gradient(to bottom, #ffffff, #f8fafc); -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 20, 0, 0, 0); -fx-border-color: #e2e8f0; -fx-border-width: 0 1 0 0;">

            <!-- LOGO SECTION -->
            <VBox alignment="CENTER_LEFT" spacing="8" style="-fx-background-color: transparent;">
                <padding>
                    <Insets top="24" right="24" bottom="24" left="24"/>
                </padding>
                <HBox alignment="CENTER_LEFT" spacing="12">
                    <StackPane style="-fx-effect: dropshadow(gaussian, rgba(99, 102, 241, 0.3), 8, 0, 0, 2);">
                        <Circle radius="20" style="-fx-fill: linear-gradient(to bottom, #6366f1, #8b5cf6);"/>
                        <Label text="🚗" style="-fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: bold;"/>
                    </StackPane>
                    <VBox spacing="2">
                        <Label text="LocationV1" style="-fx-text-fill: #1e293b; -fx-font-size: 22px; -fx-font-weight: 700;"/>
                        <Label text="Tableau de bord" style="-fx-text-fill: #64748b; -fx-font-size: 12px; -fx-font-weight: 500;"/>
                    </VBox>
                </HBox>
            </VBox>

            <Separator style="-fx-background-color: #e2e8f0; -fx-pref-height: 1;"/>

            <!-- NAVIGATION SECTION -->
            <VBox spacing="8" style="-fx-background-color: transparent;">
                <padding>
                    <Insets top="16" right="16" bottom="16" left="16"/>
                </padding>

                <!-- Main Navigation -->
                <Label text="MENU PRINCIPAL" style="-fx-text-fill: #94a3b8; -fx-font-size: 11px; -fx-font-weight: 600; -fx-padding: 8 12 4 12;"/>

                <VBox spacing="4" style="-fx-background-color: transparent;">
                    <Button fx:id="btnDashboard" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="dashboard"
                            style="-fx-background-color: linear-gradient(to right, #6366f1, #8b5cf6); -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: white; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0; -fx-effect: dropshadow(gaussian, rgba(99, 102, 241, 0.3), 8, 0, 0, 2);">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="📊" style="-fx-font-size: 16px; -fx-text-fill: white;"/>
                                <Label text="Tableau de bord" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: white;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnClients" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="clients"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="👥" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Clients" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnVehicules" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="vehicules"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="🚗" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Véhicules" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnCatalogue" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="catalogue"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="📋" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Catalogue" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnNewLocation" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="newLocation"
                            style="-fx-background-color: linear-gradient(to right, #10b981, #059669); -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: white; -fx-font-weight: 600; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0; -fx-effect: dropshadow(gaussian, rgba(16, 185, 129, 0.3), 8, 0, 0, 2);">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="➕" style="-fx-font-size: 16px; -fx-text-fill: white;"/>
                                <Label text="Nouvelle Location" style="-fx-font-size: 14px; -fx-font-weight: 600; -fx-text-fill: white;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnLocations" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="locations"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="📅" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Locations" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnPaiements" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="paiements"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="💰" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Paiements" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>

                    <Button fx:id="btnUserManagement" maxWidth="Infinity"
                            onAction="#navigateToPage" userData="userManagement"
                            style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 12 16; -fx-text-fill: #64748b; -fx-font-weight: 500; -fx-cursor: hand; -fx-alignment: center-left; -fx-border-color: transparent; -fx-border-width: 0;">
                        <graphic>
                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label text="👥" style="-fx-font-size: 16px; -fx-text-fill: #64748b;"/>
                                <Label text="Utilisateurs" style="-fx-font-size: 14px; -fx-font-weight: 500; -fx-text-fill: #64748b;"/>
                            </HBox>
                        </graphic>
                    </Button>
                </VBox>
            </VBox>

            <!-- SPACER -->
            <Region VBox.vgrow="ALWAYS"/>

            <!-- USER PROFILE SECTION -->
            <VBox spacing="12" style="-fx-background-color: transparent;">
                <padding>
                    <Insets top="16" right="16" bottom="24" left="16"/>
                </padding>

                <VBox spacing="12" style="-fx-background-color: linear-gradient(to bottom, #6366f1, #8b5cf6); -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(99, 102, 241, 0.3), 12, 0, 0, 4);">
                    <padding>
                        <Insets top="16" right="16" bottom="16" left="16"/>
                    </padding>

                    <HBox alignment="CENTER_LEFT" spacing="12">
                        <StackPane style="-fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 4, 0, 0, 1);">
                            <Circle radius="20" style="-fx-fill: rgba(255, 255, 255, 0.9);"/>
                            <Label fx:id="lblUserName" text="AD" style="-fx-text-fill: #6366f1; -fx-font-size: 14px; -fx-font-weight: bold;"/>
                        </StackPane>
                        <VBox spacing="2" HBox.hgrow="ALWAYS">
                            <Label text="Administrateur" style="-fx-text-fill: white; -fx-font-size: 15px; -fx-font-weight: 600;"/>
                            <Label fx:id="lblUserRole" text="<EMAIL>" style="-fx-text-fill: rgba(255, 255, 255, 0.8); -fx-font-size: 12px;"/>
                        </VBox>
                    </HBox>

                    <Button fx:id="btnLogout" maxWidth="Infinity" onAction="#handleLogout"
                            style="-fx-background-color: rgba(255, 255, 255, 0.15); -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: 500; -fx-cursor: hand; -fx-border-color: rgba(255, 255, 255, 0.2); -fx-border-width: 1; -fx-border-radius: 10;">
                        <graphic>
                            <HBox alignment="CENTER" spacing="8">
                                <Label text="🚪" style="-fx-font-size: 14px; -fx-text-fill: white;"/>
                                <Label text="Déconnexion" style="-fx-font-size: 13px; -fx-font-weight: 500; -fx-text-fill: white;"/>
                            </HBox>
                        </graphic>
                    </Button>
                </VBox>
            </VBox>
        </VBox>
    </left>

    <!-- MAIN CONTENT AREA -->
    <center>
        <StackPane fx:id="contentPane" style="-fx-background-color: #f8fafc; -fx-padding: 24;" />
    </center>

    <!-- TOP BAR -->
    <top>
        <HBox alignment="CENTER_LEFT" spacing="20"
              style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 4, 0, 0, 1); -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;">
            <padding>
                <Insets top="16" right="24" bottom="16" left="24"/>
            </padding>

            <VBox spacing="2">
                <Label fx:id="lblPageTitle" text="Tableau de Bord" style="-fx-text-fill: #1e293b; -fx-font-size: 24px; -fx-font-weight: 700;"/>
                <Label fx:id="lblPageSubtitle" text="Bienvenue dans votre espace d'administration" style="-fx-text-fill: #64748b; -fx-font-size: 14px;"/>
            </VBox>

            <Region HBox.hgrow="ALWAYS"/>

            <HBox alignment="CENTER_RIGHT" spacing="12">
                <Button fx:id="btnNotifications" onAction="#handleNotifications"
                        style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand; -fx-border-color: transparent;">
                    <graphic>
                        <HBox alignment="CENTER" spacing="6">
                            <Label text="🔔" style="-fx-font-size: 14px; -fx-text-fill: #64748b;"/>
                            <Label fx:id="lblNotificationCount" text="3" visible="true"
                                   style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 2 6; -fx-font-size: 10px; -fx-font-weight: bold; -fx-min-width: 18; -fx-alignment: center;"/>
                        </HBox>
                    </graphic>
                </Button>

                <MenuButton fx:id="menuProfile" text="Profil"
                            style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-cursor: hand;">
                    <graphic>
                        <Label text="👤" style="-fx-font-size: 14px; -fx-text-fill: #64748b;"/>
                    </graphic>
                    <items>
                        <MenuItem text="Voir Profil" onAction="#handleViewProfile"/>
                        <MenuItem text="Paramètres du compte" onAction="#handleAccountSettings"/>
                        <SeparatorMenuItem/>
                        <MenuItem text="Aide  Support" onAction="#handleHelp"/>
                    </items>
                </MenuButton>
            </HBox>
        </HBox>
    </top>
</BorderPane>