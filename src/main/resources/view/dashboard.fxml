<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>

<BorderPane prefHeight="878.0" prefWidth="1400.0" style="-fx-background-color: #f8fafc; -fx-font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DashboardController">

    <!-- MERGED MODERN SIDEBAR -->
    <left>
        <VBox fx:id="sidebar" prefHeight="900.0" prefWidth="280.0" style="-fx-background-color: linear-gradient(to bottom, #4f46e5, #7c3aed); -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 15, 0, 0, 0);">

            <!-- Header with Logo -->
            <HBox alignment="CENTER_LEFT" prefHeight="80.0" spacing="12.0" style="-fx-background-color: rgba(255,255,255,0.08); -fx-padding: 0 20;">
                <StackPane>
                    <Circle fill="white" opacity="0.9" radius="32.0" />
                    <Label style="-fx-font-size: 16px;" text="🚗" />
                </StackPane>
                <VBox prefHeight="61.0" prefWidth="168.0">
                    <Label prefHeight="53.0" prefWidth="105.0" style="-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';" text="LocationV1" />
                    <Label prefHeight="9.0" prefWidth="109.0" style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 12px; -fx-font-family: 'Segoe UI';" text="Système de gestion" />
                </VBox>
            </HBox>

            <!-- Navigation Menu -->
            <VBox spacing="6.0" style="-fx-padding: 20 15 15 15;">
                <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 11px; -fx-font-weight: bold; -fx-padding: 0 0 8 10; -fx-font-family: 'Segoe UI';" text="NAVIGATION" />

                <!-- All Navigation Items - Uniform Style -->
                <Button fx:id="btnDashboard" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="dashboard">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="📊" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Tableau de bord" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnClients" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="clients">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="👥" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Clients" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnVehicules" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="vehicules">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="🚗" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Véhicules" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnCatalogue" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="catalogue">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="📋" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Catalogue" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <!-- New Location - Same Style as Others -->
                <Button fx:id="btnNewLocation" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="newLocation">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="➕" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Nouvelle Location" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnLocations" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="locations">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="📅" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Locations" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnPaiements" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="paiements">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="💰" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Paiements" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnUserManagement" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" prefHeight="44.0" style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';" userData="userManagement">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="⚙️" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Utilisateurs" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>
            </VBox>

            <VBox minHeight="-Infinity" minWidth="-Infinity" prefHeight="118.0" prefWidth="264.0" spacing="12" style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-padding: 16; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 12, 0, 0, 4);">
                <HBox alignment="CENTER_LEFT" spacing="12">
                    <StackPane>
                        <Circle fill="rgba(255,255,255,0.2)" radius="24" />
                        <Circle fill="rgba(255,255,255,0.9)" radius="20" />
                        <Label fx:id="lblUserName" style="-fx-text-fill: #667eea; -fx-font-size: 14px; -fx-font-weight: bold;" text="AD" />
                    </StackPane>
                    <VBox spacing="2" HBox.hgrow="ALWAYS">
                        <Label style="-fx-text-fill: white; -fx-font-size: 15px; -fx-font-weight: 600;" text="Administrateur" />
                        <Label fx:id="lblUserRole" style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 12px;" text="<EMAIL>" />
                    </VBox>
                </HBox>

                <!-- Logout Button -->
                <Button fx:id="btnLogout" maxWidth="Infinity" onAction="#handleLogout" style="-fx-background-color: rgba(255,255,255,0.15); -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: 500; -fx-cursor: hand; -fx-border-color: rgba(255,255,255,0.2); -fx-border-width: 1; -fx-border-radius: 10;">
                    <graphic>
                        <HBox alignment="CENTER" spacing="8">
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px;" text="🚪" />
                            <Label style="-fx-text-fill: white; -fx-font-size: 13px; -fx-font-weight: 500;" text="Déconnexion" />
                        </HBox>
                    </graphic>
                </Button>
   
               <Region VBox.vgrow="ALWAYS" />
            </VBox>
        </VBox>
    </left>

    <!-- MAIN CONTENT AREA -->
    <center>
        <StackPane fx:id="contentPane" style="-fx-background-color: #f8fafc; -fx-padding: 24;" />
    </center>

    <!-- CLEAN TOP BAR -->
    <top>
        <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 4, 0, 0, 1); -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;">
            <padding>
                <Insets bottom="16" left="24" right="24" top="16" />
            </padding>

            <VBox spacing="2">
                <Label fx:id="lblPageTitle" style="-fx-text-fill: #1e293b; -fx-font-size: 24px; -fx-font-weight: 700;" text="Tableau de Bord" />
                <Label fx:id="lblPageSubtitle" style="-fx-text-fill: #64748b; -fx-font-size: 14px;" text="Bienvenue dans votre espace d'administration" />
            </VBox>

            <Region HBox.hgrow="ALWAYS" />

            <HBox alignment="CENTER_RIGHT" spacing="12">
                <Button fx:id="btnNotifications" onAction="#handleNotifications" style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand; -fx-border-color: transparent;">
                    <graphic>
                        <HBox alignment="CENTER" spacing="6">
                            <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b;" text="🔔" />
                            <Label fx:id="lblNotificationCount" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 2 6; -fx-font-size: 10px; -fx-font-weight: bold; -fx-min-width: 18; -fx-alignment: center;" text="3" visible="true" />
                        </HBox>
                    </graphic>
                </Button>

                <MenuButton fx:id="menuProfile" style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-cursor: hand;" text="Profil">
                    <graphic>
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b;" text="👤" />
                    </graphic>
                    <items>
                        <MenuItem onAction="#handleViewProfile" text="Voir Profil" />
                        <MenuItem onAction="#handleAccountSettings" text="Paramètres du compte" />
                        <SeparatorMenuItem />
                        <MenuItem onAction="#handleHelp" text="Aide/Support" />
                    </items>
                </MenuButton>
            </HBox>
        </HBox>
    </top>
</BorderPane>
