<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: #f8fafc; -fx-background-color: #f8fafc;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueController">
    <content>
        <VBox prefHeight="1200" spacing="25.0" style="-fx-background-color: #f8fafc; -fx-padding: 30;">
            <padding><Insets bottom="50.0" /></padding>
            
            <!-- Header Section -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="8">
                        <Label style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="📊 Historique Détaillé" />
                        <Label fx:id="vehicleInfoLabel" style="-fx-font-size: 16px; -fx-text-fill: #64748b;" text="Chargement des informations du véhicule..." />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <VBox alignment="CENTER_RIGHT" spacing="10">
                        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600;" text="📊 Exporter" />
                        <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600;" text="🔄 Actualiser" />
                    </VBox>
                </HBox>
            </VBox>
            
            <!-- Statistics Cards -->
            <HBox alignment="CENTER" spacing="20">
                <VBox alignment="CENTER" spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;">
                    <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="📈 Total Locations" />
                    <Label fx:id="totalLocationsLabel" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="0" />
                    <Label fx:id="totalLocationsSubLabel" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" text="locations enregistrées" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;">
                    <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="💰 Revenus Total" />
                    <Label fx:id="totalRevenueLabel" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #059669;" text="0.00 €" />
                    <Label fx:id="avgRevenueLabel" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" text="Moyenne: 0.00 €" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;">
                    <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="📅 Jours Loués" />
                    <Label fx:id="totalDaysLabel" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #d97706;" text="0" />
                    <Label fx:id="avgDaysLabel" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" text="Moyenne: 0 jours" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="10" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 200;">
                    <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="⭐ Taux Utilisation" />
                    <Label fx:id="utilizationRateLabel" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #8b5cf6;" text="0%" />
                    <Label fx:id="statusLabel" style="-fx-font-size: 12px; -fx-text-fill: #9ca3af;" text="Statut: Disponible" />
                </VBox>
            </HBox>
            
            <!-- Performance Indicators -->
            <HBox alignment="CENTER" spacing="15">
                <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;">
                    <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="🏆 Meilleur Mois" />
                    <Label fx:id="bestMonthLabel" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="N/A" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;">
                    <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="📊 Durée Moyenne" />
                    <Label fx:id="avgDurationLabel" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="0 jours" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;">
                    <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="🎯 Taux Retour" />
                    <Label fx:id="returnRateLabel" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="0%" />
                </VBox>
                
                <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2); -fx-min-width: 150;">
                    <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;" text="💎 Client VIP" />
                    <Label fx:id="topClientLabel" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="N/A" />
                </VBox>
            </HBox>
            
            <!-- Activity Table Section -->
            <VBox spacing="20" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);">
                <HBox alignment="CENTER_LEFT" spacing="20">
                    <VBox spacing="5">
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="🕒 Historique des Locations" />
                        <Label fx:id="tableSubtitleLabel" style="-fx-font-size: 14px; -fx-text-fill: #64748b;" text="Toutes les locations de ce véhicule" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <VBox alignment="CENTER_RIGHT" spacing="10">
                        <ComboBox fx:id="filterComboBox" onAction="#handleFilterChange" promptText="Filtrer par statut" style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 13px;" />
                        <Label fx:id="tableCountLabel" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="0 locations trouvées" />
                    </VBox>
                </HBox>
                
                <TableView fx:id="historyTable" prefHeight="44.0" prefWidth="1047.0" style="-fx-background-radius: 8;">
                    <columns>
                        <TableColumn fx:id="idColumn" prefWidth="60" text="ID" />
                        <TableColumn fx:id="clientColumn" prefWidth="160" text="Client" />
                        <TableColumn fx:id="dateDebutColumn" prefWidth="120" text="Date Début" />
                        <TableColumn fx:id="dateFinColumn" prefWidth="120" text="Date Fin" />
                        <TableColumn fx:id="durationColumn" prefWidth="90" text="Durée" />
                        <TableColumn fx:id="statusColumn" prefWidth="110" text="Statut" />
                        <TableColumn fx:id="priceColumn" prefWidth="100" text="Prix Total" />
                        <TableColumn fx:id="actionsColumn" prefWidth="273.0" text="Actions" />
                    </columns>
                </TableView>
                
                <!-- Table Summary -->
                <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-padding: 10 0 0 0;">
                    <Label fx:id="tableSummaryLabel" style="-fx-font-size: 13px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" text="Résumé: 0 locations • 0.00 € total • 0 jours" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Button onAction="#handleExportSelection" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="📄 Exporter Sélection" />
                </HBox>
            </VBox>

            <!-- Detailed Information Panel -->
            <VBox fx:id="detailPanel" spacing="20" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3);" visible="false">
                <HBox alignment="CENTER_LEFT" spacing="15">
                    <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a365d;" text="📋 Détails de la Location Sélectionnée" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Button onAction="#hideDetailPanel" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="✖ Masquer" />
                </HBox>

                <GridPane hgap="25" style="-fx-padding: 10 0;" vgap="15">
                    <columnConstraints>
                        <ColumnConstraints minWidth="120" />
                        <ColumnConstraints minWidth="200" />
                        <ColumnConstraints minWidth="120" />
                        <ColumnConstraints minWidth="200" />
                    </columnConstraints>

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="👤 Client:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                    <Label fx:id="detailClientLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="📞 Téléphone:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                    <Label fx:id="detailPhoneLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="📅 Date début:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                    <Label fx:id="detailStartDateLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="📅 Date fin prévue:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                    <Label fx:id="detailEndDateLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="🏁 Date retour réelle:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                    <Label fx:id="detailActualEndLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="⏱️ Durée totale:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                    <Label fx:id="detailDurationLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px;" GridPane.columnIndex="3" GridPane.rowIndex="2" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="💰 Prix total:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                    <Label fx:id="detailPriceLabel" style="-fx-text-fill: #059669; -fx-font-weight: bold; -fx-font-size: 16px;" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                    <Label style="-fx-font-weight: bold; -fx-text-fill: #374151; -fx-font-size: 14px;" text="📊 Statut:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                    <Label fx:id="detailStatusLabel" style="-fx-text-fill: #1f2937; -fx-font-size: 14px; -fx-font-weight: bold;" GridPane.columnIndex="3" GridPane.rowIndex="3" />
               <rowConstraints>
                  <RowConstraints />
                  <RowConstraints />
                  <RowConstraints />
                  <RowConstraints />
               </rowConstraints>
                </GridPane>

                <!-- Additional Actions -->
                <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 15 0 0 0;">
                    <Button onAction="#handleContactClient" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" text="📧 Contacter Client" />
                    <Button onAction="#handleGenerateInvoice" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" text="📄 Générer Facture" />
                    <Button onAction="#handleEditLocation" style="-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" text="📝 Modifier" />
                </HBox>
            </VBox>

            <!-- Action Buttons -->
            <HBox alignment="CENTER" spacing="15">
                <Button onAction="#handleViewCalendar" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" text="📅 Voir Calendrier" />
                <Button onAction="#handleFullReport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" text="📊 Rapport Complet" />
                <Button onAction="#handleClose" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-size: 14px; -fx-font-weight: 600;" text="❌ Fermer" />
            </HBox>

        </VBox>
    </content>
</ScrollPane>
