<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import javafx.collections.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" prefHeight="1614.0" prefWidth="1560.0" style="-fx-background: transparent; -fx-background-color: transparent;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.LocationCreateController">
    <content>
        <VBox prefHeight="1736.0" prefWidth="1618.0" spacing="0" style="-fx-background-color: #f8fafc;">
    <padding><Insets bottom="200.0" left="25.0" right="25.0" top="25.0" /></padding>

            <!-- Modern Header with Gradient -->
            <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-background-color: linear-gradient(to right, #1a3c40, #2d5561); -fx-padding: 28 32; -fx-background-radius: 0 0 16 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 10, 0, 0, 3);">
                <VBox spacing="4">
                    <Label style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: white; -fx-font-family: 'Segoe UI';" text="Nouvelle Location" />
                    <Label style="-fx-font-size: 16px; -fx-text-fill: rgba(255,255,255,0.8); -fx-font-family: 'Segoe UI';" text="Créez une nouvelle réservation ou location" />
                </VBox>
                <Region HBox.hgrow="ALWAYS" />
                <Label fx:id="lblCurrentDate" style="-fx-font-size: 14px; -fx-text-fill: rgba(255,255,255,0.8); -fx-font-family: 'Segoe UI';" text="Date: " />
            </HBox>

            <!-- Main Content Container - Now using HBox for side-by-side layout -->
            <HBox alignment="TOP_LEFT" minHeight="-Infinity" minWidth="-Infinity" prefHeight="1398.0" prefWidth="1288.0" spacing="24" style="-fx-padding: 32;">

                <!-- Left Column (Vehicle and Client Selection) -->
                <VBox prefHeight="1334.0" prefWidth="835.0" spacing="24" style="-fx-min-width: 600;">
                    <!-- Vehicle Selection Card -->
                    <VBox prefHeight="541.0" prefWidth="781.0" spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="🚗" />
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Sélection du Véhicule" />
                        </HBox>

                        <Separator style="-fx-background-color: #e5e7eb;" />

                        <HBox alignment="TOP_LEFT" spacing="24">
                            <!-- Vehicle Image with Card Styling -->
                            <VBox alignment="CENTER" prefHeight="447.0" prefWidth="317.0" spacing="8" style="-fx-background-color: #f9fafb; -fx-background-radius: 12; -fx-padding: 16; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
                                <ImageView fx:id="vehiculeImage" fitHeight="229.0" fitWidth="314.0" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 1);" />
                                <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b;" text="Aperçu du véhicule" />
                            </VBox>

                            <!-- Vehicle Selection Controls -->
                            <VBox spacing="16" VBox.vgrow="ALWAYS">
                                <VBox spacing="8">
                                    <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Rechercher un véhicule" />
                                    <HBox alignment="CENTER_LEFT" spacing="8">
                                        <TextField fx:id="vehiculeSearchField" promptText="Marque, modèle ou immatriculation..." style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-pref-width: 240;" />
                                        <Button style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 8; -fx-padding: 12 16; -fx-font-size: 14px;" text="Rechercher" />
                                    </HBox>
                                </VBox>

                                <VBox spacing="8">
                                    <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Sélectionner le véhicule" />
                                    <ComboBox fx:id="vehiculeComboBox" promptText="Choisissez un véhicule" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-pref-width: 240;" />
                                </VBox>

                                <!-- Vehicle Details Card -->
                                <VBox spacing="8" style="-fx-background-color: #f8fafc; -fx-padding: 16; -fx-background-radius: 12; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-border-width: 1;">
                                    <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Détails du véhicule" />
                                    <Label fx:id="vehiculeDetailsLabel" style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-line-spacing: 4;" wrapText="true" />
                                    <HBox spacing="8" style="-fx-padding: 8 0 0 0;">
                                        <Label style="-fx-font-size: 13px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Tarif journalier:" />
                                        <Label fx:id="dailyRateLabel" style="-fx-font-size: 13px; -fx-text-fill: #3b82f6; -fx-font-weight: bold;" text="0 DH" />
                                    </HBox>
                                </VBox>
                            </VBox>
                        </HBox>
                    </VBox>

                    <!-- Client Selection Card -->
                    <VBox prefHeight="733.0" prefWidth="600.0" spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="👤" />
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Sélection du Client" />
                        </HBox>

                        <Separator style="-fx-background-color: #e5e7eb;" />

                        <VBox prefHeight="718.0" prefWidth="552.0" spacing="16">
                            <VBox spacing="8">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Rechercher un client existant" />
                                <HBox alignment="CENTER_LEFT" spacing="8">
                                    <TextField fx:id="clientSearchField" promptText="Nom, prénom, email ou téléphone..." style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-pref-width: 240;" />
                                    <Button style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 8; -fx-padding: 12 16; -fx-font-size: 14px;" text="Rechercher" />
                                </HBox>
                            </VBox>

                            <VBox spacing="8">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Sélectionner le client" />
                                <ComboBox fx:id="clientComboBox" promptText="Choisissez un client" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-pref-width: 240;" />
                            </VBox>

                            <HBox alignment="CENTER_LEFT" spacing="12">
                                <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b;" text="ou" />
                                <Button onAction="#handleShowNewClientForm" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: bold;" text="➕ Créer un nouveau client" />
                            </HBox>

                            <!-- New Client Form -->
                            <VBox fx:id="newClientForm" spacing="16" style="-fx-background-color: #f0fdf4; -fx-padding: 20; -fx-background-radius: 12; -fx-border-color: #bbf7d0; -fx-border-radius: 12; -fx-border-width: 1;" visible="false">
                                <HBox alignment="CENTER_LEFT" spacing="8">
                                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #166534;" text="🆕" />
                                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #166534;" text="Nouveau Client" />
                                </HBox>

                                <GridPane hgap="16" vgap="12">
                                    <TextField fx:id="newNomField" promptText="Nom" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #d1fae5; -fx-border-radius: 8;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                    <TextField fx:id="newPrenomField" promptText="Prénom" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #d1fae5; -fx-border-radius: 8;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                    <TextField fx:id="newEmailField" promptText="Email" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #d1fae5; -fx-border-radius: 8;" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                    <TextField fx:id="newTelField" promptText="Téléphone" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #d1fae5; -fx-border-radius: 8;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           <columnConstraints>
                              <ColumnConstraints />
                              <ColumnConstraints />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints />
                              <RowConstraints />
                           </rowConstraints>
                                </GridPane>

                                <HBox alignment="CENTER_RIGHT" spacing="12">
                                    <Button onAction="#handleCancelNewClient" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px;" text="Annuler" />
                                    <Button onAction="#handleSaveNewClient" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: bold;" text="Enregistrer" />
                                </HBox>
                            </VBox>
                        </VBox>
                    </VBox>
                </VBox>

                <!-- Right Column (Rental Details and Summary) -->
                <VBox spacing="24" style="-fx-min-width: 600;">
                    <!-- Rental Details Card -->
                    <VBox spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="📋" />
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Détails de la Location" />
                        </HBox>

                        <Separator style="-fx-background-color: #e5e7eb;" />

                        <GridPane hgap="20" vgap="16">
                            <!-- Contract Info -->
                            <VBox spacing="8" GridPane.columnIndex="0" GridPane.rowIndex="0">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Numéro de contrat" />
                                <TextField fx:id="contractNumberField" editable="false" promptText="Auto-généré" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: #f3f4f6; -fx-border-color: #d1d5db; -fx-border-radius: 8;" />
                            </VBox>

                            <VBox spacing="8" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Agent responsable" />
                                <TextField fx:id="agentField" editable="false" promptText="Auto-rempli" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: #f3f4f6; -fx-border-color: #d1d5db; -fx-border-radius: 8;" />
                            </VBox>

                            <!-- Dates -->
                            <VBox spacing="8" GridPane.columnIndex="0" GridPane.rowIndex="1">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Date de début" />
                                <DatePicker fx:id="dateDebutPicker" promptText="Sélectionner la date" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>

                            <VBox spacing="8" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Date de fin prévue" />
                                <DatePicker fx:id="dateFinPicker" promptText="Sélectionner la date" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>

                            <!-- Locations -->
                            <VBox spacing="8" GridPane.columnIndex="0" GridPane.rowIndex="2">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Lieu de prise en charge" />
                                <TextField fx:id="pickupLocationField" promptText="Adresse de départ" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;" />
                            </VBox>

                            <VBox spacing="8" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Lieu de retour" />
                                <TextField fx:id="deliveryLocationField" promptText="Adresse de retour" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;" />
                            </VBox>

                            <!-- Insurance and Payment -->
                            <VBox spacing="8" GridPane.columnIndex="0" GridPane.rowIndex="3">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Type d'assurance" />
                                <ComboBox fx:id="insuranceTypeComboBox" promptText="Sélectionner" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Standard" />
                                            <String fx:value="Tous risques" />
                                            <String fx:value="Premium" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>

                            <VBox spacing="8" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Méthode de paiement" />
                                <ComboBox fx:id="paiementComboBox" promptText="Sélectionner" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Espèces" />
                                            <String fx:value="Carte bancaire" />
                                            <String fx:value="Virement" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>

                            <!-- Fuel Policy -->
                            <VBox spacing="8" GridPane.columnIndex="0" GridPane.columnSpan="2" GridPane.rowIndex="4">
                                <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Politique carburant" />
                                <ComboBox fx:id="fuelPolicyComboBox" promptText="Sélectionner la politique carburant" style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Plein à plein" />
                                            <String fx:value="Plein à vide" />
                                            <String fx:value="Retour identique" />
                                        </FXCollections>
                                    </items>
                                </ComboBox>
                            </VBox>
                     <columnConstraints>
                        <ColumnConstraints />
                        <ColumnConstraints />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                     </rowConstraints>
                        </GridPane>

                        <!-- Options -->
                        <VBox spacing="12" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
                            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Options supplémentaires" />
                            <GridPane hgap="20" vgap="8">
                                <CheckBox fx:id="optionAssurance" style="-fx-font-size: 14px; -fx-text-fill: #374151;" text="Assurance supplémentaire (+50 DH)" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                <CheckBox fx:id="optionGPS" style="-fx-font-size: 14px; -fx-text-fill: #374151;" text="GPS (+20 DH)" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                <CheckBox fx:id="optionSiegeBebe" style="-fx-font-size: 14px; -fx-text-fill: #374151;" text="Siège bébé (+15 DH)" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                <CheckBox fx:id="optionAdditionalDriver" style="-fx-font-size: 14px; -fx-text-fill: #374151;" text="Conducteur additionnel (+30 DH)" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <columnConstraints>
                           <ColumnConstraints />
                           <ColumnConstraints />
                        </columnConstraints>
                        <rowConstraints>
                           <RowConstraints />
                           <RowConstraints />
                        </rowConstraints>
                            </GridPane>
                        </VBox>

                        <!-- Notes -->
                        <VBox spacing="8">
                            <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #374151;" text="Notes supplémentaires" />
                            <TextArea fx:id="notesField" prefRowCount="3" promptText="Ajoutez des notes ou commentaires..." style="-fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-background-color: white; -fx-border-color: #e5e7eb; -fx-border-radius: 8;" />
                        </VBox>
                    </VBox>

                    <!-- Summary and Actions Card -->
                    <VBox prefHeight="388.0" prefWidth="600.0" spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="💰" />
                            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Résumé et Actions" />
                        </HBox>

                        <Separator style="-fx-background-color: #e5e7eb;" />

                        <VBox spacing="16">
                            <!-- Price Summary -->
                            <VBox spacing="12" style="-fx-background-color: #f0f9ff; -fx-padding: 24; -fx-background-radius: 12; -fx-border-color: #bae6fd; -fx-border-radius: 12; -fx-border-width: 1;">
                                <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #0369a1;" text="Total à payer" />
                                <HBox alignment="BASELINE_LEFT" spacing="4">
                                    <Label fx:id="totalPrixLabel" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="0" />
                                    <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="DH" />
                                </HBox>
                                <HBox alignment="CENTER_LEFT" spacing="8">
                                    <Label fx:id="durationLabel" style="-fx-font-size: 13px; -fx-text-fill: #64748b;" text="0 jours" />
                                    <Label fx:id="dailyRateCalculation" style="-fx-font-size: 13px; -fx-text-fill: #64748b;" text="(0 DH × 0 jours)" />
                                </HBox>
                            </VBox>

                            <!-- Action Buttons -->
                            <HBox alignment="CENTER" spacing="12">
                                <Button onAction="#handleExportContrat" style="-fx-background-color: #e5e7eb; -fx-text-fill: #374151; -fx-background-radius: 8; -fx-padding: 14 24; -fx-font-size: 15px; -fx-font-weight: bold; -fx-min-width: 150;" text="📄 Exporter" />
                                <Button onAction="#handleConfirm" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 14 24; -fx-font-size: 15px; -fx-font-weight: bold; -fx-min-width: 150;" text="✅ Confirmer" />
                                <Button onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-padding: 14 24; -fx-font-size: 14px; -fx-min-width: 150;" text="❌ Annuler" />
                            </HBox>
                        </VBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>
