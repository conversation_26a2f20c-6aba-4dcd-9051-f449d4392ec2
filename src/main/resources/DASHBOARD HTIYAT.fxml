<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="controller.DashboardController"
            prefHeight="900.0" prefWidth="1400.0"
            style="-fx-background-color: #f8fafc; -fx-font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;">

    <!-- MODERN CLEAN SIDEBAR -->
    <left>
        <VBox fx:id="sidebar" prefHeight="900.0" prefWidth="280.0"
              style="-fx-background-color: linear-gradient(to bottom, #4f46e5, #7c3aed); -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 15, 0, 0, 0);">

            <!-- Header with Logo -->
            <HBox alignment="CENTER_LEFT" prefHeight="80.0" spacing="12.0"
                  style="-fx-background-color: rgba(255,255,255,0.1); -fx-padding: 0 20;">
                <StackPane>
                    <Circle fill="white" opacity="0.9" radius="20" />
                    <Label style="-fx-font-size: 16px;" text="🚗" />
                </StackPane>
                <VBox>
                    <Label style="-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';"
                           text="LocationV1" />
                    <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 12px; -fx-font-family: 'Segoe UI';"
                           text="Système de gestion" />
                </VBox>
            </HBox>

            <!-- User Profile Section -->
            <VBox alignment="CENTER" spacing="12.0"
                  style="-fx-padding: 25 20 25 20; -fx-background-color: rgba(255,255,255,0.05);">
                <StackPane>
                    <Circle fill="white" opacity="0.9" radius="35" />
                    <Label fx:id="lblUserName" style="-fx-text-fill: #4f46e5; -fx-font-size: 16px; -fx-font-weight: bold;"
                           text="AD" />
                </StackPane>
                <VBox alignment="CENTER" spacing="4">
                    <Label style="-fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';"
                           text="Administrateur" />
                    <Label fx:id="lblUserRole"
                           style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 12px; -fx-font-family: 'Segoe UI'; -fx-background-color: rgba(255,255,255,0.1); -fx-padding: 4 12; -fx-background-radius: 15;"
                           text="<EMAIL>" />
                </VBox>
            </VBox>

            <!-- Navigation Menu -->
            <VBox spacing="6.0" style="-fx-padding: 20 15 15 15;">
                <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 11px; -fx-font-weight: bold; -fx-padding: 0 0 8 10; -fx-font-family: 'Segoe UI';"
                       text="NAVIGATION" />

                <!-- Dashboard - Active -->
                <Button fx:id="btnDashboard" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="dashboard" prefHeight="44.0"
                        style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="white" opacity="0.9" radius="8" />
                                <Label style="-fx-text-fill: #4f46e5; -fx-font-size: 10px;" text="📊" />
                            </StackPane>
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Tableau de bord" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnClients" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="clients" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="👥" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Clients" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnVehicules" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="vehicules" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="🚗" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Véhicules" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnCatalogue" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="catalogue" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="📋" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Catalogue" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <!-- Actions Section -->
                <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 11px; -fx-font-weight: bold; -fx-padding: 15 0 8 10; -fx-font-family: 'Segoe UI';"
                       text="ACTIONS" />

                <!-- Primary Action - New Location -->
                <Button fx:id="btnNewLocation" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="newLocation" prefHeight="44.0"
                        style="-fx-background-color: rgba(16, 185, 129, 0.8); -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="white" opacity="0.9" radius="8" />
                                <Label style="-fx-text-fill: #10b981; -fx-font-size: 10px;" text="➕" />
                            </StackPane>
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Nouvelle Location" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnLocations" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="locations" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="📅" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Locations" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <Button fx:id="btnPaiements" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="paiements" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="💰" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Paiements" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>

                <!-- Administration Section -->
                <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 11px; -fx-font-weight: bold; -fx-padding: 15 0 8 10; -fx-font-family: 'Segoe UI';"
                       text="ADMINISTRATION" />

                <Button fx:id="btnUserManagement" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#navigateToPage" userData="userManagement" prefHeight="44.0"
                        style="-fx-background-color: transparent; -fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="rgba(255,255,255,0.5)" strokeWidth="1" />
                                <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 10px;" text="⚙️" />
                            </StackPane>
                            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Utilisateurs" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>
            </VBox>

            <Region VBox.vgrow="ALWAYS" />

            <!-- Logout Button -->
            <VBox style="-fx-padding: 20 15 25 15;">
                <Button fx:id="btnLogout" alignment="CENTER_LEFT" maxWidth="Infinity"
                        onAction="#handleLogout" prefHeight="44.0"
                        style="-fx-background-color: rgba(255,255,255,0.15); -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 10; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <StackPane>
                                <Circle fill="transparent" radius="8" stroke="white" strokeWidth="1" />
                                <Label style="-fx-text-fill: white; -fx-font-size: 10px;" text="🚪" />
                            </StackPane>
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="Déconnexion" />
                        </HBox>
                    </graphic>
                    <padding><Insets left="15" right="15" /></padding>
                </Button>
            </VBox>
        </VBox>
    </left>

    <!-- MAIN CONTENT AREA -->
    <center>
        <StackPane fx:id="contentPane" style="-fx-background-color: #f8fafc; -fx-padding: 24;" />
    </center>

    <!-- TOP BAR -->
    <top>
        <HBox alignment="CENTER_LEFT" spacing="20"
              style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 4, 0, 0, 1); -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;">
            <padding>
                <Insets top="16" right="24" bottom="16" left="24"/>
            </padding>

            <VBox spacing="2">
                <Label fx:id="lblPageTitle" text="Tableau de Bord"
                       style="-fx-text-fill: #1e293b; -fx-font-size: 24px; -fx-font-weight: 700;"/>
                <Label fx:id="lblPageSubtitle" text="Bienvenue dans votre espace d'administration"
                       style="-fx-text-fill: #64748b; -fx-font-size: 14px;"/>
            </VBox>

            <Region HBox.hgrow="ALWAYS"/>

            <HBox alignment="CENTER_RIGHT" spacing="12">
                <Button fx:id="btnNotifications" onAction="#handleNotifications"
                        style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand; -fx-border-color: transparent;">
                    <graphic>
                        <HBox alignment="CENTER" spacing="6">
                            <Label text="🔔" style="-fx-font-size: 14px; -fx-text-fill: #64748b;"/>
                            <Label fx:id="lblNotificationCount" text="3" visible="true"
                                   style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 2 6; -fx-font-size: 10px; -fx-font-weight: bold; -fx-min-width: 18; -fx-alignment: center;"/>
                        </HBox>
                    </graphic>
                </Button>

                <MenuButton fx:id="menuProfile" text="Profil"
                            style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-cursor: hand;">
                    <graphic>
                        <Label text="👤" style="-fx-font-size: 14px; -fx-text-fill: #64748b;"/>
                    </graphic>
                    <items>
                        <MenuItem text="Voir Profil" onAction="#handleViewProfile"/>
                        <MenuItem text="Paramètres du compte" onAction="#handleAccountSettings"/>
                        <SeparatorMenuItem/>
                        <MenuItem text="Aide/Support" onAction="#handleHelp"/>
                    </items>
                </MenuButton>
            </HBox>
        </HBox>
    </top>
</BorderPane>