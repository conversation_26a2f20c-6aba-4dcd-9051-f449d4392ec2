<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">***********************************************************************************************************************</property>
        <property name="hibernate.connection.username">locationapp</property>
        <property name="hibernate.connection.password">location123</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL8Dialect</property>
        <property name="hibernate.hbm2ddl.auto">update</property>
        <property name="show_sql">true</property>
        <!-- Mapping des entités -->
        <mapping class="model.Vehicule"/>
        <mapping class="model.Client"/>
        <mapping class="model.Location"/>
        <mapping class="model.Paiement"/>
        <mapping class="model.Admin"/>
        <mapping class="model.Agent"/>
        <mapping class="model.VehicleMaintenance"/>
        <mapping class="model.VehicleFailure"/>
    </session-factory>
</hibernate-configuration> 