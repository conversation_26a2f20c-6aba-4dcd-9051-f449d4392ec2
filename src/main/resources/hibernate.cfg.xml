<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">***********************************************************************************************************************</property>
        <property name="hibernate.connection.username">locationapp</property>
        <property name="hibernate.connection.password">location123</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQLDialect</property>
        <property name="hibernate.hbm2ddl.auto">update</property>
        <property name="show_sql">false</property>

        <!-- Performance Optimizations -->
        <property name="hibernate.jdbc.batch_size">20</property>
        <property name="hibernate.order_inserts">true</property>
        <property name="hibernate.order_updates">true</property>
        <property name="hibernate.jdbc.batch_versioned_data">true</property>
        <property name="hibernate.connection.pool_size">10</property>
        <property name="hibernate.jdbc.fetch_size">50</property>
        <property name="hibernate.default_batch_fetch_size">16</property>
        <!-- Mapping des entités -->
        <mapping class="model.Vehicule"/>
        <mapping class="model.Client"/>
        <mapping class="model.Location"/>
        <mapping class="model.Paiement"/>
        <mapping class="model.User"/>
        <mapping class="model.Admin"/>
        <mapping class="model.Agent"/>
        <mapping class="model.VehicleMaintenance"/>
        <mapping class="model.VehicleFailure"/>
        <mapping class="model.Notification"/>
        <mapping class="model.NotificationSettings"/>
    </session-factory>
</hibernate-configuration>