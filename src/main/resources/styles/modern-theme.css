/* Modern LocationV1 Theme */

/* Root Variables for Consistent Design */
.root {
    /* Primary Colors */
    -fx-primary-color: #2563eb;
    -fx-primary-dark: #1d4ed8;
    -fx-primary-light: #3b82f6;
    
    /* Secondary Colors */
    -fx-secondary-color: #10b981;
    -fx-secondary-dark: #059669;
    -fx-secondary-light: #34d399;
    
    /* Neutral Colors */
    -fx-background-primary: #ffffff;
    -fx-background-secondary: #f8fafc;
    -fx-background-tertiary: #f1f5f9;
    -fx-surface: #ffffff;
    -fx-surface-variant: #f8fafc;
    
    /* Text Colors */
    -fx-text-primary: #0f172a;
    -fx-text-secondary: #475569;
    -fx-text-tertiary: #64748b;
    -fx-text-inverse: #ffffff;
    
    /* Border Colors */
    -fx-border-light: #e2e8f0;
    -fx-border-medium: #cbd5e1;
    -fx-border-dark: #94a3b8;
    
    /* Status Colors */
    -fx-success: #10b981;
    -fx-warning: #f59e0b;
    -fx-error: #ef4444;
    -fx-info: #3b82f6;
    
    /* Shadows */
    -fx-shadow-light: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
    -fx-shadow-medium: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-shadow-heavy: dropshadow(gaussian, rgba(0,0,0,0.15), 16, 0, 0, 4);
    
    /* Border Radius */
    -fx-radius-small: 6px;
    -fx-radius-medium: 8px;
    -fx-radius-large: 12px;
    -fx-radius-xl: 16px;
    
    /* Typography */
    -fx-font-family-primary: "Segoe UI", system-ui, -apple-system, sans-serif;
    -fx-font-size-xs: 11px;
    -fx-font-size-sm: 12px;
    -fx-font-size-base: 14px;
    -fx-font-size-lg: 16px;
    -fx-font-size-xl: 18px;
    -fx-font-size-2xl: 20px;
    -fx-font-size-3xl: 24px;
    -fx-font-size-4xl: 28px;
}

/* Modern Button Styles */
.btn-primary {
    -fx-background-color: linear-gradient(to bottom, -fx-primary-color, -fx-primary-dark);
    -fx-text-fill: -fx-text-inverse;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-font-weight: 600;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 20;
    -fx-cursor: hand;
    -fx-effect: -fx-shadow-light;
    -fx-border-width: 0;
}

.btn-primary:hover {
    -fx-background-color: linear-gradient(to bottom, -fx-primary-dark, #1e40af);
    -fx-effect: -fx-shadow-medium;
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
}

.btn-primary:pressed {
    -fx-background-color: -fx-primary-dark;
    -fx-effect: -fx-shadow-light;
    -fx-scale-y: 0.98;
    -fx-scale-x: 0.98;
}

.btn-secondary {
    -fx-background-color: -fx-background-tertiary;
    -fx-text-fill: -fx-text-secondary;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-font-weight: 500;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 20;
    -fx-cursor: hand;
    -fx-border-color: -fx-border-light;
    -fx-border-width: 1;
    -fx-border-radius: -fx-radius-medium;
}

.btn-secondary:hover {
    -fx-background-color: -fx-background-secondary;
    -fx-border-color: -fx-border-medium;
    -fx-effect: -fx-shadow-light;
}

.btn-success {
    -fx-background-color: linear-gradient(to bottom, -fx-success, -fx-secondary-dark);
    -fx-text-fill: -fx-text-inverse;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-font-weight: 600;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 20;
    -fx-cursor: hand;
    -fx-effect: -fx-shadow-light;
}

.btn-danger {
    -fx-background-color: linear-gradient(to bottom, -fx-error, #dc2626);
    -fx-text-fill: -fx-text-inverse;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-font-weight: 600;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 20;
    -fx-cursor: hand;
    -fx-effect: -fx-shadow-light;
}

/* Modern Input Styles */
.modern-text-field {
    -fx-background-color: -fx-surface;
    -fx-border-color: -fx-border-light;
    -fx-border-width: 1.5;
    -fx-border-radius: -fx-radius-medium;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 16;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-text-fill: -fx-text-primary;
    -fx-prompt-text-fill: -fx-text-tertiary;
}

.modern-text-field:focused {
    -fx-border-color: -fx-primary-color;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(37,99,235,0.2), 0, 0, 0, 3);
}

/* Modern Card Styles */
.modern-card {
    -fx-background-color: -fx-surface;
    -fx-background-radius: -fx-radius-large;
    -fx-effect: -fx-shadow-medium;
    -fx-padding: 24;
}

.modern-card-header {
    -fx-background-color: -fx-surface;
    -fx-background-radius: -fx-radius-large -fx-radius-large 0 0;
    -fx-padding: 20 24;
    -fx-border-color: -fx-border-light;
    -fx-border-width: 0 0 1 0;
}

/* Modern Table Styles */
.modern-table-view {
    -fx-background-color: -fx-surface;
    -fx-border-color: -fx-border-light;
    -fx-border-width: 1;
    -fx-border-radius: -fx-radius-large;
    -fx-background-radius: -fx-radius-large;
    -fx-effect: -fx-shadow-light;
}

.modern-table-view .column-header {
    -fx-background-color: -fx-background-tertiary;
    -fx-text-fill: -fx-text-secondary;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-sm;
    -fx-font-weight: 600;
    -fx-padding: 12 16;
}

.modern-table-view .table-row-cell {
    -fx-background-color: -fx-surface;
    -fx-text-fill: -fx-text-primary;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-sm;
    -fx-padding: 8 16;
}

.modern-table-view .table-row-cell:hover {
    -fx-background-color: -fx-background-secondary;
}

.modern-table-view .table-row-cell:selected {
    -fx-background-color: rgba(37,99,235,0.1);
    -fx-text-fill: -fx-text-primary;
}

/* Modern Navigation Styles */
.modern-nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: -fx-text-secondary;
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-font-weight: 500;
    -fx-background-radius: -fx-radius-medium;
    -fx-padding: 12 16;
    -fx-cursor: hand;
    -fx-alignment: CENTER_LEFT;
}

.modern-nav-button:hover {
    -fx-background-color: rgba(37,99,235,0.08);
    -fx-text-fill: -fx-primary-color;
}

.modern-nav-button.active {
    -fx-background-color: -fx-primary-color;
    -fx-text-fill: -fx-text-inverse;
    -fx-font-weight: 600;
}

/* Modern Sidebar */
.modern-sidebar {
    -fx-background-color: -fx-surface;
    -fx-padding: 24 16;
    -fx-effect: -fx-shadow-medium;
    -fx-border-color: -fx-border-light;
    -fx-border-width: 0 1 0 0;
}

/* Typography Classes */
.text-heading-1 {
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-4xl;
    -fx-font-weight: 700;
    -fx-text-fill: -fx-text-primary;
}

.text-heading-2 {
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-3xl;
    -fx-font-weight: 600;
    -fx-text-fill: -fx-text-primary;
}

.text-heading-3 {
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-xl;
    -fx-font-weight: 600;
    -fx-text-fill: -fx-text-primary;
}

.text-body {
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-base;
    -fx-text-fill: -fx-text-primary;
}

.text-caption {
    -fx-font-family: -fx-font-family-primary;
    -fx-font-size: -fx-font-size-sm;
    -fx-text-fill: -fx-text-tertiary;
}

/* Status Indicators */
.status-success {
    -fx-background-color: rgba(16,185,129,0.1);
    -fx-text-fill: -fx-success;
    -fx-background-radius: 20;
    -fx-padding: 4 12;
    -fx-font-size: -fx-font-size-xs;
    -fx-font-weight: 600;
}

.status-warning {
    -fx-background-color: rgba(245,158,11,0.1);
    -fx-text-fill: -fx-warning;
    -fx-background-radius: 20;
    -fx-padding: 4 12;
    -fx-font-size: -fx-font-size-xs;
    -fx-font-weight: 600;
}

.status-error {
    -fx-background-color: rgba(239,68,68,0.1);
    -fx-text-fill: -fx-error;
    -fx-background-radius: 20;
    -fx-padding: 4 12;
    -fx-font-size: -fx-font-size-xs;
    -fx-font-weight: 600;
}
