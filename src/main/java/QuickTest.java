import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Quick test launcher for specific views
 */
public class QuickTest extends Application {
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        // Initialize database first
        util.DatabaseInitializer.initialize();
        
        // Test the account settings view directly
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/account_settings.fxml"));
            Parent root = loader.load();
            
            primaryStage.setTitle("Test - Account Settings");
            primaryStage.setScene(new Scene(root, 800, 600));
            primaryStage.show();
            
            System.out.println("✓ Account settings view loaded successfully!");
            
        } catch (Exception e) {
            System.err.println("✗ Failed to load account settings view: " + e.getMessage());
            e.printStackTrace();
            
            // Fallback to login view
            Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            primaryStage.setTitle("LocationV12 - Login");
            primaryStage.setScene(new Scene(root, 500, 800));
            primaryStage.show();
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
