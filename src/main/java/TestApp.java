import util.DatabaseInitializer;
import dao.AdminDAO;
import dao.NotificationDAO;
import model.Admin;
import model.Notification;
import service.NotificationService;

import java.util.List;

/**
 * Simple test application to verify the system works
 */
public class TestApp {
    public static void main(String[] args) {
        System.out.println("=== Testing LocationV12 System ===");
        
        try {
            // Initialize database
            System.out.println("Initializing database...");
            DatabaseInitializer.initialize();
            System.out.println("✓ Database initialized successfully");
            
            // Test basic DAO operations
            AdminDAO adminDAO = new AdminDAO();
            List<Admin> admins = adminDAO.findAll();
            System.out.println("✓ Found " + admins.size() + " admin users");
            
            if (!admins.isEmpty()) {
                Admin admin = admins.get(0);
                System.out.println("✓ Admin user: " + admin.getUsername());
                
                // Test notification system
                NotificationService notificationService = NotificationService.getInstance();
                List<Notification> notifications = notificationService.getNotificationsForUser(admin);
                System.out.println("✓ Found " + notifications.size() + " notifications for admin");
                
                long unreadCount = notificationService.getUnreadCountForUser(admin);
                System.out.println("✓ Unread notifications: " + unreadCount);
            }
            
            System.out.println("✓ All tests passed! System is ready.");
            
        } catch (Exception e) {
            System.err.println("✗ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
