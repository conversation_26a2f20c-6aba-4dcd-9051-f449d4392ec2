package util;

import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;

import java.io.IOException;
import java.net.URL;

/**
 * Utility class for safe FXML loading and window management
 * Prevents "Location is not set" errors and provides consistent error handling
 */
public class FXMLUtil {
    
    /**
     * Safely loads an FXML file and returns the root node
     * @param resourcePath Path to the FXML file (e.g., "/view/example.fxml")
     * @param caller The calling class (for resource loading context)
     * @return FXMLLoader instance if successful, null if failed
     */
    public static FXMLLoader loadFXML(String resourcePath, Class<?> caller) {
        try {
            // Check if resource exists first
            URL fxmlUrl = caller.getResource(resourcePath);
            if (fxmlUrl == null) {
                showError("Erreur: Le fichier " + resourcePath + " est introuvable.");
                System.err.println("FXML file not found: " + resourcePath);
                return null;
            }
            
            FXMLLoader loader = new FXMLLoader(fxmlUrl);
            return loader;
        } catch (Exception e) {
            showError("Erreur lors du chargement de " + resourcePath + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Safely loads an FXML file and returns the root Parent node
     * @param resourcePath Path to the FXML file
     * @param caller The calling class
     * @return Parent node if successful, null if failed
     */
    public static Parent loadFXMLRoot(String resourcePath, Class<?> caller) {
        try {
            FXMLLoader loader = loadFXML(resourcePath, caller);
            if (loader == null) return null;
            
            return loader.load();
        } catch (IOException e) {
            showError("Erreur lors du chargement de " + resourcePath + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Creates and shows a new window with the given FXML
     * @param resourcePath Path to the FXML file
     * @param title Window title
     * @param caller The calling class
     * @param maximized Whether to maximize the window
     * @return The created Stage, or null if failed
     */
    public static Stage createWindow(String resourcePath, String title, Class<?> caller, boolean maximized) {
        try {
            Parent root = loadFXMLRoot(resourcePath, caller);
            if (root == null) return null;
            
            Stage stage = new Stage();
            stage.setTitle(title);
            stage.setScene(new Scene(root));
            
            if (maximized) {
                maximizeWindow(stage);
            }
            
            return stage;
        } catch (Exception e) {
            showError("Erreur lors de la création de la fenêtre: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Creates and shows a maximized window
     * @param resourcePath Path to the FXML file
     * @param title Window title
     * @param caller The calling class
     * @return The created Stage, or null if failed
     */
    public static Stage createMaximizedWindow(String resourcePath, String title, Class<?> caller) {
        Stage stage = createWindow(resourcePath, title, caller, true);
        if (stage != null) {
            stage.show();
        }
        return stage;
    }
    
    /**
     * Creates and shows a normal-sized window
     * @param resourcePath Path to the FXML file
     * @param title Window title
     * @param caller The calling class
     * @param width Window width
     * @param height Window height
     * @return The created Stage, or null if failed
     */
    public static Stage createWindow(String resourcePath, String title, Class<?> caller, double width, double height) {
        Stage stage = createWindow(resourcePath, title, caller, false);
        if (stage != null) {
            stage.setWidth(width);
            stage.setHeight(height);
            centerWindow(stage);
            stage.show();
        }
        return stage;
    }
    
    /**
     * Maximizes a window while avoiding taskbar overlap
     * @param stage The stage to maximize
     */
    public static void maximizeWindow(Stage stage) {
        try {
            Screen screen = Screen.getPrimary();
            Rectangle2D bounds = screen.getVisualBounds();
            stage.setX(bounds.getMinX());
            stage.setY(bounds.getMinY());
            stage.setWidth(bounds.getWidth());
            stage.setHeight(bounds.getHeight());
        } catch (Exception e) {
            System.err.println("Error maximizing window: " + e.getMessage());
            // Fallback to normal maximization
            stage.setMaximized(true);
        }
    }
    
    /**
     * Centers a window on the screen
     * @param stage The stage to center
     */
    public static void centerWindow(Stage stage) {
        try {
            Screen screen = Screen.getPrimary();
            Rectangle2D bounds = screen.getVisualBounds();
            stage.setX((bounds.getWidth() - stage.getWidth()) / 2);
            stage.setY((bounds.getHeight() - stage.getHeight()) / 2);
        } catch (Exception e) {
            System.err.println("Error centering window: " + e.getMessage());
        }
    }
    
    /**
     * Shows an error alert
     * @param message Error message to display
     */
    public static void showError(String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("LocationV1 - Erreur");
            alert.setHeaderText("Erreur de chargement");
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            // If we can't show the alert, at least print to console
            System.err.println("Error showing alert: " + e.getMessage());
            System.err.println("Original error: " + message);
        }
    }
    
    /**
     * Shows a success alert
     * @param message Success message to display
     */
    public static void showSuccess(String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("LocationV1 - Succès");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing success alert: " + e.getMessage());
        }
    }
    
    /**
     * Shows a warning alert
     * @param message Warning message to display
     */
    public static void showWarning(String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle("LocationV1 - Attention");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing warning alert: " + e.getMessage());
        }
    }
    
    /**
     * Validates that all required FXML files exist
     * @return true if all files exist, false otherwise
     */
    public static boolean validateFXMLFiles() {
        String[] requiredFiles = {
            "/view/dashboard.fxml",
            "/view/location.fxml",
            "/view/location_create.fxml",
            "/view/location_detail.fxml",
            "/view/vehicule.fxml",
            "/view/client.fxml",
            "/view/vehicle_maintenance.fxml",
            "/view/vehicle_return_preview.fxml"
        };
        
        boolean allExist = true;
        for (String file : requiredFiles) {
            URL url = FXMLUtil.class.getResource(file);
            if (url == null) {
                System.err.println("Missing FXML file: " + file);
                allExist = false;
            } else {
                System.out.println("Found FXML file: " + file);
            }
        }
        
        return allExist;
    }
}
