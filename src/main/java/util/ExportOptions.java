package util;

import java.time.LocalDate;

/**
 * Unified ExportOptions class for all export operations
 */
public class ExportOptions {
    private final String format;
    private final String period;
    private final LocalDate startDate;
    private final LocalDate endDate;

    // Constructor for simple format-only exports (like HistoriqueController)
    public ExportOptions(String format) {
        this.format = format;
        this.period = null;
        this.startDate = null;
        this.endDate = null;
    }

    // Constructor for full export options (like other controllers)
    public ExportOptions(String format, String period, LocalDate startDate, LocalDate endDate) {
        this.format = format;
        this.period = period;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    // Getters
    public String getFormat() {
        return format;
    }

    public String getPeriod() {
        return period;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }
}
