package util;

import dao.AdminDAO;
import dao.AgentDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.NotificationDAO;
import dao.NotificationSettingsDAO;
import model.Admin;
import model.Agent;
import model.Client;
import model.Vehicule;
import model.Notification;
import model.NotificationSettings;
import org.mindrot.jbcrypt.BCrypt;
import dao.LocationDAO;
import dao.PaiementDAO;
import dao.VehicleMaintenanceDAO;
import dao.VehicleFailureDAO;
import model.Location;
import model.Paiement;
import model.VehicleMaintenance;
import model.VehicleFailure;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

public class DatabaseInitializer {
    public static void initialize() {
        AdminDAO adminDAO = new AdminDAO();
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        ClientDAO clientDAO = new ClientDAO();
        Random rand = new Random(); // Random instance for all data generation

        // Admin
        if (adminDAO.findAll().isEmpty()) {
            Admin admin = new Admin();
            admin.setUsername("admin");
            admin.setPasswordHash(BCrypt.hashpw("admin123", BCrypt.gensalt()));
            admin.setRole("admin");
            admin.setEmail("<EMAIL>");
            admin.setStatus("ACTIF");
            adminDAO.save(admin);
        }

        // Véhicules
        if (vehiculeDAO.findAll().isEmpty()) {
            for (int i = 1; i <= 15; i++) {
                Vehicule v = new Vehicule();
                v.setMarque(i % 3 == 0 ? "Renault" : i % 3 == 1 ? "Peugeot" : "Dacia");
                v.setModele("Model" + i);
                v.setImmatriculation(String.format("%03d-XYZ-%02d", i * 7, i));
                v.setEtat(i % 5 == 0 ? "en panne" : i % 4 == 0 ? "loué" : "disponible");
                v.setPrixParJour((double)(30 + i));
                v.setPhotoUrl("images/car" + i + ".jpg");
                v.setCarburant(i % 4 == 0 ? "Diesel" : i % 3 == 0 ? "Essence" : "Hybride");
                v.setMetrage(40000 + i * 1000);
                v.setDateAcquisition(java.time.LocalDate.now().minusMonths(i * 2));
                v.setLastUsed(java.time.LocalDate.now().minusDays(i));
                v.setNbreChevaux(4 + (i % 4));
                v.setAssuranceCompagnie(i % 2 == 0 ? "AXA" : "Wafa Assurance");
                v.setAssuranceExpiration(java.time.LocalDate.now().plusMonths(i));
                v.setAssuranceNumero("ASSUR-" + i);
                vehiculeDAO.save(v);
            }
        }
        // Clients
        if (clientDAO.findAll().isEmpty()) {
            for (int i = 1; i <= 10; i++) {
                Client c = new Client();
                c.setNom("ClientNom" + i);
                c.setPrenom("Prenom" + i);
                c.setCin("CIN" + (10000 + i));
                c.setTelephone("06000000" + String.format("%02d", i));
                c.setEmail("client" + i + "@email.com");
                clientDAO.save(c);
            }
        }
        // Locations + Paiements
        LocationDAO locationDAO = new LocationDAO();
        PaiementDAO paiementDAO = new PaiementDAO();
        VehicleMaintenanceDAO maintenanceDAO = new VehicleMaintenanceDAO();
        VehicleFailureDAO failureDAO = new VehicleFailureDAO();
        if (locationDAO.findAll().isEmpty()) {
            java.util.List<Client> clients = clientDAO.findAll();
            java.util.List<Vehicule> vehicules = vehiculeDAO.findAll();
            for (int i = 0; i < 20; i++) {
                Location loc = new Location();
                Client client = clients.get(rand.nextInt(clients.size()));
                Vehicule vehicule = vehicules.get(rand.nextInt(vehicules.size()));
                loc.setClient(client);
                loc.setVehicule(vehicule);
                java.time.LocalDate start = java.time.LocalDate.now().minusDays(rand.nextInt(365));
                loc.setDateDebut(start);
                loc.setDateFinPrevue(start.plusDays(3 + rand.nextInt(7)));
                boolean finished = rand.nextBoolean();
                if (finished) {
                    loc.setDateFinReelle(loc.getDateFinPrevue().plusDays(rand.nextInt(3)));
                }
                double prix = (vehicule.getPrixParJour() != null ? vehicule.getPrixParJour() : 0.0) * (loc.getDateFinPrevue().toEpochDay() - loc.getDateDebut().toEpochDay());
                loc.setPrixTotal(prix);
                loc.setPenalite(finished && rand.nextBoolean() ? 20.0 : 0.0);
                locationDAO.save(loc);
            }

            // Create payments after all locations are saved
            java.util.List<Location> allLocations = locationDAO.findAll();
            for (Location savedLoc : allLocations) {
                if (rand.nextBoolean()) {
                    Paiement p = new Paiement();
                    p.setLocation(savedLoc);
                    p.setMontant(savedLoc.getPrixTotal() + (savedLoc.getPenalite() != 0 ? savedLoc.getPenalite() : 0));
                    p.setDatePaiement(savedLoc.getDateFinReelle() != null ? savedLoc.getDateFinReelle() : savedLoc.getDateFinPrevue());
                    p.setStatut("Payé");
                    p.setMethodePaiement(rand.nextBoolean() ? "Carte bancaire" : (rand.nextBoolean() ? "Espèces" : "Virement"));
                    paiementDAO.save(p);
                }
            }
        }

        // Agent
        AgentDAO agentDAO = new AgentDAO();
        if (agentDAO.findAll().isEmpty()) {
            Agent agent = new Agent();
            agent.setUsername("agent1");
            agent.setPasswordHash(BCrypt.hashpw("agent123", BCrypt.gensalt()));
            agent.setRole("agent");
            agent.setEmail("<EMAIL>");
            agent.setStatus("ACTIF");
            agentDAO.save(agent);
        }

        // Create sample maintenance records
        try {
            List<Vehicule> allVehicles = vehiculeDAO.findAll();
            for (Vehicule vehicule : allVehicles) {
                // Create past maintenance
                VehicleMaintenance pastMaintenance = new VehicleMaintenance(vehicule,
                    VehicleMaintenance.MaintenanceType.PERIODIQUE,
                    LocalDate.now().minusMonths(rand.nextInt(12) + 1));
                pastMaintenance.setStatus(VehicleMaintenance.MaintenanceStatus.TERMINE);
                pastMaintenance.setCost((double)(200 + rand.nextInt(800)));
                pastMaintenance.setPerformedBy("Garage Central");
                pastMaintenance.setDescription("Maintenance périodique complète");
                maintenanceDAO.save(pastMaintenance);

                // Create upcoming maintenance for some vehicles
                if (rand.nextBoolean()) {
                    VehicleMaintenance upcomingMaintenance = new VehicleMaintenance(vehicule,
                        VehicleMaintenance.MaintenanceType.VISITE_TECHNIQUE,
                        LocalDate.now().plusDays(rand.nextInt(60) + 1));
                    upcomingMaintenance.setStatus(VehicleMaintenance.MaintenanceStatus.PLANIFIE);
                    upcomingMaintenance.setDescription("Visite technique annuelle");
                    maintenanceDAO.save(upcomingMaintenance);
                }

                // Create some failures for older vehicles
                int vehicleAge = LocalDate.now().getYear() - (vehicule.getDateAcquisition() != null ?
                    vehicule.getDateAcquisition().getYear() : LocalDate.now().getYear());
                if (vehicleAge >= 3 && rand.nextBoolean()) {
                    VehicleFailure.FailureType[] failureTypes = VehicleFailure.FailureType.values();
                    VehicleFailure failure = new VehicleFailure(vehicule,
                        failureTypes[rand.nextInt(failureTypes.length)],
                        "Panne détectée lors de l'utilisation");
                    failure.setFailureDate(LocalDate.now().minusDays(rand.nextInt(90)));
                    failure.setSeverity(VehicleFailure.FailureSeverity.values()[rand.nextInt(3)]);

                    // Some failures are resolved
                    if (rand.nextBoolean()) {
                        failure.setStatus(VehicleFailure.RepairStatus.REPARE);
                        failure.setRepairDate(failure.getFailureDate().plusDays(rand.nextInt(7) + 1));
                        failure.setRepairCost((double)(100 + rand.nextInt(500)));
                        failure.setRepairAction("Réparation effectuée");
                        failure.setRepairedBy("Garage Central");
                    }

                    failureDAO.save(failure);
                }
            }
            System.out.println("Données de maintenance et pannes créées avec succès !");
        } catch (Exception e) {
            System.err.println("Erreur lors de la création des données de maintenance: " + e.getMessage());
        }

        // Create sample notifications and settings
        try {
            NotificationDAO notificationDAO = new NotificationDAO();
            NotificationSettingsDAO settingsDAO = new NotificationSettingsDAO();


            List<Admin> admins = adminDAO.findAll();
            if (!admins.isEmpty()) {
                Admin admin = admins.get(0);

                // Create default notification settings for admin
                NotificationSettings settings = settingsDAO.findByUser(admin);
                if (settings == null) {
                    settings = new NotificationSettings(admin);
                    settingsDAO.save(settings);
                }

                // Create sample notifications
                List<Notification> existingNotifications = notificationDAO.findByUser(admin);
                if (existingNotifications.isEmpty()) {
                    // Welcome notification
                    Notification welcomeNotification = new Notification(
                        Notification.NotificationType.WELCOME_ADMIN,
                        "Bienvenue dans LocationV12",
                        "Bienvenue " + admin.getFullName() + "! Le système de gestion de location est prêt à l'emploi.",
                        admin
                    );
                    notificationDAO.save(welcomeNotification);

                    // System alert notification
                    Notification systemNotification = new Notification(
                        Notification.NotificationType.SYSTEM_ALERT,
                        "Système initialisé",
                        "Le système LocationV12 a été initialisé avec succès. Toutes les fonctionnalités sont opérationnelles.",
                        admin
                    );
                    systemNotification.setPriority(Notification.Priority.HIGH);
                    notificationDAO.save(systemNotification);

                    // Sample maintenance notification
                    List<Vehicule> vehicules = vehiculeDAO.findAll();
                    if (!vehicules.isEmpty()) {
                        Notification maintenanceNotification = new Notification(
                            Notification.NotificationType.MAINTENANCE_DUE,
                            "Maintenance programmée",
                            "Le véhicule " + vehicules.get(0).toString() + " nécessite une maintenance dans 7 jours.",
                            admin
                        );
                        maintenanceNotification.setRelatedVehicule(vehicules.get(0));
                        maintenanceNotification.setScheduledFor(LocalDate.now().plusDays(7).atTime(9, 0));
                        notificationDAO.save(maintenanceNotification);
                    }
                }
            }

            System.out.println("Notifications et paramètres créés avec succès !");
        } catch (Exception e) {
            System.err.println("Erreur lors de la création des notifications: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        initialize();
    }
}