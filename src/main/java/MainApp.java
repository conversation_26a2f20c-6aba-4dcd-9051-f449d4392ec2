import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import service.SystemTrayService;
import service.NotificationService;

public class MainApp extends Application {
    private SystemTrayService systemTrayService;
    private NotificationService notificationService;

    @Override
    public void start(Stage primaryStage) throws Exception {
        // Initialize system tray service
        systemTrayService = SystemTrayService.getInstance();
        systemTrayService.setPrimaryStage(primaryStage);

        // Initialize notification service
        notificationService = NotificationService.getInstance();

        // Set up the primary stage
        Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
        primaryStage.setTitle("LocationV12 - Gestion de Location");
        primaryStage.setScene(new Scene(root, 500, 800));
        primaryStage.centerOnScreen();

        // Handle window close event to minimize to tray instead of closing
        primaryStage.setOnCloseRequest(event -> {
            if (systemTrayService.isSupported()) {
                event.consume(); // Prevent default close
                systemTrayService.minimizeToTray();
            } else {
                // If system tray is not supported, close normally
                Platform.exit();
                System.exit(0);
            }
        });

        primaryStage.show();

        // Show welcome notification if system tray is available
        if (systemTrayService.isSupported()) {
            Platform.runLater(() -> {
                systemTrayService.updateTrayIconTooltip("LocationV12 - Application démarrée");
            });
        }
    }

    @Override
    public void stop() throws Exception {
        // Cleanup services
        if (notificationService != null) {
            notificationService.shutdown();
        }
        if (systemTrayService != null) {
            systemTrayService.cleanup();
        }
        super.stop();
    }

    public static void main(String[] args) {
        // Set system properties for better AWT/Swing integration
        System.setProperty("java.awt.headless", "false");
        System.setProperty("prism.order", "sw");

        util.DatabaseInitializer.initialize();
        launch(args);
    }
}
