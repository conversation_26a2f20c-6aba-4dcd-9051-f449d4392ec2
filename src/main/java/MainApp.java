import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;


public class MainApp extends Application {
    @Override
    public void start(Stage primaryStage) throws Exception {
        Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
        primaryStage.setTitle("Location de voitures - Admin");
        primaryStage.setScene(new Scene(root, 500, 800));
        primaryStage.centerOnScreen();
        primaryStage.show();
    }



    public static void main(String[] args) {
        util.DatabaseInitializer.initialize();
        launch(args);
    }
}
