package model;

import jakarta.persistence.*;
import java.time.LocalDate;

@Entity
public class VehicleFailure {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Vehicule vehicule;

    private LocalDate failureDate;
    private String failureType;
    private String description;
    private String symptoms;
    private String causeAnalysis;
    private String repairAction;
    private Double repairCost;
    private String repairedBy;
    private LocalDate repairDate;
    private Integer kilometrageAtFailure;
    
    public enum FailureType {
        MOTEUR,          // Engine failure
        TRANSMISSION,    // Transmission issues
        FREINS,          // Brake problems
        SUSPENSION,      // Suspension issues
        ELECTRIQUE,      // Electrical problems
        CLIMATISATION,   // AC/Heating issues
        PNEUS,           // Tire problems
        CARROSSERIE,     // Body damage
        VITRES,          // Window/glass issues
        ECLAIRAGE,       // Lighting problems
        DIRECTION,       // Steering issues
        ECHAPPEMENT,     // Exhaust system
        CARBURANT,       // Fuel system
        REFROIDISSEMENT, // Cooling system
        AUTRE            // Other
    }
    
    public enum FailureSeverity {
        CRITIQUE,        // Critical - vehicle cannot be used
        MAJEURE,         // Major - affects safety or major functionality
        MINEURE,         // Minor - cosmetic or minor functionality
        PREVENTIVE       // Preventive - caught before becoming a problem
    }
    
    public enum RepairStatus {
        SIGNALE,         // Reported
        EN_DIAGNOSTIC,   // Being diagnosed
        EN_ATTENTE_PIECES, // Waiting for parts
        EN_REPARATION,   // Being repaired
        REPARE,          // Repaired
        NON_REPARABLE,   // Cannot be repaired
        REPORTE          // Repair postponed
    }
    
    @Enumerated(EnumType.STRING)
    private FailureType type;
    
    @Enumerated(EnumType.STRING)
    private FailureSeverity severity;
    
    @Enumerated(EnumType.STRING)
    private RepairStatus status;
    
    private String partsReplaced;
    private String preventiveMeasures;
    private String notes;
    private LocalDate createdDate;
    private LocalDate updatedDate;
    
    // Constructors
    public VehicleFailure() {
        this.createdDate = LocalDate.now();
        this.updatedDate = LocalDate.now();
        this.status = RepairStatus.SIGNALE;
        this.failureDate = LocalDate.now();
    }
    
    public VehicleFailure(Vehicule vehicule, FailureType type, String description) {
        this();
        this.vehicule = vehicule;
        this.type = type;
        this.failureType = type.name();
        this.description = description;
    }
    
    // Business logic methods
    public boolean isResolved() {
        return status == RepairStatus.REPARE;
    }
    
    public boolean isCritical() {
        return severity == FailureSeverity.CRITIQUE;
    }
    
    public long getDaysSinceFailure() {
        return java.time.temporal.ChronoUnit.DAYS.between(failureDate, LocalDate.now());
    }
    
    public long getDaysToRepair() {
        if (repairDate == null || failureDate == null) return 0;
        return java.time.temporal.ChronoUnit.DAYS.between(failureDate, repairDate);
    }
    
    public String getFailureSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(type != null ? type.name() : failureType);
        if (severity != null) {
            summary.append(" (").append(severity.name()).append(")");
        }
        summary.append(" - ").append(description != null ? description : "Pas de description");
        return summary.toString();
    }
    
    public String getRepairSummary() {
        if (!isResolved()) {
            return "Non réparé - Statut: " + (status != null ? status.name() : "Inconnu");
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("Réparé le ").append(repairDate != null ? repairDate.toString() : "date inconnue");
        if (repairAction != null && !repairAction.isEmpty()) {
            summary.append(" - ").append(repairAction);
        }
        if (repairCost != null && repairCost > 0) {
            summary.append(" (Coût: ").append(String.format("%.2f", repairCost)).append(" DH)");
        }
        return summary.toString();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }
    
    public LocalDate getFailureDate() { return failureDate; }
    public void setFailureDate(LocalDate failureDate) { this.failureDate = failureDate; }
    
    public String getFailureType() { return failureType; }
    public void setFailureType(String failureType) { this.failureType = failureType; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getSymptoms() { return symptoms; }
    public void setSymptoms(String symptoms) { this.symptoms = symptoms; }
    
    public String getCauseAnalysis() { return causeAnalysis; }
    public void setCauseAnalysis(String causeAnalysis) { this.causeAnalysis = causeAnalysis; }
    
    public String getRepairAction() { return repairAction; }
    public void setRepairAction(String repairAction) { this.repairAction = repairAction; }
    
    public Double getRepairCost() { return repairCost; }
    public void setRepairCost(Double repairCost) { this.repairCost = repairCost; }
    
    public String getRepairedBy() { return repairedBy; }
    public void setRepairedBy(String repairedBy) { this.repairedBy = repairedBy; }
    
    public LocalDate getRepairDate() { return repairDate; }
    public void setRepairDate(LocalDate repairDate) { 
        this.repairDate = repairDate;
        if (repairDate != null && status != RepairStatus.REPARE) {
            this.status = RepairStatus.REPARE;
        }
        this.updatedDate = LocalDate.now();
    }
    
    public Integer getKilometrageAtFailure() { return kilometrageAtFailure; }
    public void setKilometrageAtFailure(Integer kilometrageAtFailure) { this.kilometrageAtFailure = kilometrageAtFailure; }
    
    public FailureType getType() { return type; }
    public void setType(FailureType type) { 
        this.type = type;
        this.failureType = type != null ? type.name() : null;
    }
    
    public FailureSeverity getSeverity() { return severity; }
    public void setSeverity(FailureSeverity severity) { this.severity = severity; }
    
    public RepairStatus getStatus() { return status; }
    public void setStatus(RepairStatus status) { 
        this.status = status;
        this.updatedDate = LocalDate.now();
    }
    
    public String getPartsReplaced() { return partsReplaced; }
    public void setPartsReplaced(String partsReplaced) { this.partsReplaced = partsReplaced; }
    
    public String getPreventiveMeasures() { return preventiveMeasures; }
    public void setPreventiveMeasures(String preventiveMeasures) { this.preventiveMeasures = preventiveMeasures; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDate getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDate createdDate) { this.createdDate = createdDate; }
    
    public LocalDate getUpdatedDate() { return updatedDate; }
    public void setUpdatedDate(LocalDate updatedDate) { this.updatedDate = updatedDate; }
    
    @Override
    public String toString() {
        return String.format("%s - %s (%s)", 
            vehicule != null ? vehicule.getMarque() + " " + vehicule.getModele() : "Véhicule inconnu",
            type != null ? type.name() : failureType,
            failureDate != null ? failureDate.toString() : "Date inconnue"
        );
    }
}
