package model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "notifications")
public class Notification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    private NotificationType type;

    private String title;
    private String message;
    private String actionUrl; // URL to navigate when clicked
    private String actionData; // JSON data for the action

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne
    @JoinColumn(name = "client_id", nullable = true)
    private Client relatedClient;

    @ManyToOne
    @JoinColumn(name = "location_id", nullable = true)
    private Location relatedLocation;

    @ManyToOne
    @JoinColumn(name = "vehicule_id", nullable = true)
    private Vehicule relatedVehicule;

    private boolean isRead = false;
    private boolean isSystemTrayShown = false;
    private LocalDateTime createdAt;
    private LocalDateTime readAt;
    private LocalDateTime scheduledFor; // For scheduled notifications

    @Enumerated(EnumType.STRING)
    private Priority priority = Priority.NORMAL;

    public enum NotificationType {
        RAPPEL_RETOUR("Rappel de retour de véhicule", "🚗"),
        LOCATION_CONFIRMEE("Location confirmée", "✅"),
        LOCATION_RESERVE("Nouvelle réservation", "📅"),
        PAIEMENT_DU("Paiement en attente", "💰"),
        PAIEMENT_RECU("Paiement reçu", "💳"),
        MAINTENANCE_DUE("Maintenance requise", "🔧"),
        VEHICULE_DISPONIBLE("Véhicule disponible", "🚙"),
        WELCOME_ADMIN("Bienvenue administrateur", "👋"),
        SYSTEM_ALERT("Alerte système", "⚠️"),
        CLIENT_BIRTHDAY("Anniversaire client", "🎂"),
        CONTRACT_EXPIRING("Contrat expirant", "📋");

        private final String displayName;
        private final String icon;

        NotificationType(String displayName, String icon) {
            this.displayName = displayName;
            this.icon = icon;
        }

        public String getDisplayName() { return displayName; }
        public String getIcon() { return icon; }
    }

    public enum Priority {
        LOW, NORMAL, HIGH, URGENT
    }

    // Constructors
    public Notification() {
        this.createdAt = LocalDateTime.now();
    }

    public Notification(NotificationType type, String title, String message, User user) {
        this();
        this.type = type;
        this.title = title;
        this.message = message;
        this.user = user;
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public NotificationType getType() { return type; }
    public void setType(NotificationType type) { this.type = type; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public String getActionUrl() { return actionUrl; }
    public void setActionUrl(String actionUrl) { this.actionUrl = actionUrl; }

    public String getActionData() { return actionData; }
    public void setActionData(String actionData) { this.actionData = actionData; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Client getRelatedClient() { return relatedClient; }
    public void setRelatedClient(Client relatedClient) { this.relatedClient = relatedClient; }

    public Location getRelatedLocation() { return relatedLocation; }
    public void setRelatedLocation(Location relatedLocation) { this.relatedLocation = relatedLocation; }

    public Vehicule getRelatedVehicule() { return relatedVehicule; }
    public void setRelatedVehicule(Vehicule relatedVehicule) { this.relatedVehicule = relatedVehicule; }

    public boolean isRead() { return isRead; }
    public void setRead(boolean read) { isRead = read; }

    public boolean isSystemTrayShown() { return isSystemTrayShown; }
    public void setSystemTrayShown(boolean systemTrayShown) { isSystemTrayShown = systemTrayShown; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getReadAt() { return readAt; }
    public void setReadAt(LocalDateTime readAt) { this.readAt = readAt; }

    public LocalDateTime getScheduledFor() { return scheduledFor; }
    public void setScheduledFor(LocalDateTime scheduledFor) { this.scheduledFor = scheduledFor; }

    public Priority getPriority() { return priority; }
    public void setPriority(Priority priority) { this.priority = priority; }

    // Utility methods
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
    }

    public String getFormattedCreatedAt() {
        if (createdAt == null) return "";
        return createdAt.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
    }

    public String getTypeIcon() {
        return type != null ? type.getIcon() : "📢";
    }

    public String getTypeDisplayName() {
        return type != null ? type.getDisplayName() : "Notification";
    }

    public boolean isScheduled() {
        return scheduledFor != null && scheduledFor.isAfter(LocalDateTime.now());
    }

    public boolean isDue() {
        return scheduledFor != null && scheduledFor.isBefore(LocalDateTime.now());
    }

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
}
