package model;

import jakarta.persistence.*;
import java.time.LocalDate;

@Entity
public class VehicleMaintenance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Vehicule vehicule;

    private LocalDate maintenanceDate;
    private LocalDate nextMaintenanceDate;
    private String maintenanceType;
    private String description;
    private Double cost;
    private String performedBy;
    private Integer kilometrage;
    
    public enum MaintenanceType {
        PERIODIQUE,      // Regular periodic maintenance
        PREVENTIVE,      // Preventive maintenance
        CORRECTIVE,      // Corrective maintenance (after failure)
        VISITE_TECHNIQUE, // Technical inspection
        VIDANGE,         // Oil change
        PNEUS,           // Tire maintenance
        FREINS,          // Brake maintenance
        MOTEUR,          // Engine maintenance
        CARROSSERIE,     // Body work
        AUTRE            // Other
    }
    
    public enum MaintenanceStatus {
        PLANIFIE,        // Scheduled
        EN_COURS,        // In progress
        TERMINE,         // Completed
        REPORTE,         // Postponed
        ANNULE           // Cancelled
    }
    
    @Enumerated(EnumType.STRING)
    private MaintenanceType type;
    
    @Enumerated(EnumType.STRING)
    private MaintenanceStatus status;
    
    private String notes;
    private LocalDate createdDate;
    private LocalDate updatedDate;
    
    // Constructors
    public VehicleMaintenance() {
        this.createdDate = LocalDate.now();
        this.updatedDate = LocalDate.now();
        this.status = MaintenanceStatus.PLANIFIE;
    }
    
    public VehicleMaintenance(Vehicule vehicule, MaintenanceType type, LocalDate maintenanceDate) {
        this();
        this.vehicule = vehicule;
        this.type = type;
        this.maintenanceDate = maintenanceDate;
        this.maintenanceType = type.name();
        calculateNextMaintenanceDate();
    }
    
    // Calculate next maintenance date based on type and vehicle age
    public void calculateNextMaintenanceDate() {
        if (maintenanceDate == null || vehicule == null) return;
        
        int vehicleAge = calculateVehicleAge();
        
        switch (type) {
            case PERIODIQUE:
                if (vehicleAge >= 5) {
                    // Vehicles 5+ years old need maintenance every 6 months
                    nextMaintenanceDate = maintenanceDate.plusMonths(6);
                } else {
                    // Newer vehicles need maintenance every 12 months
                    nextMaintenanceDate = maintenanceDate.plusMonths(12);
                }
                break;
            case VISITE_TECHNIQUE:
                if (vehicleAge >= 5) {
                    // Technical inspection every 6 months for old vehicles
                    nextMaintenanceDate = maintenanceDate.plusMonths(6);
                } else {
                    // Annual technical inspection for newer vehicles
                    nextMaintenanceDate = maintenanceDate.plusMonths(12);
                }
                break;
            case VIDANGE:
                // Oil change every 6 months or 10,000 km
                nextMaintenanceDate = maintenanceDate.plusMonths(6);
                break;
            case PREVENTIVE:
                // Preventive maintenance every 8 months
                nextMaintenanceDate = maintenanceDate.plusMonths(8);
                break;
            default:
                // Default to 12 months for other types
                nextMaintenanceDate = maintenanceDate.plusMonths(12);
                break;
        }
    }
    
    private int calculateVehicleAge() {
        if (vehicule == null || vehicule.getDateAcquisition() == null) return 0;
        return LocalDate.now().getYear() - vehicule.getDateAcquisition().getYear();
    }
    
    public boolean isOverdue() {
        return nextMaintenanceDate != null && nextMaintenanceDate.isBefore(LocalDate.now()) 
               && status != MaintenanceStatus.TERMINE;
    }
    
    public long getDaysOverdue() {
        if (!isOverdue()) return 0;
        return java.time.temporal.ChronoUnit.DAYS.between(nextMaintenanceDate, LocalDate.now());
    }
    
    public boolean isUpcoming() {
        if (nextMaintenanceDate == null) return false;
        LocalDate thirtyDaysFromNow = LocalDate.now().plusDays(30);
        return nextMaintenanceDate.isAfter(LocalDate.now()) && 
               nextMaintenanceDate.isBefore(thirtyDaysFromNow);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }
    
    public LocalDate getMaintenanceDate() { return maintenanceDate; }
    public void setMaintenanceDate(LocalDate maintenanceDate) { 
        this.maintenanceDate = maintenanceDate;
        calculateNextMaintenanceDate();
    }
    
    public LocalDate getNextMaintenanceDate() { return nextMaintenanceDate; }
    public void setNextMaintenanceDate(LocalDate nextMaintenanceDate) { this.nextMaintenanceDate = nextMaintenanceDate; }
    
    public String getMaintenanceType() { return maintenanceType; }
    public void setMaintenanceType(String maintenanceType) { this.maintenanceType = maintenanceType; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Double getCost() { return cost; }
    public void setCost(Double cost) { this.cost = cost; }
    
    public String getPerformedBy() { return performedBy; }
    public void setPerformedBy(String performedBy) { this.performedBy = performedBy; }
    
    public Integer getKilometrage() { return kilometrage; }
    public void setKilometrage(Integer kilometrage) { this.kilometrage = kilometrage; }
    
    public MaintenanceType getType() { return type; }
    public void setType(MaintenanceType type) { 
        this.type = type;
        this.maintenanceType = type != null ? type.name() : null;
        calculateNextMaintenanceDate();
    }
    
    public MaintenanceStatus getStatus() { return status; }
    public void setStatus(MaintenanceStatus status) { 
        this.status = status;
        this.updatedDate = LocalDate.now();
    }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDate getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDate createdDate) { this.createdDate = createdDate; }
    
    public LocalDate getUpdatedDate() { return updatedDate; }
    public void setUpdatedDate(LocalDate updatedDate) { this.updatedDate = updatedDate; }
    
    @Override
    public String toString() {
        return String.format("%s - %s (%s)", 
            vehicule != null ? vehicule.getMarque() + " " + vehicule.getModele() : "Véhicule inconnu",
            type != null ? type.name() : maintenanceType,
            maintenanceDate != null ? maintenanceDate.toString() : "Date inconnue"
        );
    }
}
