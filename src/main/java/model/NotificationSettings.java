package model;

import jakarta.persistence.*;
import java.time.LocalTime;

@Entity
@Table(name = "notification_settings")
public class NotificationSettings {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @JoinColumn(name = "user_id", unique = true)
    private User user;

    // General notification settings
    private boolean desktopNotificationsEnabled = true;
    private boolean emailNotificationsEnabled = true;
    private boolean soundEnabled = true;

    // Specific notification type settings
    private boolean rappelRetourEnabled = true;
    private boolean locationConfirmeeEnabled = true;
    private boolean locationReserveEnabled = true;
    private boolean paiementDuEnabled = true;
    private boolean paiementRecuEnabled = true;
    private boolean maintenanceDueEnabled = true;
    private boolean vehiculeDisponibleEnabled = true;
    private boolean welcomeAdminEnabled = true;
    private boolean systemAlertEnabled = true;
    private boolean clientBirthdayEnabled = false;
    private boolean contractExpiringEnabled = true;

    // Timing settings
    private LocalTime quietHoursStart = LocalTime.of(22, 0); // 10 PM
    private LocalTime quietHoursEnd = LocalTime.of(8, 0);    // 8 AM
    private boolean respectQuietHours = true;

    // Reminder settings
    private int rappelRetourDaysBefore = 1; // Days before return date
    private int maintenanceDaysBefore = 7;  // Days before maintenance due
    private int contractExpiringDaysBefore = 30; // Days before contract expires

    // Display settings
    private int maxNotificationsToShow = 50;
    private boolean autoMarkAsReadAfterDays = true;
    private int autoMarkAsReadDays = 30;

    // Constructors
    public NotificationSettings() {}

    public NotificationSettings(User user) {
        this.user = user;
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public boolean isDesktopNotificationsEnabled() { return desktopNotificationsEnabled; }
    public void setDesktopNotificationsEnabled(boolean desktopNotificationsEnabled) { 
        this.desktopNotificationsEnabled = desktopNotificationsEnabled; 
    }

    public boolean isEmailNotificationsEnabled() { return emailNotificationsEnabled; }
    public void setEmailNotificationsEnabled(boolean emailNotificationsEnabled) { 
        this.emailNotificationsEnabled = emailNotificationsEnabled; 
    }

    public boolean isSoundEnabled() { return soundEnabled; }
    public void setSoundEnabled(boolean soundEnabled) { this.soundEnabled = soundEnabled; }

    public boolean isRappelRetourEnabled() { return rappelRetourEnabled; }
    public void setRappelRetourEnabled(boolean rappelRetourEnabled) { 
        this.rappelRetourEnabled = rappelRetourEnabled; 
    }

    public boolean isLocationConfirmeeEnabled() { return locationConfirmeeEnabled; }
    public void setLocationConfirmeeEnabled(boolean locationConfirmeeEnabled) { 
        this.locationConfirmeeEnabled = locationConfirmeeEnabled; 
    }

    public boolean isLocationReserveEnabled() { return locationReserveEnabled; }
    public void setLocationReserveEnabled(boolean locationReserveEnabled) { 
        this.locationReserveEnabled = locationReserveEnabled; 
    }

    public boolean isPaiementDuEnabled() { return paiementDuEnabled; }
    public void setPaiementDuEnabled(boolean paiementDuEnabled) { 
        this.paiementDuEnabled = paiementDuEnabled; 
    }

    public boolean isPaiementRecuEnabled() { return paiementRecuEnabled; }
    public void setPaiementRecuEnabled(boolean paiementRecuEnabled) { 
        this.paiementRecuEnabled = paiementRecuEnabled; 
    }

    public boolean isMaintenanceDueEnabled() { return maintenanceDueEnabled; }
    public void setMaintenanceDueEnabled(boolean maintenanceDueEnabled) { 
        this.maintenanceDueEnabled = maintenanceDueEnabled; 
    }

    public boolean isVehiculeDisponibleEnabled() { return vehiculeDisponibleEnabled; }
    public void setVehiculeDisponibleEnabled(boolean vehiculeDisponibleEnabled) { 
        this.vehiculeDisponibleEnabled = vehiculeDisponibleEnabled; 
    }

    public boolean isWelcomeAdminEnabled() { return welcomeAdminEnabled; }
    public void setWelcomeAdminEnabled(boolean welcomeAdminEnabled) { 
        this.welcomeAdminEnabled = welcomeAdminEnabled; 
    }

    public boolean isSystemAlertEnabled() { return systemAlertEnabled; }
    public void setSystemAlertEnabled(boolean systemAlertEnabled) { 
        this.systemAlertEnabled = systemAlertEnabled; 
    }

    public boolean isClientBirthdayEnabled() { return clientBirthdayEnabled; }
    public void setClientBirthdayEnabled(boolean clientBirthdayEnabled) { 
        this.clientBirthdayEnabled = clientBirthdayEnabled; 
    }

    public boolean isContractExpiringEnabled() { return contractExpiringEnabled; }
    public void setContractExpiringEnabled(boolean contractExpiringEnabled) { 
        this.contractExpiringEnabled = contractExpiringEnabled; 
    }

    public LocalTime getQuietHoursStart() { return quietHoursStart; }
    public void setQuietHoursStart(LocalTime quietHoursStart) { this.quietHoursStart = quietHoursStart; }

    public LocalTime getQuietHoursEnd() { return quietHoursEnd; }
    public void setQuietHoursEnd(LocalTime quietHoursEnd) { this.quietHoursEnd = quietHoursEnd; }

    public boolean isRespectQuietHours() { return respectQuietHours; }
    public void setRespectQuietHours(boolean respectQuietHours) { this.respectQuietHours = respectQuietHours; }

    public int getRappelRetourDaysBefore() { return rappelRetourDaysBefore; }
    public void setRappelRetourDaysBefore(int rappelRetourDaysBefore) { 
        this.rappelRetourDaysBefore = rappelRetourDaysBefore; 
    }

    public int getMaintenanceDaysBefore() { return maintenanceDaysBefore; }
    public void setMaintenanceDaysBefore(int maintenanceDaysBefore) { 
        this.maintenanceDaysBefore = maintenanceDaysBefore; 
    }

    public int getContractExpiringDaysBefore() { return contractExpiringDaysBefore; }
    public void setContractExpiringDaysBefore(int contractExpiringDaysBefore) { 
        this.contractExpiringDaysBefore = contractExpiringDaysBefore; 
    }

    public int getMaxNotificationsToShow() { return maxNotificationsToShow; }
    public void setMaxNotificationsToShow(int maxNotificationsToShow) { 
        this.maxNotificationsToShow = maxNotificationsToShow; 
    }

    public boolean isAutoMarkAsReadAfterDays() { return autoMarkAsReadAfterDays; }
    public void setAutoMarkAsReadAfterDays(boolean autoMarkAsReadAfterDays) { 
        this.autoMarkAsReadAfterDays = autoMarkAsReadAfterDays; 
    }

    public int getAutoMarkAsReadDays() { return autoMarkAsReadDays; }
    public void setAutoMarkAsReadDays(int autoMarkAsReadDays) { this.autoMarkAsReadDays = autoMarkAsReadDays; }

    // Utility methods
    public boolean isNotificationTypeEnabled(Notification.NotificationType type) {
        switch (type) {
            case RAPPEL_RETOUR: return rappelRetourEnabled;
            case LOCATION_CONFIRMEE: return locationConfirmeeEnabled;
            case LOCATION_RESERVE: return locationReserveEnabled;
            case PAIEMENT_DU: return paiementDuEnabled;
            case PAIEMENT_RECU: return paiementRecuEnabled;
            case MAINTENANCE_DUE: return maintenanceDueEnabled;
            case VEHICULE_DISPONIBLE: return vehiculeDisponibleEnabled;
            case WELCOME_ADMIN: return welcomeAdminEnabled;
            case SYSTEM_ALERT: return systemAlertEnabled;
            case CLIENT_BIRTHDAY: return clientBirthdayEnabled;
            case CONTRACT_EXPIRING: return contractExpiringEnabled;
            default: return true;
        }
    }

    public boolean isInQuietHours() {
        if (!respectQuietHours) return false;
        
        LocalTime now = LocalTime.now();
        if (quietHoursStart.isBefore(quietHoursEnd)) {
            // Same day quiet hours (e.g., 14:00 to 18:00)
            return now.isAfter(quietHoursStart) && now.isBefore(quietHoursEnd);
        } else {
            // Overnight quiet hours (e.g., 22:00 to 08:00)
            return now.isAfter(quietHoursStart) || now.isBefore(quietHoursEnd);
        }
    }
}
