package model;

import jakarta.persistence.*;

import java.io.PrintWriter;
import java.time.LocalDate;

@Entity
public class Location {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Client client;

    @ManyToOne
    private Vehicule vehicule;

    private LocalDate dateDebut;
    private LocalDate dateFinPrevue;
    private LocalDate dateFinReelle;
    private double prixTotal;
    private double penalite;

    public LocalDate getDateFin() {
        return dateFinReelle != null ? dateFinReelle : dateFinPrevue;
    }

    public LocalDate getDateRetourEffective() {

        return dateFinReelle;
    }

    public enum Status {
        RESERVE, EN_COURS, TERMINE, ANNULE
    }

    @Enumerated(EnumType.STRING)
    private Status status;

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Client getClient() { return client; }
    public void setClient(Client client) { this.client = client; }
    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }
    public java.time.LocalDate getDateDebut() { return dateDebut; }
    public void setDateDebut(java.time.LocalDate dateDebut) { this.dateDebut = dateDebut; }
    public java.time.LocalDate getDateFinPrevue() { return dateFinPrevue; }
    public void setDateFinPrevue(java.time.LocalDate dateFinPrevue) { this.dateFinPrevue = dateFinPrevue; }
    public java.time.LocalDate getDateFinReelle() { return dateFinReelle; }
    public void setDateFinReelle(java.time.LocalDate dateFinReelle) { this.dateFinReelle = dateFinReelle; }
    public double getPrixTotal() { return prixTotal; }
    public void setPrixTotal(double prixTotal) { this.prixTotal = prixTotal; }
    public double getPenalite() { return penalite; }
    public void setPenalite(double penalite) { this.penalite = penalite; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    /**
     * Update the status based on current date and location dates.
     * Should be called after any date/status change.
     */
    public void updateStatus() {
        LocalDate today = LocalDate.now();
        if (status == Status.ANNULE) return; // Don't auto-update if cancelled
        if (dateDebut != null && dateFinPrevue != null) {
            if (today.isBefore(dateDebut)) {
                status = Status.RESERVE;
            } else if ((today.isEqual(dateDebut) || today.isAfter(dateDebut)) && (dateFinReelle == null || today.isBefore(dateFinReelle))) {
                status = Status.EN_COURS;
            } else if (dateFinReelle != null && today.isAfter(dateFinReelle)) {
                status = Status.TERMINE;
            } else if (today.isAfter(dateFinPrevue)) {
                status = Status.TERMINE;
            }
        }
    }
} 