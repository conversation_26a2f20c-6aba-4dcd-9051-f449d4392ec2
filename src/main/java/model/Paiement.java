package model;

import jakarta.persistence.*;

import java.io.PrintWriter;
import java.time.LocalDate;

@Entity
public class Paiement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Location location;

    private double montant;
    private LocalDate datePaiement;
    private String methodePaiement; // Espèces, Carte bancaire, Virement
    private String statut; // En attente, Payé, Remboursé

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }
    public double getMontant() { return montant; }
    public void setMontant(double montant) { this.montant = montant; }
    public java.time.LocalDate getDatePaiement() { return datePaiement; }
    public void setDatePaiement(java.time.LocalDate datePaiement) { this.datePaiement = datePaiement; }
    public String getMethodePaiement() { return methodePaiement; }
    public void setMethodePaiement(String methodePaiement) { this.methodePaiement = methodePaiement; }
    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }

    public LocalDate getDateEcheance() {
        return location.getDateFinPrevue();
    }

    public String getReference() {
        return "REF-" + id;
    }
}