package service;

import javafx.application.Platform;
import javafx.stage.Stage;
import model.Notification;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.net.URL;

public class SystemTrayService {
    private static SystemTrayService instance;
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    private Stage primaryStage;
    private boolean isInitialized = false;

    private SystemTrayService() {
        initializeSystemTray();
    }

    public static synchronized SystemTrayService getInstance() {
        if (instance == null) {
            instance = new SystemTrayService();
        }
        return instance;
    }

    public void setPrimaryStage(Stage stage) {
        this.primaryStage = stage;
    }

    private void initializeSystemTray() {
        if (!SystemTray.isSupported()) {
            System.err.println("System tray is not supported on this platform");
            return;
        }

        try {
            systemTray = SystemTray.getSystemTray();
            
            // Load the application icon
            Image trayIconImage = loadTrayIcon();
            
            // Create popup menu
            PopupMenu popup = createPopupMenu();
            
            // Create tray icon
            trayIcon = new TrayIcon(trayIconImage, "LocationV12 - Gestion de Location", popup);
            trayIcon.setImageAutoSize(true);
            trayIcon.setToolTip("LocationV12 - Gestion de Location");
            
            // Add double-click listener to restore window
            trayIcon.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    if (e.getClickCount() == 2) {
                        Platform.runLater(() -> restoreWindow());
                    }
                }
            });
            
            // Add to system tray
            systemTray.add(trayIcon);
            isInitialized = true;
            
            System.out.println("System tray initialized successfully");
            
        } catch (AWTException e) {
            System.err.println("Failed to initialize system tray: " + e.getMessage());
        }
    }

    private Image loadTrayIcon() {
        try {
            // Try to load from resources
            URL iconUrl = getClass().getResource("/images/app-icon.png");
            if (iconUrl != null) {
                return Toolkit.getDefaultToolkit().getImage(iconUrl);
            }
            
            // Fallback: try different paths
            iconUrl = getClass().getResource("/app-icon.png");
            if (iconUrl != null) {
                return Toolkit.getDefaultToolkit().getImage(iconUrl);
            }
            
            // Create a simple default icon if no image found
            return createDefaultIcon();
            
        } catch (Exception e) {
            System.err.println("Failed to load tray icon: " + e.getMessage());
            return createDefaultIcon();
        }
    }

    private Image createDefaultIcon() {
        // Create a simple 16x16 blue square as default icon
        int size = 16;
        int[] pixels = new int[size * size];
        for (int i = 0; i < pixels.length; i++) {
            pixels[i] = 0xFF0066CC; // Blue color
        }
        return Toolkit.getDefaultToolkit().createImage(
            new java.awt.image.MemoryImageSource(size, size, pixels, 0, size)
        );
    }

    private PopupMenu createPopupMenu() {
        PopupMenu popup = new PopupMenu();
        
        // Dashboard menu item
        MenuItem dashboardItem = new MenuItem("Ouvrir Dashboard");
        dashboardItem.addActionListener(e -> Platform.runLater(() -> restoreWindow()));
        popup.add(dashboardItem);
        
        popup.addSeparator();
        
        // Quick actions
        MenuItem newLocationItem = new MenuItem("Nouvelle Location");
        newLocationItem.addActionListener(e -> Platform.runLater(() -> {
            restoreWindow();
            // Navigate to new location page
            // This would need to be implemented in the main controller
        }));
        popup.add(newLocationItem);
        
        MenuItem clientsItem = new MenuItem("Gestion Clients");
        clientsItem.addActionListener(e -> Platform.runLater(() -> {
            restoreWindow();
            // Navigate to clients page
        }));
        popup.add(clientsItem);
        
        MenuItem vehiclesItem = new MenuItem("Gestion Véhicules");
        vehiclesItem.addActionListener(e -> Platform.runLater(() -> {
            restoreWindow();
            // Navigate to vehicles page
        }));
        popup.add(vehiclesItem);
        
        popup.addSeparator();
        
        // Notifications
        MenuItem notificationsItem = new MenuItem("Notifications");
        notificationsItem.addActionListener(e -> Platform.runLater(() -> {
            restoreWindow();
            // Show notifications panel
        }));
        popup.add(notificationsItem);
        
        popup.addSeparator();
        
        // Exit
        MenuItem exitItem = new MenuItem("Quitter");
        exitItem.addActionListener(e -> Platform.runLater(() -> {
            Platform.exit();
            System.exit(0);
        }));
        popup.add(exitItem);
        
        return popup;
    }

    public void showNotification(Notification notification) {
        if (!isInitialized || trayIcon == null) {
            return;
        }

        try {
            String title = notification.getTypeIcon() + " " + notification.getTitle();
            String message = notification.getMessage();
            
            // Limit message length for system tray
            if (message.length() > 100) {
                message = message.substring(0, 97) + "...";
            }
            
            TrayIcon.MessageType messageType = getMessageType(notification.getType());
            
            trayIcon.displayMessage(title, message, messageType);
            
            // Add click listener for this notification
            ActionListener notificationClickListener = new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    Platform.runLater(() -> {
                        restoreWindow();
                        handleNotificationClick(notification);
                    });
                    // Remove this listener after use
                    trayIcon.removeActionListener(this);
                }
            };
            
            trayIcon.addActionListener(notificationClickListener);
            
        } catch (Exception e) {
            System.err.println("Failed to show system tray notification: " + e.getMessage());
        }
    }

    private TrayIcon.MessageType getMessageType(Notification.NotificationType type) {
        switch (type) {
            case SYSTEM_ALERT:
            case PAIEMENT_DU:
            case MAINTENANCE_DUE:
                return TrayIcon.MessageType.WARNING;
            case LOCATION_CONFIRMEE:
            case PAIEMENT_RECU:
            case WELCOME_ADMIN:
                return TrayIcon.MessageType.INFO;
            case RAPPEL_RETOUR:
                return TrayIcon.MessageType.WARNING;
            default:
                return TrayIcon.MessageType.INFO;
        }
    }

    private void handleNotificationClick(Notification notification) {
        // This would navigate to the appropriate page based on the notification
        // For now, just mark as read
        NotificationService.getInstance().markAsRead(notification.getId());
        
        // In a full implementation, you would:
        // 1. Get the main controller
        // 2. Navigate to the appropriate page using notification.getActionUrl()
        // 3. Pass any action data if needed
    }

    public void restoreWindow() {
        if (primaryStage != null) {
            primaryStage.show();
            primaryStage.toFront();
            primaryStage.requestFocus();
            
            // If minimized, restore
            if (primaryStage.isIconified()) {
                primaryStage.setIconified(false);
            }
        }
    }

    public void minimizeToTray() {
        if (primaryStage != null && isInitialized) {
            primaryStage.hide();
            
            // Show a notification that the app is minimized to tray
            if (trayIcon != null) {
                trayIcon.displayMessage(
                    "LocationV12",
                    "L'application continue de fonctionner en arrière-plan. Double-cliquez sur l'icône pour la restaurer.",
                    TrayIcon.MessageType.INFO
                );
            }
        }
    }

    public void updateTrayIconTooltip(String tooltip) {
        if (trayIcon != null) {
            trayIcon.setToolTip(tooltip);
        }
    }

    public void updateNotificationBadge(int unreadCount) {
        if (trayIcon != null) {
            String tooltip = "LocationV12 - Gestion de Location";
            if (unreadCount > 0) {
                tooltip += " (" + unreadCount + " notifications non lues)";
            }
            trayIcon.setToolTip(tooltip);
        }
    }

    public boolean isSupported() {
        return SystemTray.isSupported() && isInitialized;
    }

    public void cleanup() {
        if (systemTray != null && trayIcon != null) {
            systemTray.remove(trayIcon);
        }
    }
}
