package service;

import dao.NotificationDAO;
import dao.NotificationSettingsDAO;
import dao.LocationDAO;
import dao.VehicleMaintenanceDAO;
import model.*;
import javafx.application.Platform;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class NotificationService {
    private static NotificationService instance;
    private final NotificationDAO notificationDAO;
    private final NotificationSettingsDAO settingsDAO;
    private final LocationDAO locationDAO;
    private final VehicleMaintenanceDAO maintenanceDAO;
    private final SystemTrayService systemTrayService;
    private final ScheduledExecutorService scheduler;

    private NotificationService() {
        this.notificationDAO = new NotificationDAO();
        this.settingsDAO = new NotificationSettingsDAO();
        this.locationDAO = new LocationDAO();
        this.maintenanceDAO = new VehicleMaintenanceDAO();
        this.systemTrayService = SystemTrayService.getInstance();
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // Start periodic checks
        startPeriodicChecks();
    }

    public static synchronized NotificationService getInstance() {
        if (instance == null) {
            instance = new NotificationService();
        }
        return instance;
    }

    private void startPeriodicChecks() {
        // Check for due notifications every 5 minutes
        scheduler.scheduleAtFixedRate(this::checkForDueNotifications, 0, 5, TimeUnit.MINUTES);
        
        // Check for return reminders every hour
        scheduler.scheduleAtFixedRate(this::checkForReturnReminders, 0, 1, TimeUnit.HOURS);
        
        // Check for maintenance reminders daily at 9 AM
        scheduler.scheduleAtFixedRate(this::checkForMaintenanceReminders, 
            getSecondsUntil9AM(), 24 * 60 * 60, TimeUnit.SECONDS);
    }

    private long getSecondsUntil9AM() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime next9AM = now.toLocalDate().atTime(9, 0);
        if (now.isAfter(next9AM)) {
            next9AM = next9AM.plusDays(1);
        }
        return java.time.Duration.between(now, next9AM).getSeconds();
    }

    public void createNotification(Notification.NotificationType type, String title, String message, User user) {
        createNotification(type, title, message, user, null, null, null);
    }

    public void createNotification(Notification.NotificationType type, String title, String message, 
                                 User user, Client client, Location location, Vehicule vehicule) {
        NotificationSettings settings = settingsDAO.findOrCreateByUser(user);
        
        if (!settings.isNotificationTypeEnabled(type)) {
            return; // User has disabled this type of notification
        }

        Notification notification = new Notification(type, title, message, user);
        notification.setRelatedClient(client);
        notification.setRelatedLocation(location);
        notification.setRelatedVehicule(vehicule);

        // Set action URL based on type
        setActionUrl(notification, type, client, location, vehicule);

        notificationDAO.save(notification);

        // Show system tray notification if enabled and not in quiet hours
        if (settings.isDesktopNotificationsEnabled() && !settings.isInQuietHours()) {
            Platform.runLater(() -> {
                systemTrayService.showNotification(notification);
                notificationDAO.markAsSystemTrayShown(notification.getId());
            });
        }
    }

    private void setActionUrl(Notification notification, Notification.NotificationType type, 
                            Client client, Location location, Vehicule vehicule) {
        switch (type) {
            case RAPPEL_RETOUR:
            case LOCATION_CONFIRMEE:
            case LOCATION_RESERVE:
                notification.setActionUrl("/view/location.fxml");
                if (location != null) {
                    notification.setActionData("{\"locationId\":" + location.getId() + "}");
                }
                break;
            case PAIEMENT_DU:
            case PAIEMENT_RECU:
                notification.setActionUrl("/view/paiement.fxml");
                if (client != null) {
                    notification.setActionData("{\"clientId\":" + client.getId() + "}");
                }
                break;
            case MAINTENANCE_DUE:
                notification.setActionUrl("/view/vehicle_maintenance.fxml");
                if (vehicule != null) {
                    notification.setActionData("{\"vehiculeId\":" + vehicule.getId() + "}");
                }
                break;
            case VEHICULE_DISPONIBLE:
                notification.setActionUrl("/view/vehicule.fxml");
                if (vehicule != null) {
                    notification.setActionData("{\"vehiculeId\":" + vehicule.getId() + "}");
                }
                break;
            default:
                notification.setActionUrl("/view/dashboard_content.fxml");
                break;
        }
    }

    public void createWelcomeAdminNotification(User admin) {
        // Get statistics for the welcome message
        List<Location> activeLocations = locationDAO.findAll().stream()
            .filter(l -> l.getDateFinReelle() == null)
            .toList();
        
        List<Location> returnsToday = locationDAO.findAll().stream()
            .filter(l -> l.getDateFinPrevue() != null && 
                        l.getDateFinPrevue().equals(LocalDate.now()) && 
                        l.getDateFinReelle() == null)
            .toList();

        String message = String.format(
            "Bienvenue %s! Vous avez %d locations actives. %d véhicules doivent être retournés aujourd'hui.",
            admin.getFullName(),
            activeLocations.size(),
            returnsToday.size()
        );

        createNotification(
            Notification.NotificationType.WELCOME_ADMIN,
            "Bienvenue Administrateur",
            message,
            admin
        );
    }

    public void createLocationConfirmedNotification(Location location, User user) {
        String message = String.format(
            "Location confirmée pour %s %s - Véhicule: %s du %s au %s",
            location.getClient().getPrenom(),
            location.getClient().getNom(),
            location.getVehicule().toString(),
            location.getDateDebut().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            location.getDateFinPrevue().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        );

        createNotification(
            Notification.NotificationType.LOCATION_CONFIRMEE,
            "Location Confirmée",
            message,
            user,
            location.getClient(),
            location,
            location.getVehicule()
        );
    }

    public void createReturnReminderNotification(Location location, User user) {
        String message = String.format(
            "Rappel: %s %s doit retourner le véhicule %s demain (%s)",
            location.getClient().getPrenom(),
            location.getClient().getNom(),
            location.getVehicule().toString(),
            location.getDateFinPrevue().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        );

        createNotification(
            Notification.NotificationType.RAPPEL_RETOUR,
            "Rappel de Retour",
            message,
            user,
            location.getClient(),
            location,
            location.getVehicule()
        );
    }

    private void checkForDueNotifications() {
        List<Notification> dueNotifications = notificationDAO.findScheduledNotifications();
        for (Notification notification : dueNotifications) {
            NotificationSettings settings = settingsDAO.findOrCreateByUser(notification.getUser());
            
            if (settings.isDesktopNotificationsEnabled() && !settings.isInQuietHours()) {
                Platform.runLater(() -> {
                    systemTrayService.showNotification(notification);
                    notificationDAO.markAsSystemTrayShown(notification.getId());
                });
            }
        }
    }

    private void checkForReturnReminders() {
        try {
            List<Location> activeLocations = locationDAO.findAll().stream()
                .filter(l -> l.getDateFinReelle() == null && l.getStatus() == Location.Status.EN_COURS)
                .toList();

            for (Location location : activeLocations) {
                LocalDate returnDate = location.getDateFinPrevue();
                LocalDate reminderDate = returnDate.minusDays(1); // 1 day before

                if (LocalDate.now().equals(reminderDate)) {
                    // Check if reminder already sent
                    List<Notification> existingReminders = notificationDAO.findByUserAndType(
                        null, // We'll need to get all users with admin/agent role
                        Notification.NotificationType.RAPPEL_RETOUR
                    );

                    boolean reminderExists = existingReminders.stream()
                        .anyMatch(n -> n.getRelatedLocation() != null && 
                                     n.getRelatedLocation().getId().equals(location.getId()) &&
                                     n.getCreatedAt().toLocalDate().equals(LocalDate.now()));

                    if (!reminderExists) {
                        // Send to all admin users
                        // For now, we'll create a generic notification
                        // In a real implementation, you'd get all admin users
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error checking return reminders: " + e.getMessage());
        }
    }

    private void checkForMaintenanceReminders() {
        try {
            // This would check for upcoming maintenance
            // Implementation depends on your VehicleMaintenance model
        } catch (Exception e) {
            System.err.println("Error checking maintenance reminders: " + e.getMessage());
        }
    }

    public List<Notification> getNotificationsForUser(User user) {
        return notificationDAO.findByUser(user);
    }

    public List<Notification> getUnreadNotificationsForUser(User user) {
        return notificationDAO.findUnreadByUser(user);
    }

    public long getUnreadCountForUser(User user) {
        return notificationDAO.countUnreadByUser(user);
    }

    public void markAsRead(Long notificationId) {
        notificationDAO.markAsRead(notificationId);
    }

    public void markAllAsReadForUser(User user) {
        notificationDAO.markAllAsReadForUser(user);
    }

    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}
