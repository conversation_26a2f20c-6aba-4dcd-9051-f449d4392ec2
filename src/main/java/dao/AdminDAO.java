package dao;

import model.Admin;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class AdminDAO {
    public void save(Admin admin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(admin);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Admin admin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(admin);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Admin> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Admin", Admin.class).list();
        } finally {
            session.close();
        }
    }

    public Admin findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Admin.class, id);
        } finally {
            session.close();
        }
    }

    public Admin findByUsername(String username) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Admin> result = session.createQuery("from Admin where username = :username", Admin.class)
                .setParameter("username", username)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }
} 