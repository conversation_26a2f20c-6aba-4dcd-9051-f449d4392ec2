package dao;

import model.VehicleFailure;
import model.Vehicule;
import util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import java.time.LocalDate;
import java.util.List;

public class VehicleFailureDAO {
    
    public void save(VehicleFailure failure) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            failure.setUpdatedDate(LocalDate.now());
            session.saveOrUpdate(failure);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw new RuntimeException("Error saving failure: " + e.getMessage(), e);
        }
    }
    
    public VehicleFailure findById(Long id) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.id = :id", 
                VehicleFailure.class
            ).setParameter("id", id).uniqueResult();
        }
    }
    
    public List<VehicleFailure> findAll() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).list();
        }
    }
    
    public List<VehicleFailure> findByVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "WHERE f.vehicule = :vehicule " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("vehicule", vehicule).list();
        }
    }
    
    public List<VehicleFailure> findUnresolvedFailures() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.status != :repaired " +
                "ORDER BY f.severity DESC, f.failureDate ASC", 
                VehicleFailure.class
            ).setParameter("repaired", VehicleFailure.RepairStatus.REPARE)
             .list();
        }
    }
    
    public List<VehicleFailure> findCriticalFailures() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.severity = :critical " +
                "AND f.status != :repaired " +
                "ORDER BY f.failureDate ASC", 
                VehicleFailure.class
            ).setParameter("critical", VehicleFailure.FailureSeverity.CRITIQUE)
             .setParameter("repaired", VehicleFailure.RepairStatus.REPARE)
             .list();
        }
    }
    
    public List<VehicleFailure> findByType(VehicleFailure.FailureType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.type = :type " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("type", type).list();
        }
    }
    
    public List<VehicleFailure> findBySeverity(VehicleFailure.FailureSeverity severity) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.severity = :severity " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("severity", severity).list();
        }
    }
    
    public List<VehicleFailure> findByStatus(VehicleFailure.RepairStatus status) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.status = :status " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("status", status).list();
        }
    }
    
    public List<VehicleFailure> findByDateRange(LocalDate startDate, LocalDate endDate) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.failureDate BETWEEN :startDate AND :endDate " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("startDate", startDate)
             .setParameter("endDate", endDate)
             .list();
        }
    }
    
    public List<VehicleFailure> findRecentFailuresForVehicle(Vehicule vehicule, int days) {
        LocalDate cutoffDate = LocalDate.now().minusDays(days);
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "WHERE f.vehicule = :vehicule " +
                "AND f.failureDate >= :cutoffDate " +
                "ORDER BY f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("vehicule", vehicule)
             .setParameter("cutoffDate", cutoffDate)
             .list();
        }
    }
    
    public long getFailureCountByVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT COUNT(f) FROM VehicleFailure f WHERE f.vehicule = :vehicule", 
                Long.class
            ).setParameter("vehicule", vehicule).uniqueResult();
        }
    }
    
    public long getFailureCountByType(VehicleFailure.FailureType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT COUNT(f) FROM VehicleFailure f WHERE f.type = :type", 
                Long.class
            ).setParameter("type", type).uniqueResult();
        }
    }
    
    public Double getTotalRepairCostForVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Double result = session.createQuery(
                "SELECT SUM(f.repairCost) FROM VehicleFailure f " +
                "WHERE f.vehicule = :vehicule " +
                "AND f.repairCost IS NOT NULL", 
                Double.class
            ).setParameter("vehicule", vehicule).uniqueResult();
            return result != null ? result : 0.0;
        }
    }
    
    public Double getTotalRepairCostByDateRange(LocalDate startDate, LocalDate endDate) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Double result = session.createQuery(
                "SELECT SUM(f.repairCost) FROM VehicleFailure f " +
                "WHERE f.repairDate BETWEEN :startDate AND :endDate " +
                "AND f.repairCost IS NOT NULL", 
                Double.class
            ).setParameter("startDate", startDate)
             .setParameter("endDate", endDate)
             .uniqueResult();
            return result != null ? result : 0.0;
        }
    }
    
    public Double getAverageRepairTimeByType(VehicleFailure.FailureType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            @SuppressWarnings("unchecked")
            List<Object[]> results = session.createQuery(
                "SELECT f.failureDate, f.repairDate FROM VehicleFailure f " +
                "WHERE f.type = :type " +
                "AND f.failureDate IS NOT NULL " +
                "AND f.repairDate IS NOT NULL"
            ).setParameter("type", type).list();
            
            if (results.isEmpty()) return 0.0;
            
            long totalDays = 0;
            for (Object[] result : results) {
                LocalDate failureDate = (LocalDate) result[0];
                LocalDate repairDate = (LocalDate) result[1];
                totalDays += java.time.temporal.ChronoUnit.DAYS.between(failureDate, repairDate);
            }
            
            return (double) totalDays / results.size();
        }
    }
    
    public List<VehicleFailure> findRecurringFailures(VehicleFailure.FailureType type, int occurrences) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.vehicule IN (" +
                "  SELECT f2.vehicule FROM VehicleFailure f2 " +
                "  WHERE f2.type = :type " +
                "  GROUP BY f2.vehicule " +
                "  HAVING COUNT(f2) >= :occurrences" +
                ") AND f.type = :type " +
                "ORDER BY f.vehicule.id, f.failureDate DESC", 
                VehicleFailure.class
            ).setParameter("type", type)
             .setParameter("occurrences", (long) occurrences)
             .list();
        }
    }
    
    public void delete(VehicleFailure failure) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            session.delete(failure);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw new RuntimeException("Error deleting failure: " + e.getMessage(), e);
        }
    }
    
    public void deleteById(Long id) {
        VehicleFailure failure = findById(id);
        if (failure != null) {
            delete(failure);
        }
    }
}
