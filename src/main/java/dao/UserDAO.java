
package dao;

import model.Admin;
import model.Agent;
import model.User;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class UserDAO {
    // Unified user lookup for login
    public User findUserByUsername(String username) {
        Admin admin = findAdminByUsername(username);
        if (admin != null) return admin;
        Agent agent = findAgentByUsername(username);
        if (agent != null) return agent;
        return null;
    }

    public void save(Object user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(user);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Object user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(user);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Admin> findAllAdmins() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Admin", Admin.class).list();
        } finally {
            session.close();
        }
    }

    public List<Agent> findAllAgents() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Agent", Agent.class).list();
        } finally {
            session.close();
        }
    }

    public Admin findAdminById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Admin.class, id);
        } finally {
            session.close();
        }
    }

    public Agent findAgentById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Agent.class, id);
        } finally {
            session.close();
        }
    }

    public Admin findAdminByUsername(String username) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Admin> result = session.createQuery("from Admin where username = :username", Admin.class)
                .setParameter("username", username)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    public Agent findAgentByUsername(String username) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Agent> result = session.createQuery("from Agent where username = :username", Agent.class)
                .setParameter("username", username)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    public List<User> findAll() {
        List<User> allUsers = new java.util.ArrayList<>();
        allUsers.addAll(findAllAdmins());
        allUsers.addAll(findAllAgents());
        return allUsers;
    }
}