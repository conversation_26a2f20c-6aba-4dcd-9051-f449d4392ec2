package dao;

import model.Vehicule;

import util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import java.util.List;

public class VehiculeDAO {
    // Ajoute ici des méthodes spécifiques de recherche/filtrage si besoin

    public void save(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(vehicule);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Vehicule> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Vehicule", Vehicule.class).list();
        } finally {
            session.close();
        }
    }

    public Vehicule findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Vehicule.class, id);
        } finally {
            session.close();
        }
    }

    public void delete(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(vehicule);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public Vehicule findByImmatriculation(String immatriculation) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Vehicule> result = session.createQuery("from Vehicule where immatriculation = :immatriculation", Vehicule.class)
                .setParameter("immatriculation", immatriculation)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    public List<Vehicule> findByEtat(String etat) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Vehicule where etat = :etat", Vehicule.class)
                .setParameter("etat", etat)
                .list();
        } finally {
            session.close();
        }
    }

    public List<Vehicule> searchVehicules(String searchTerm) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Vehicule where lower(marque) like :search or lower(modele) like :search or lower(immatriculation) like :search", Vehicule.class)
                .setParameter("search", "%" + searchTerm.toLowerCase() + "%")
                .list();
        } finally {
            session.close();
        }
    }
}