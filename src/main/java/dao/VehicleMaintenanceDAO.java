package dao;

import model.VehicleMaintenance;
import model.Vehicule;
import util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import java.time.LocalDate;
import java.util.List;

public class VehicleMaintenanceDAO {
    
    public void save(VehicleMaintenance maintenance) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            maintenance.setUpdatedDate(LocalDate.now());
            session.saveOrUpdate(maintenance);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw new RuntimeException("Error saving maintenance: " + e.getMessage(), e);
        }
    }
    
    public VehicleMaintenance findById(Long id) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.id = :id", 
                VehicleMaintenance.class
            ).setParameter("id", id).uniqueResult();
        }
    }
    
    public List<VehicleMaintenance> findAll() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).list();
        }
    }
    
    public List<VehicleMaintenance> findByVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "WHERE m.vehicule = :vehicule " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("vehicule", vehicule).list();
        }
    }
    
    public List<VehicleMaintenance> findOverdueMaintenance() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.nextMaintenanceDate < :today " +
                "AND m.status != :completed " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("today", LocalDate.now())
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .list();
        }
    }
    
    public List<VehicleMaintenance> findUpcomingMaintenance(int days) {
        LocalDate futureDate = LocalDate.now().plusDays(days);
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.nextMaintenanceDate BETWEEN :today AND :futureDate " +
                "AND m.status != :completed " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("today", LocalDate.now())
             .setParameter("futureDate", futureDate)
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .list();
        }
    }
    
    public List<VehicleMaintenance> findByType(VehicleMaintenance.MaintenanceType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.type = :type " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("type", type).list();
        }
    }
    
    public List<VehicleMaintenance> findByStatus(VehicleMaintenance.MaintenanceStatus status) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.status = :status " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("status", status).list();
        }
    }
    
    public List<VehicleMaintenance> findByDateRange(LocalDate startDate, LocalDate endDate) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.maintenanceDate BETWEEN :startDate AND :endDate " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("startDate", startDate)
             .setParameter("endDate", endDate)
             .list();
        }
    }
    
    public VehicleMaintenance findLastMaintenanceForVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "WHERE m.vehicule = :vehicule " +
                "AND m.status = :completed " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("vehicule", vehicule)
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .setMaxResults(1)
             .uniqueResult();
        }
    }
    
    public VehicleMaintenance findNextMaintenanceForVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "WHERE m.vehicule = :vehicule " +
                "AND m.nextMaintenanceDate >= :today " +
                "AND m.status != :completed " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("vehicule", vehicule)
             .setParameter("today", LocalDate.now())
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .setMaxResults(1)
             .uniqueResult();
        }
    }
    
    public Double getTotalMaintenanceCostForVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Double result = session.createQuery(
                "SELECT SUM(m.cost) FROM VehicleMaintenance m " +
                "WHERE m.vehicule = :vehicule " +
                "AND m.cost IS NOT NULL", 
                Double.class
            ).setParameter("vehicule", vehicule).uniqueResult();
            return result != null ? result : 0.0;
        }
    }
    
    public Double getTotalMaintenanceCostByDateRange(LocalDate startDate, LocalDate endDate) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Double result = session.createQuery(
                "SELECT SUM(m.cost) FROM VehicleMaintenance m " +
                "WHERE m.maintenanceDate BETWEEN :startDate AND :endDate " +
                "AND m.cost IS NOT NULL", 
                Double.class
            ).setParameter("startDate", startDate)
             .setParameter("endDate", endDate)
             .uniqueResult();
            return result != null ? result : 0.0;
        }
    }
    
    public long getMaintenanceCountByType(VehicleMaintenance.MaintenanceType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT COUNT(m) FROM VehicleMaintenance m WHERE m.type = :type", 
                Long.class
            ).setParameter("type", type).uniqueResult();
        }
    }
    
    public void delete(VehicleMaintenance maintenance) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            session.delete(maintenance);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw new RuntimeException("Error deleting maintenance: " + e.getMessage(), e);
        }
    }
    
    public void deleteById(Long id) {
        VehicleMaintenance maintenance = findById(id);
        if (maintenance != null) {
            delete(maintenance);
        }
    }
}
