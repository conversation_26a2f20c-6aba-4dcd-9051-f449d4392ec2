package dao;

import model.Agent;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class AgentDAO {
    public void save(Agent agent) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(agent);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Agent agent) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(agent);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Agent> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Agent", Agent.class).list();
        } finally {
            session.close();
        }
    }

    public Agent findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Agent.class, id);
        } finally {
            session.close();
        }
    }

    public Agent findByUsername(String username) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Agent> result = session.createQuery("from Agent where username = :username", Agent.class)
                .setParameter("username", username)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }
} 