package dao;

import model.NotificationSettings;
import model.User;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import util.HibernateUtil;

import java.util.List;

public class NotificationSettingsDAO {

    public void save(NotificationSettings settings) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(settings);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public NotificationSettings findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(NotificationSettings.class, id);
        } finally {
            session.close();
        }
    }

    public NotificationSettings findByUser(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<NotificationSettings> query = session.createQuery(
                "from NotificationSettings where user = :user", 
                NotificationSettings.class
            );
            query.setParameter("user", user);
            List<NotificationSettings> results = query.list();
            return results.isEmpty() ? null : results.get(0);
        } finally {
            session.close();
        }
    }

    public NotificationSettings findOrCreateByUser(User user) {
        NotificationSettings settings = findByUser(user);
        if (settings == null) {
            settings = new NotificationSettings(user);
            save(settings);
        }
        return settings;
    }

    public List<NotificationSettings> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from NotificationSettings", NotificationSettings.class).list();
        } finally {
            session.close();
        }
    }

    public void delete(NotificationSettings settings) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(session.contains(settings) ? settings : session.merge(settings));
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void resetToDefaults(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            // Delete existing settings
            Query<?> deleteQuery = session.createQuery("delete from NotificationSettings where user = :user");
            deleteQuery.setParameter("user", user);
            deleteQuery.executeUpdate();
            
            // Create new default settings
            NotificationSettings defaultSettings = new NotificationSettings(user);
            session.persist(defaultSettings);
            
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<User> findUsersWithDesktopNotificationsEnabled() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<User> query = session.createQuery(
                "select ns.user from NotificationSettings ns where ns.desktopNotificationsEnabled = true", 
                User.class
            );
            return query.list();
        } finally {
            session.close();
        }
    }

    public List<User> findUsersWithEmailNotificationsEnabled() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<User> query = session.createQuery(
                "select ns.user from NotificationSettings ns where ns.emailNotificationsEnabled = true", 
                User.class
            );
            return query.list();
        } finally {
            session.close();
        }
    }
}
