package dao;

import model.Paiement;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class PaiementDAO {
    public void save(Paiement paiement) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(paiement);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Paiement paiement) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(paiement);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Paiement> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            // Use JOIN FETCH to load related entities
            return session.createQuery(
                "SELECT p FROM Paiement p " +
                "LEFT JOIN FETCH p.location l " +
                "LEFT JOIN FETCH l.client " +
                "LEFT JOIN FETCH l.vehicule " +
                "ORDER BY p.datePaiement DESC",
                Paiement.class
            ).list();
        } finally {
            session.close();
        }
    }

    public Paiement findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery(
                "SELECT p FROM Paiement p " +
                "LEFT JOIN FETCH p.location l " +
                "LEFT JOIN FETCH l.client " +
                "LEFT JOIN FETCH l.vehicule " +
                "WHERE p.id = :id",
                Paiement.class
            ).setParameter("id", id).uniqueResult();
        } finally {
            session.close();
        }
    }

    public List<Paiement> findByLocationId(Long locationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Paiement where location.id = :locationId order by datePaiement desc", Paiement.class)
                .setParameter("locationId", locationId)
                .list();
        } finally {
            session.close();
        }
    }

    public double getTotalPaiementsByLocation(Long locationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(montant) from Paiement where location.id = :locationId", Double.class)
                .setParameter("locationId", locationId)
                .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    public List<Paiement> findByDateRange(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery(
                "SELECT p FROM Paiement p " +
                "LEFT JOIN FETCH p.location l " +
                "LEFT JOIN FETCH l.client " +
                "LEFT JOIN FETCH l.vehicule " +
                "WHERE p.datePaiement BETWEEN :startDate AND :endDate " +
                "ORDER BY p.datePaiement DESC",
                Paiement.class
            ).setParameter("startDate", startDate)
             .setParameter("endDate", endDate)
             .list();
        } finally {
            session.close();
        }
    }

    public double getTotalRevenue() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(montant) from Paiement", Double.class)
                .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }
}