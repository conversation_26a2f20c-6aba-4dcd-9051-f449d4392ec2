package dao;

import model.Client;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class ClientDAO {
    public void save(Client client) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(client);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Client client) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(client);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Client> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Client", Client.class).list();
        } finally {
            session.close();
        }
    }

    public Client findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Client.class, id);
        } finally {
            session.close();
        }
    }

    public Client findByCin(String cin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Client> result = session.createQuery("from Client where cin = :cin", Client.class)
                .setParameter("cin", cin)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    public Client findByEmail(String email) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<Client> result = session.createQuery("from Client where email = :email", Client.class)
                .setParameter("email", email)
                .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }
}