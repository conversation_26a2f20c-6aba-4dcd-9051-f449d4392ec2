package dao;

import model.Notification;
import model.User;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import util.HibernateUtil;

import java.time.LocalDateTime;
import java.util.List;

public class NotificationDAO {

    public void save(Notification notification) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(notification);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public Notification findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Notification.class, id);
        } finally {
            session.close();
        }
    }

    public List<Notification> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Notification order by createdAt desc", Notification.class).list();
        } finally {
            session.close();
        }
    }

    public List<Notification> findByUser(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where user = :user order by createdAt desc", 
                Notification.class
            );
            query.setParameter("user", user);
            return query.list();
        } finally {
            session.close();
        }
    }

    public List<Notification> findUnreadByUser(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where user = :user and isRead = false order by createdAt desc", 
                Notification.class
            );
            query.setParameter("user", user);
            return query.list();
        } finally {
            session.close();
        }
    }

    public List<Notification> findByUserAndType(User user, Notification.NotificationType type) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where user = :user and type = :type order by createdAt desc", 
                Notification.class
            );
            query.setParameter("user", user);
            query.setParameter("type", type);
            return query.list();
        } finally {
            session.close();
        }
    }

    public List<Notification> findScheduledNotifications() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where scheduledFor is not null and scheduledFor <= :now and isRead = false", 
                Notification.class
            );
            query.setParameter("now", LocalDateTime.now());
            return query.list();
        } finally {
            session.close();
        }
    }

    public List<Notification> findPendingSystemTrayNotifications() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where isSystemTrayShown = false and (scheduledFor is null or scheduledFor <= :now)", 
                Notification.class
            );
            query.setParameter("now", LocalDateTime.now());
            return query.list();
        } finally {
            session.close();
        }
    }

    public long countUnreadByUser(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Long> query = session.createQuery(
                "select count(*) from Notification where user = :user and isRead = false", 
                Long.class
            );
            query.setParameter("user", user);
            return query.uniqueResult();
        } finally {
            session.close();
        }
    }

    public void markAsRead(Long notificationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Notification notification = session.get(Notification.class, notificationId);
            if (notification != null) {
                notification.markAsRead();
                session.merge(notification);
            }
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void markAllAsReadForUser(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Query<?> query = session.createQuery(
                "update Notification set isRead = true, readAt = :readAt where user = :user and isRead = false"
            );
            query.setParameter("readAt", LocalDateTime.now());
            query.setParameter("user", user);
            query.executeUpdate();
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void markAsSystemTrayShown(Long notificationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Notification notification = session.get(Notification.class, notificationId);
            if (notification != null) {
                notification.setSystemTrayShown(true);
                session.merge(notification);
            }
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Notification notification) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(session.contains(notification) ? notification : session.merge(notification));
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void deleteOldNotifications(int daysOld) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
            Query<?> query = session.createQuery(
                "delete from Notification where createdAt < :cutoffDate and isRead = true"
            );
            query.setParameter("cutoffDate", cutoffDate);
            query.executeUpdate();
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Notification> findRecentByUser(User user, int limit) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Query<Notification> query = session.createQuery(
                "from Notification where user = :user order by createdAt desc", 
                Notification.class
            );
            query.setParameter("user", user);
            query.setMaxResults(limit);
            return query.list();
        } finally {
            session.close();
        }
    }
}
