package dao;

import model.Location;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;

public class LocationDAO {
    public void save(Location location) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(location);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Location location) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(location);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Location> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Location", Location.class).list();
        } finally {
            session.close();
        }
    }

    public Location findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Location.class, id);
        } finally {
            session.close();
        }
    }

    public List<Location> findByVehiculeId(Long vehiculeId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Location where vehicule.id = :vehiculeId order by dateDebut desc", Location.class)
                    .setParameter("vehiculeId", vehiculeId)
                    .list();
        } finally {
            session.close();
        }
    }

    public List<Location> findByClientId(Long clientId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Location where client.id = :clientId order by dateDebut desc", Location.class)
                .setParameter("clientId", clientId)
                .list();
        } finally {
            session.close();
        }
    }

    public List<Location> findActiveByVehiculeId(Long vehiculeId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Location where vehicule.id = :vehiculeId and (dateFinReelle is null or dateFinReelle > current_date) order by dateDebut desc", Location.class)
                    .setParameter("vehiculeId", vehiculeId)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Checks if a vehicle is available for the given period (no overlap with existing locations).
     * If excludeLocationId is not null, excludes that location (for update scenarios).
     */
    public boolean isVehiculeAvailable(Long vehiculeId, java.time.LocalDate start, java.time.LocalDate end, Long excludeLocationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from Location where vehicule.id = :vehiculeId " +
                    "and ((dateDebut <= :end and dateFinPrevue >= :start)" +
                    (excludeLocationId != null ? " and id != :excludeId" : "") + ")";
            var query = session.createQuery(hql, Location.class)
                    .setParameter("vehiculeId", vehiculeId)
                    .setParameter("start", start)
                    .setParameter("end", end);
            if (excludeLocationId != null) {
                query.setParameter("excludeId", excludeLocationId);
            }
            return query.list().isEmpty();
        } finally {
            session.close();
        }
    }

    /**
     * Batch update status for all locations (optional utility).
     */
    public void updateAllStatuses() {
        List<Location> all = findAll();
        for (Location loc : all) {
            loc.updateStatus();
            save(loc);
        }
    }
}
