import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Test application for enhanced controllers
 */
public class TestEnhancedControllers extends Application {
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        // Initialize database first
        util.DatabaseInitializer.initialize();
        
        try {
            // Test payment history
            FXMLLoader paymentLoader = new FXMLLoader(getClass().getResource("/view/historique_paiements_enhanced.fxml"));
            Parent paymentRoot = paymentLoader.load();
            
            Stage paymentStage = new Stage();
            paymentStage.setTitle("Test - Enhanced Payment History");
            paymentStage.setScene(new Scene(paymentRoot, 1200, 800));
            paymentStage.show();
            
            System.out.println("✓ Enhanced payment history loaded successfully!");
            
            // Test location history
            FXMLLoader locationLoader = new FXMLLoader(getClass().getResource("/view/historique_locations_enhanced.fxml"));
            Parent locationRoot = locationLoader.load();
            
            Stage locationStage = new Stage();
            locationStage.setTitle("Test - Enhanced Location History");
            locationStage.setScene(new Scene(locationRoot, 1200, 800));
            locationStage.show();
            
            System.out.println("✓ Enhanced location history loaded successfully!");
            
        } catch (Exception e) {
            System.err.println("✗ Failed to load enhanced views: " + e.getMessage());
            e.printStackTrace();
            
            // Fallback to login view
            Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            primaryStage.setTitle("LocationV12 - Login");
            primaryStage.setScene(new Scene(root, 500, 800));
            primaryStage.show();
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
