import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Test application for enhanced controllers
 */
public class TestEnhancedControllers extends Application {

    @Override
    public void start(Stage primaryStage) throws Exception {
        System.out.println("=== Testing Enhanced Controllers ===");

        // Initialize database first
        try {
            util.DatabaseInitializer.initialize();
            System.out.println("✓ Database initialized successfully");
        } catch (Exception e) {
            System.err.println("✗ Database initialization failed: " + e.getMessage());
        }

        // Test payment history
        try {
            System.out.println("Loading enhanced payment history...");
            FXMLLoader paymentLoader = new FXMLLoader(getClass().getResource("/view/historique_paiements_enhanced.fxml"));
            Parent paymentRoot = paymentLoader.load();

            Stage paymentStage = new Stage();
            paymentStage.setTitle("✅ Enhanced Payment History - Working");
            paymentStage.setScene(new Scene(paymentRoot, 1400, 900));
            paymentStage.setX(100);
            paymentStage.setY(100);
            paymentStage.show();

            System.out.println("✓ Enhanced payment history loaded successfully!");

        } catch (Exception e) {
            System.err.println("✗ Failed to load payment history: " + e.getMessage());
            e.printStackTrace();
        }

        // Test location history
        try {
            System.out.println("Loading enhanced location history...");
            FXMLLoader locationLoader = new FXMLLoader(getClass().getResource("/view/historique_locations_enhanced.fxml"));
            Parent locationRoot = locationLoader.load();

            Stage locationStage = new Stage();
            locationStage.setTitle("✅ Enhanced Location History - Working");
            locationStage.setScene(new Scene(locationRoot, 1400, 900));
            locationStage.setX(200);
            locationStage.setY(150);
            locationStage.show();

            System.out.println("✓ Enhanced location history loaded successfully!");

        } catch (Exception e) {
            System.err.println("✗ Failed to load location history: " + e.getMessage());
            e.printStackTrace();
        }

        // Show main login as fallback
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            primaryStage.setTitle("LocationV12 - Login (Fallback)");
            primaryStage.setScene(new Scene(root, 500, 800));
            primaryStage.setX(50);
            primaryStage.setY(50);
            primaryStage.show();

            System.out.println("✓ Login view loaded as fallback");

        } catch (Exception e) {
            System.err.println("✗ Even login view failed: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== Test Complete ===");
    }

    public static void main(String[] args) {
        launch(args);
    }
}
