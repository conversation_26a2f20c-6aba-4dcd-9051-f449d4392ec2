package test;

import dao.AdminDAO;
import dao.NotificationDAO;
import dao.NotificationSettingsDAO;
import model.Admin;
import model.Notification;
import model.NotificationSettings;
import service.NotificationService;
import service.SystemTrayService;

import java.util.List;

/**
 * Simple test class to verify the notification system functionality
 */
public class NotificationSystemTest {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Notification System ===");
        
        // Initialize database
        util.DatabaseInitializer.initialize();
        
        testNotificationDAO();
        testNotificationSettings();
        testNotificationService();
        testSystemTrayService();
        
        System.out.println("=== All tests completed ===");
    }
    
    private static void testNotificationDAO() {
        System.out.println("\n--- Testing NotificationDAO ---");
        
        try {
            NotificationDAO notificationDAO = new NotificationDAO();
            AdminDAO adminDAO = new AdminDAO();
            
            List<Admin> admins = adminDAO.findAll();
            if (admins.isEmpty()) {
                System.out.println("No admin users found. Creating test admin...");
                Admin testAdmin = new Admin();
                testAdmin.setUsername("testadmin");
                testAdmin.setPasswordHash(org.mindrot.jbcrypt.BCrypt.hashpw("test123", org.mindrot.jbcrypt.BCrypt.gensalt()));
                testAdmin.setRole("admin");
                testAdmin.setEmail("<EMAIL>");
                testAdmin.setStatus("ACTIF");
                adminDAO.save(testAdmin);
                admins = adminDAO.findAll();
            }
            
            Admin admin = admins.get(0);
            
            // Test creating notification
            Notification testNotification = new Notification(
                Notification.NotificationType.SYSTEM_ALERT,
                "Test Notification",
                "This is a test notification to verify the system works correctly.",
                admin
            );
            testNotification.setPriority(Notification.Priority.HIGH);
            
            notificationDAO.save(testNotification);
            System.out.println("✓ Notification created successfully");
            
            // Test retrieving notifications
            List<Notification> userNotifications = notificationDAO.findByUser(admin);
            System.out.println("✓ Found " + userNotifications.size() + " notifications for user");
            
            // Test unread count
            long unreadCount = notificationDAO.countUnreadByUser(admin);
            System.out.println("✓ Unread notifications: " + unreadCount);
            
        } catch (Exception e) {
            System.err.println("✗ NotificationDAO test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testNotificationSettings() {
        System.out.println("\n--- Testing NotificationSettings ---");
        
        try {
            NotificationSettingsDAO settingsDAO = new NotificationSettingsDAO();
            AdminDAO adminDAO = new AdminDAO();
            
            List<Admin> admins = adminDAO.findAll();
            if (!admins.isEmpty()) {
                Admin admin = admins.get(0);
                
                // Test creating/retrieving settings
                NotificationSettings settings = settingsDAO.findOrCreateByUser(admin);
                System.out.println("✓ Settings retrieved/created for user");
                
                // Test updating settings
                settings.setDesktopNotificationsEnabled(true);
                settings.setRappelRetourDaysBefore(2);
                settingsDAO.save(settings);
                System.out.println("✓ Settings updated successfully");
                
                // Test retrieving updated settings
                NotificationSettings updatedSettings = settingsDAO.findByUser(admin);
                if (updatedSettings != null && updatedSettings.getRappelRetourDaysBefore() == 2) {
                    System.out.println("✓ Settings persistence verified");
                } else {
                    System.out.println("✗ Settings persistence failed");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ NotificationSettings test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testNotificationService() {
        System.out.println("\n--- Testing NotificationService ---");
        
        try {
            NotificationService notificationService = NotificationService.getInstance();
            AdminDAO adminDAO = new AdminDAO();
            
            List<Admin> admins = adminDAO.findAll();
            if (!admins.isEmpty()) {
                Admin admin = admins.get(0);
                
                // Test creating notification through service
                notificationService.createNotification(
                    Notification.NotificationType.SYSTEM_ALERT,
                    "Service Test",
                    "Testing notification creation through NotificationService",
                    admin
                );
                System.out.println("✓ Notification created through service");
                
                // Test retrieving notifications
                List<Notification> notifications = notificationService.getNotificationsForUser(admin);
                System.out.println("✓ Retrieved " + notifications.size() + " notifications through service");
                
                // Test unread count
                long unreadCount = notificationService.getUnreadCountForUser(admin);
                System.out.println("✓ Unread count through service: " + unreadCount);
            }
            
        } catch (Exception e) {
            System.err.println("✗ NotificationService test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSystemTrayService() {
        System.out.println("\n--- Testing SystemTrayService ---");
        
        try {
            SystemTrayService systemTrayService = SystemTrayService.getInstance();
            
            if (systemTrayService.isSupported()) {
                System.out.println("✓ System tray is supported");
                
                // Test updating tooltip
                systemTrayService.updateTrayIconTooltip("LocationV12 - Test Mode");
                System.out.println("✓ Tray icon tooltip updated");
                
                // Test notification badge
                systemTrayService.updateNotificationBadge(5);
                System.out.println("✓ Notification badge updated");
                
            } else {
                System.out.println("⚠ System tray is not supported on this platform");
            }
            
        } catch (Exception e) {
            System.err.println("✗ SystemTrayService test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
