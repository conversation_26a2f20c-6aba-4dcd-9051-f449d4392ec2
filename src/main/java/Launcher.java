/**
 * Launcher class for the JavaFX application.
 * This class is needed to avoid module path issues when creating native executables.
 * It simply launches the main JavaFX application.
 */
public class Launcher {
    public static void main(String[] args) {
        // Initialize database before launching JavaFX
        util.DatabaseInitializer.initialize();
        
        // Launch the JavaFX application
        MainApp.main(args);
    }
}
