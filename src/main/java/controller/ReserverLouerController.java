package controller;

import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TextField;
import javafx.scene.control.DatePicker;
import javafx.scene.control.CheckBox;
import javafx.scene.control.TextArea;
import javafx.scene.control.Label;
import javafx.scene.control.TabPane;
import javafx.scene.control.Tab;
import javafx.scene.control.TableView;
import javafx.scene.control.TableColumn;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.MouseEvent;
import javafx.event.ActionEvent;
import javafx.stage.Stage;
import model.Client;
import model.Vehicule;
import dao.ClientDAO;
import dao.LocationDAO;
import model.Location;
import java.util.List;
import java.time.LocalDate;

public class ReserverLouerController {
    @FXML private ComboBox<Client> clientComboBox;
    @FXML private TextField nomField;
    @FXML private TextField prenomField;
    @FXML private TextField emailField;
    @FXML private TextField telField;
    @FXML private ComboBox<Vehicule> vehiculeComboBox;
    @FXML private Label vehiculeDetailsLabel;
    @FXML private TableView<Location> historyTable;
    @FXML private TableColumn<Location, LocalDate> dateColumn;
    @FXML private TableColumn<Location, String> clientColumn;
    @FXML private TableColumn<Location, String> statutColumn;
    @FXML private TableColumn<Location, Double> prixColumn;
    @FXML private TableView<Location> availabilityTable;
    @FXML private TableColumn<Location, LocalDate> dateDebutColumn;
    @FXML private TableColumn<Location, LocalDate> dateFinColumn;
    @FXML private TableColumn<Location, String> statutDispoColumn;
    @FXML private TextField vehiculeField;
    @FXML private TextField prixJourField;
    @FXML private DatePicker dateDebutPicker;
    @FXML private DatePicker dateFinPicker;
    @FXML private CheckBox optionAssurance;
    @FXML private CheckBox optionGPS;
    @FXML private CheckBox optionSiegeBebe;
    @FXML private ComboBox<String> paiementComboBox;
    @FXML private TextArea notesField;
    @FXML private Label totalPrixLabel;
    @FXML private TextField pickupLocationField;
    @FXML private TextField deliveryLocationField;
    @FXML private ComboBox<String> insuranceTypeComboBox;
    @FXML private ComboBox<String> fuelPolicyComboBox;
    @FXML private CheckBox optionAdditionalDriver;
    private Vehicule vehicule;
    private final ClientDAO clientDAO = new ClientDAO();
    private final LocationDAO locationDAO = new LocationDAO();
    private double prixParJour = 0;

    @FXML
    public void initialize() {
        // Populate vehiculeComboBox
        List<Vehicule> vehicules = new dao.VehiculeDAO().findAll();
        vehiculeComboBox.setItems(FXCollections.observableArrayList(vehicules));
        // Custom cell factory and converter for vehiculeComboBox
        vehiculeComboBox.setCellFactory(cb -> new javafx.scene.control.ListCell<Vehicule>() {
            @Override
            protected void updateItem(Vehicule v, boolean empty) {
                super.updateItem(v, empty);
                if (empty || v == null) {
                    setText(null);
                } else {
                    setText(v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : ""));
                }
            }
        });
        vehiculeComboBox.setButtonCell(new javafx.scene.control.ListCell<Vehicule>() {
            @Override
            protected void updateItem(Vehicule v, boolean empty) {
                super.updateItem(v, empty);
                if (empty || v == null) {
                    setText(null);
                } else {
                    setText(v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : ""));
                }
            }
        });
        vehiculeComboBox.setConverter(new javafx.util.StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule v) {
                if (v == null) return "";
                return v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : "");
            }
            @Override
            public Vehicule fromString(String s) { return null; }
        });
        vehiculeComboBox.setOnAction(e -> onVehiculeSelected());
        // Populate clientComboBox
        List<Client> clients = clientDAO.findAll();
        clientComboBox.setItems(FXCollections.observableArrayList(clients));
        // Custom cell factory and converter for clientComboBox
        clientComboBox.setCellFactory(cb -> new javafx.scene.control.ListCell<Client>() {
            @Override
            protected void updateItem(Client c, boolean empty) {
                super.updateItem(c, empty);
                if (empty || c == null) {
                    setText(null);
                } else {
                    setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                }
            }
        });
        clientComboBox.setButtonCell(new javafx.scene.control.ListCell<Client>() {
            @Override
            protected void updateItem(Client c, boolean empty) {
                super.updateItem(c, empty);
                if (empty || c == null) {
                    setText(null);
                } else {
                    setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                }
            }
        });
        clientComboBox.setConverter(new javafx.util.StringConverter<Client>() {
            @Override
            public String toString(Client c) {
                if (c == null) return "";
                return c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : "");
            }
            @Override
            public Client fromString(String s) { return null; }
        });
        // Set up history table columns
        if (historyTable != null) {
            dateColumn.setCellValueFactory(new PropertyValueFactory<>("dateDebut"));
            clientColumn.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getClient() != null ? cellData.getValue().getClient().getNom() + " " + cellData.getValue().getClient().getPrenom() : ""
            ));
            statutColumn.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinReelle() == null ? "En cours" : "Terminée"
            ));
            prixColumn.setCellValueFactory(new PropertyValueFactory<>("prixTotal"));
        }
        // Set up availability table columns
        if (availabilityTable != null) {
            dateDebutColumn.setCellValueFactory(new PropertyValueFactory<>("dateDebut"));
            dateFinColumn.setCellValueFactory(new PropertyValueFactory<>("dateFinPrevue"));
            statutDispoColumn.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinReelle() == null ? "Réservé" : "Disponible"
            ));
        }
        // Populate insurance, fuel, payment ComboBoxes
        insuranceTypeComboBox.setItems(FXCollections.observableArrayList("Standard", "Tous risques", "Premium"));
        fuelPolicyComboBox.setItems(FXCollections.observableArrayList("Plein à plein", "Plein à vide", "Retour identique"));
        paiementComboBox.setItems(FXCollections.observableArrayList("Espèces", "Carte bancaire", "Virement"));
        // Default selection with error handling
        try {
            if (!vehiculeComboBox.getItems().isEmpty()) {
                vehiculeComboBox.getSelectionModel().selectFirst();
            }
            if (!clientComboBox.getItems().isEmpty()) {
                clientComboBox.getSelectionModel().selectFirst();
            }
            if (!insuranceTypeComboBox.getItems().isEmpty()) {
                insuranceTypeComboBox.getSelectionModel().selectFirst();
            }
            if (!fuelPolicyComboBox.getItems().isEmpty()) {
                fuelPolicyComboBox.getSelectionModel().selectFirst();
            }
            if (!paiementComboBox.getItems().isEmpty()) {
                paiementComboBox.getSelectionModel().selectFirst();
            }
            // Trigger initial vehicle selection
            onVehiculeSelected();
        } catch (Exception e) {
            System.err.println("Error in default selection: " + e.getMessage());
        }
    }

    private void onVehiculeSelected() {
        try {
            Vehicule v = vehiculeComboBox.getValue();
            if (v == null) {
                vehiculeDetailsLabel.setText("");
                prixParJour = 0.0;
                updateTotalPrix();
                return;
            }

            this.vehicule = v;

            // Build details string with null-safe operations
            StringBuilder details = new StringBuilder();
            details.append(v.getMarque() != null ? v.getMarque() : "").append(" ")
                   .append(v.getModele() != null ? v.getModele() : "").append("\n");
            details.append("Immatriculation: ").append(v.getImmatriculation() != null ? v.getImmatriculation() : "N/A").append("\n");
            details.append("Prix: ").append(v.getPrixParJour() != null ? v.getPrixParJour() : 0).append(" DH/jour\n");
            details.append("État: ").append(v.getEtat() != null ? v.getEtat() : "N/A").append("\n");
            details.append("Carburant: ").append(v.getCarburant() != null ? v.getCarburant() : "N/A").append("\n");
            details.append("Métrage: ").append(v.getMetrage() != null ? v.getMetrage() : "N/A").append(" km\n");
            details.append("Date d'acquisition: ").append(v.getDateAcquisition() != null ? v.getDateAcquisition() : "N/A").append("\n");
            details.append("Dernière utilisation: ").append(v.getLastUsed() != null ? v.getLastUsed() : "N/A").append("\n");
            details.append("Nombre de chevaux: ").append(v.getNbreChevaux() != null ? v.getNbreChevaux() : "N/A").append("\n");
            details.append("Assurance: ").append(v.getAssuranceCompagnie() != null ? v.getAssuranceCompagnie() : "N/A")
                   .append(", exp: ").append(v.getAssuranceExpiration() != null ? v.getAssuranceExpiration() : "N/A")
                   .append(", n°: ").append(v.getAssuranceNumero() != null ? v.getAssuranceNumero() : "N/A");

            vehiculeDetailsLabel.setText(details.toString());
            prixParJour = v.getPrixParJour() != null ? v.getPrixParJour() : 0.0;
            updateTotalPrix();
            // Fill history and availability tables
            fillHistoryAndAvailability(v);
        } catch (Exception e) {
            System.err.println("Error in vehicle selection: " + e.getMessage());
            vehiculeDetailsLabel.setText("Erreur lors du chargement des détails du véhicule");
            prixParJour = 0.0;
            updateTotalPrix();
        }
    }

    private void fillHistoryAndAvailability(Vehicule v) {
        List<Location> history = locationDAO.findByVehiculeId(v.getId());
        if (historyTable != null) historyTable.setItems(FXCollections.observableArrayList(history));
        List<Location> active = locationDAO.findActiveByVehiculeId(v.getId());
        if (availabilityTable != null) availabilityTable.setItems(FXCollections.observableArrayList(active));
    }

    @FXML
    private void handleCreateClient() {
        nomField.clear();
        prenomField.clear();
        emailField.clear();
        telField.clear();
        clientComboBox.getSelectionModel().clearSelection();
    }

    @FXML
    private void handleConfirm() {
        Client client = clientComboBox.getValue();
        if (client == null) {
            client = new Client();
            client.setNom(nomField.getText());
            client.setPrenom(prenomField.getText());
            client.setEmail(emailField.getText());
            client.setTelephone(telField.getText());
            clientDAO.save(client);
        }
        Location location = new Location();
        location.setVehicule(vehicule);
        location.setClient(client);
        location.setDateDebut(dateDebutPicker.getValue() != null ? dateDebutPicker.getValue() : java.time.LocalDate.now());
        location.setDateFinPrevue(dateFinPicker.getValue());
        // Save new options (extend Location model to persist these if needed)
        // location.setPickupLocation(pickupLocationField.getText());
        // location.setDeliveryLocation(deliveryLocationField.getText());
        // location.setInsuranceType(insuranceTypeComboBox.getValue());
        // location.setFuelPolicy(fuelPolicyComboBox.getValue());
        // location.setAdditionalDriver(optionAdditionalDriver.isSelected());
        // location.setNotes(notesField.getText());
        // location.setPaymentMethod(paiementComboBox.getValue());
        // Calculate total price
        double total = calculateTotalPrix();
        location.setPrixTotal(total);
        locationDAO.save(location);
        ((Stage) clientComboBox.getScene().getWindow()).close();
    }

    @FXML
    private void handleCancel() {
        ((Stage) clientComboBox.getScene().getWindow()).close();
    }

    @FXML
    private void updateTotalPrix() {
        double total = calculateTotalPrix();
        if (totalPrixLabel != null) totalPrixLabel.setText(String.format("%.2f DH", total));
    }

    private double calculateTotalPrix() {
        if (dateDebutPicker == null || dateFinPicker == null || dateDebutPicker.getValue() == null || dateFinPicker.getValue() == null) return prixParJour;
        long days = java.time.temporal.ChronoUnit.DAYS.between(dateDebutPicker.getValue(), dateFinPicker.getValue());
        if (days < 1) days = 1;
        double total = days * prixParJour;
        if (optionAssurance != null && optionAssurance.isSelected()) total += 50;
        if (optionGPS != null && optionGPS.isSelected()) total += 20;
        if (optionSiegeBebe != null && optionSiegeBebe.isSelected()) total += 15;
        if (optionAdditionalDriver != null && optionAdditionalDriver.isSelected()) total += 30;
        if (insuranceTypeComboBox != null && insuranceTypeComboBox.getValue() != null) {
            switch (insuranceTypeComboBox.getValue()) {
                case "Tous risques": total += 100; break;
                case "Premium": total += 200; break;
            }
        }
        return total;
    }

    @FXML
    private void handleExportContrat(ActionEvent event) {
        // TODO: Implement contract export logic (PDF/print)
        // For now, just show a dialog or print to console
        System.out.println("Export contrat for: " + vehiculeComboBox.getValue() + ", client: " + clientComboBox.getValue());
    }

    public void setVehicule(Vehicule v) {
        if (vehiculeComboBox != null) {
            vehiculeComboBox.getSelectionModel().select(v);
        }
        this.vehicule = v;
        onVehiculeSelected();
    }
} 