package controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import model.NotificationSettings;
import model.User;
import dao.NotificationSettingsDAO;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalTime;
import java.util.ResourceBundle;

public class AccountSettingsController implements Initializable {

    // General Settings
    @FXML private CheckBox chkDesktopNotifications;
    @FXML private CheckBox chkEmailNotifications;
    @FXML private CheckBox chkSoundEnabled;

    // Notification Types
    @FXML private CheckBox chkRappelRetour;
    @FXML private CheckBox chkLocationConfirmee;
    @FXML private CheckBox chkLocationReserve;
    @FXML private CheckBox chkPaiementDu;
    @FXML private CheckBox chkPaiementRecu;
    @FXML private CheckBox chkMaintenanceDue;
    @FXML private CheckBox chkVehiculeDisponible;
    @FXML private CheckBox chkWelcomeAdmin;
    @FXML private CheckBox chkSystemAlert;
    @FXML private CheckBox chkClientBirthday;

    // Timing Settings
    @FXML private Spinner<Integer> spnRappelRetourDays;
    @FXML private Spinner<Integer> spnMaintenanceDays;
    @FXML private Spinner<Integer> spnContractExpiringDays;

    // Quiet Hours
    @FXML private CheckBox chkRespectQuietHours;
    @FXML private Spinner<Integer> spnQuietHoursStart;
    @FXML private Spinner<Integer> spnQuietMinutesStart;
    @FXML private Spinner<Integer> spnQuietHoursEnd;
    @FXML private Spinner<Integer> spnQuietMinutesEnd;

    // Display Settings
    @FXML private Spinner<Integer> spnMaxNotifications;
    @FXML private CheckBox chkAutoMarkAsRead;
    @FXML private Spinner<Integer> spnAutoMarkDays;

    // Buttons
    @FXML private Button btnSaveSettings;
    @FXML private Button btnResetToDefaults;

    private User currentUser;
    private NotificationSettingsDAO settingsDAO;
    private NotificationSettings currentSettings;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currentUser = (User) LoginController.loggedInUser;
        settingsDAO = new NotificationSettingsDAO();

        setupSpinners();
        loadCurrentSettings();
    }

    private void setupSpinners() {
        // Initialize spinners with proper value factories
        spnRappelRetourDays.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 30, 1));
        spnMaintenanceDays.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 90, 7));
        spnContractExpiringDays.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 365, 30));
        
        spnQuietHoursStart.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 23, 22));
        spnQuietMinutesStart.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 59, 0));
        spnQuietHoursEnd.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 23, 8));
        spnQuietMinutesEnd.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 59, 0));
        
        spnMaxNotifications.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(10, 200, 50));
        spnAutoMarkDays.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 365, 30));

        // Make spinners editable
        spnRappelRetourDays.setEditable(true);
        spnMaintenanceDays.setEditable(true);
        spnContractExpiringDays.setEditable(true);
        spnQuietHoursStart.setEditable(true);
        spnQuietMinutesStart.setEditable(true);
        spnQuietHoursEnd.setEditable(true);
        spnQuietMinutesEnd.setEditable(true);
        spnMaxNotifications.setEditable(true);
        spnAutoMarkDays.setEditable(true);
    }

    private void loadCurrentSettings() {
        if (currentUser == null) return;

        currentSettings = settingsDAO.findOrCreateByUser(currentUser);

        // Load general settings
        chkDesktopNotifications.setSelected(currentSettings.isDesktopNotificationsEnabled());
        chkEmailNotifications.setSelected(currentSettings.isEmailNotificationsEnabled());
        chkSoundEnabled.setSelected(currentSettings.isSoundEnabled());

        // Load notification type settings
        chkRappelRetour.setSelected(currentSettings.isRappelRetourEnabled());
        chkLocationConfirmee.setSelected(currentSettings.isLocationConfirmeeEnabled());
        chkLocationReserve.setSelected(currentSettings.isLocationReserveEnabled());
        chkPaiementDu.setSelected(currentSettings.isPaiementDuEnabled());
        chkPaiementRecu.setSelected(currentSettings.isPaiementRecuEnabled());
        chkMaintenanceDue.setSelected(currentSettings.isMaintenanceDueEnabled());
        chkVehiculeDisponible.setSelected(currentSettings.isVehiculeDisponibleEnabled());
        chkWelcomeAdmin.setSelected(currentSettings.isWelcomeAdminEnabled());
        chkSystemAlert.setSelected(currentSettings.isSystemAlertEnabled());
        chkClientBirthday.setSelected(currentSettings.isClientBirthdayEnabled());

        // Load timing settings
        spnRappelRetourDays.getValueFactory().setValue(currentSettings.getRappelRetourDaysBefore());
        spnMaintenanceDays.getValueFactory().setValue(currentSettings.getMaintenanceDaysBefore());
        spnContractExpiringDays.getValueFactory().setValue(currentSettings.getContractExpiringDaysBefore());

        // Load quiet hours
        chkRespectQuietHours.setSelected(currentSettings.isRespectQuietHours());
        if (currentSettings.getQuietHoursStart() != null) {
            spnQuietHoursStart.getValueFactory().setValue(currentSettings.getQuietHoursStart().getHour());
            spnQuietMinutesStart.getValueFactory().setValue(currentSettings.getQuietHoursStart().getMinute());
        }
        if (currentSettings.getQuietHoursEnd() != null) {
            spnQuietHoursEnd.getValueFactory().setValue(currentSettings.getQuietHoursEnd().getHour());
            spnQuietMinutesEnd.getValueFactory().setValue(currentSettings.getQuietHoursEnd().getMinute());
        }

        // Load display settings
        spnMaxNotifications.getValueFactory().setValue(currentSettings.getMaxNotificationsToShow());
        chkAutoMarkAsRead.setSelected(currentSettings.isAutoMarkAsReadAfterDays());
        spnAutoMarkDays.getValueFactory().setValue(currentSettings.getAutoMarkAsReadDays());
    }

    @FXML
    private void handleSaveSettings() {
        try {
            if (currentSettings == null) {
                currentSettings = new NotificationSettings(currentUser);
            }

            // Save general settings
            currentSettings.setDesktopNotificationsEnabled(chkDesktopNotifications.isSelected());
            currentSettings.setEmailNotificationsEnabled(chkEmailNotifications.isSelected());
            currentSettings.setSoundEnabled(chkSoundEnabled.isSelected());

            // Save notification type settings
            currentSettings.setRappelRetourEnabled(chkRappelRetour.isSelected());
            currentSettings.setLocationConfirmeeEnabled(chkLocationConfirmee.isSelected());
            currentSettings.setLocationReserveEnabled(chkLocationReserve.isSelected());
            currentSettings.setPaiementDuEnabled(chkPaiementDu.isSelected());
            currentSettings.setPaiementRecuEnabled(chkPaiementRecu.isSelected());
            currentSettings.setMaintenanceDueEnabled(chkMaintenanceDue.isSelected());
            currentSettings.setVehiculeDisponibleEnabled(chkVehiculeDisponible.isSelected());
            currentSettings.setWelcomeAdminEnabled(chkWelcomeAdmin.isSelected());
            currentSettings.setSystemAlertEnabled(chkSystemAlert.isSelected());
            currentSettings.setClientBirthdayEnabled(chkClientBirthday.isSelected());

            // Save timing settings
            currentSettings.setRappelRetourDaysBefore(spnRappelRetourDays.getValue());
            currentSettings.setMaintenanceDaysBefore(spnMaintenanceDays.getValue());
            currentSettings.setContractExpiringDaysBefore(spnContractExpiringDays.getValue());

            // Save quiet hours
            currentSettings.setRespectQuietHours(chkRespectQuietHours.isSelected());
            currentSettings.setQuietHoursStart(LocalTime.of(spnQuietHoursStart.getValue(), spnQuietMinutesStart.getValue()));
            currentSettings.setQuietHoursEnd(LocalTime.of(spnQuietHoursEnd.getValue(), spnQuietMinutesEnd.getValue()));

            // Save display settings
            currentSettings.setMaxNotificationsToShow(spnMaxNotifications.getValue());
            currentSettings.setAutoMarkAsReadAfterDays(chkAutoMarkAsRead.isSelected());
            currentSettings.setAutoMarkAsReadDays(spnAutoMarkDays.getValue());

            // Save to database
            settingsDAO.save(currentSettings);

            FXMLUtil.showSuccess("Paramètres sauvegardés avec succès!");

        } catch (Exception e) {
            FXMLUtil.showError("Erreur lors de la sauvegarde des paramètres: " + e.getMessage());
        }
    }

    @FXML
    private void handleResetToDefaults() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Réinitialiser les Paramètres");
        alert.setHeaderText("Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?");
        alert.setContentText("Cette action restaurera tous les paramètres par défaut.");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    settingsDAO.resetToDefaults(currentUser);
                    loadCurrentSettings();
                    FXMLUtil.showSuccess("Paramètres réinitialisés aux valeurs par défaut!");
                } catch (Exception e) {
                    FXMLUtil.showError("Erreur lors de la réinitialisation: " + e.getMessage());
                }
            }
        });
    }
}
