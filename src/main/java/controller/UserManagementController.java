package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.UserDAO;
import model.User;
import org.mindrot.jbcrypt.BCrypt;
import java.util.List;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.scene.control.Label;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ButtonBar;
import javafx.stage.FileChooser;
import javafx.application.Platform;
import java.util.Optional;
import java.io.File;
import util.ExportUtil;
import util.ExportOptions;

public class UserManagementController {
    @FXML private TableView<User> userTable;
    @FXML private TableColumn<User, Long> idColumn;
    @FXML private TableColumn<User, String> usernameColumn;
    @FXML private TableColumn<User, String> roleColumn;
    @FXML private TableColumn<User, String> emailColumn;
    @FXML private TableColumn<User, String> statusColumn;
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private ComboBox<String> roleCombo;
    @FXML private TextField emailField;
    @FXML private ComboBox<String> statusCombo;
    @FXML private Label lblTotalCount;
    @FXML private TextField searchField;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;

    private final UserDAO userDAO = new UserDAO();
    private ObservableList<User> userList;
    private User selectedUser = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        usernameColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getUsername()));
        roleColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getRole()));
        if (emailColumn != null) emailColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getEmail()));
        if (statusColumn != null) statusColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getStatus()));
        roleCombo.setItems(FXCollections.observableArrayList("admin", "agent"));
        statusCombo.setItems(FXCollections.observableArrayList("ACTIF", "INACTIF"));
        loadUsers();
        userTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedUser = newSelection;
                    isEditMode = true;
                    btnSave.setText("Modifier");

                    // Use null-safe operations
                    usernameField.setText(selectedUser.getUsername() != null ? selectedUser.getUsername() : "");
                    passwordField.clear();
                    roleCombo.setValue(selectedUser.getRole() != null ? selectedUser.getRole() : "agent");
                    emailField.setText(selectedUser.getEmail() != null ? selectedUser.getEmail() : "");
                    statusCombo.setValue(selectedUser.getStatus() != null ? selectedUser.getStatus() : "ACTIF");
                } else {
                    clearForm();
                }
            } catch (Exception e) {
                System.err.println("Error in user selection: " + e.getMessage());
                clearForm();
            }
        });
        clearForm();
    }

    private void loadUsers() {
        List<User> users = userDAO.findAll();
        userList = FXCollections.observableArrayList(users);
        userTable.setItems(userList);
        if (lblTotalCount != null) lblTotalCount.setText("Total: " + userList.size());
    }

    private void clearForm() {
        usernameField.clear();
        passwordField.clear();
        roleCombo.setValue(null);
        emailField.clear();
        statusCombo.setValue(null);
        selectedUser = null;
        isEditMode = false;
        btnSave.setText("Enregistrer");
    }

    @FXML
    private void handleSave() {
        String username = usernameField.getText();
        String password = passwordField.getText();
        String role = roleCombo.getValue();
        String email = emailField.getText();
        String status = statusCombo.getValue();
        if (username.isEmpty() || role == null || email.isEmpty() || status == null) return;
        try {
            if (isEditMode && selectedUser != null) {
                // Update user
                selectedUser.setUsername(username);
                if (!password.isEmpty()) selectedUser.setPasswordHash(BCrypt.hashpw(password, BCrypt.gensalt()));
                selectedUser.setRole(role);
                selectedUser.setEmail(email);
                selectedUser.setStatus(status);
                userDAO.save(selectedUser);
            } else {
                // Add new user
                if (password.isEmpty()) return;
                User user;
                if ("admin".equals(role)) {
                    user = new model.Admin();
                } else {
                    user = new model.Agent();
                }
                user.setUsername(username);
                user.setPasswordHash(BCrypt.hashpw(password, BCrypt.gensalt()));
                user.setRole(role);
                user.setEmail(email);
                user.setStatus(status);
                userDAO.save(user);
            }
            loadUsers();
            clearForm();
        } catch (Exception e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'enregistrement");
            alert.setContentText(e.getMessage());
            alert.showAndWait();
        }
    }

    @FXML
    private void handleDeleteUser() {
        User selected = userTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            userDAO.delete(selected);
            loadUsers();
            clearForm();
        }
    }

    @FXML
    private void handleRefresh() {
        loadUsers();
    }

    @FXML
    private void handleExport() {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Exporter les utilisateurs");
        dialog.setHeaderText("Choisissez le format d'export");
        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(exportButtonType, ButtonType.CANCEL);
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));
        ComboBox<String> formatCombo = new ComboBox<>();
        formatCombo.getItems().addAll("CSV", "PDF");
        formatCombo.setValue("CSV");
        grid.add(new Label("Format:"), 0, 0);
        grid.add(formatCombo, 1, 0);
        dialog.getDialogPane().setContent(grid);
        Platform.runLater(() -> formatCombo.requestFocus());
        dialog.setResultConverter(dialogButton -> dialogButton == exportButtonType ? formatCombo.getValue() : null);
        Optional<String> result = dialog.showAndWait();
        result.ifPresent(format -> {
            try {
                FileChooser fileChooser = new FileChooser();
                fileChooser.setTitle("Sauvegarder l'export des utilisateurs");
                String extension = "CSV".equals(format) ? "*.csv" : "*.pdf";
                String formatName = "CSV".equals(format) ? "CSV Files" : "PDF Files";
                fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter(formatName, extension));
                File file = fileChooser.showSaveDialog(userTable.getScene().getWindow());
                if (file != null) {
                    String[] headers = {"ID", "Nom d'utilisateur", "Rôle", "Email", "Statut"};
                    if ("CSV".equals(format)) {
                        ExportUtil.exportToCSV(userList, headers, user -> new String[] {
                            String.valueOf(user.getId()), user.getUsername(), user.getRole(), user.getEmail(), user.getStatus()
                        }, file.getAbsolutePath());
                    } else {
                        ExportUtil.exportToPDF(userList, headers, user -> new String[] {
                            String.valueOf(user.getId()), user.getUsername(), user.getRole(), user.getEmail(), user.getStatus()
                        }, file.getAbsolutePath());
                    }
                    Alert alert = new Alert(Alert.AlertType.INFORMATION);
                    alert.setTitle("Export réussi");
                    alert.setHeaderText(null);
                    alert.setContentText("Export réussi!\nFichier sauvegardé: " + file.getAbsolutePath());
                    alert.showAndWait();
                }
            } catch (Exception e) {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export: " + e.getMessage());
                alert.showAndWait();
            }
        });
    }



    @FXML
    private void handleRechercher() {
        handleSearch();
    }

    @FXML
    private void handleSearch() {
        String keyword = searchField != null ? searchField.getText().toLowerCase() : "";
        String role = roleCombo != null ? roleCombo.getValue() : null;
        ObservableList<User> filtered = FXCollections.observableArrayList(userList.stream()
            .filter(user -> {
                boolean matches = true;
                if (!keyword.isBlank()) {
                    matches &= user.getUsername().toLowerCase().contains(keyword) ||
                               (user.getEmail() != null && user.getEmail().toLowerCase().contains(keyword));
                }
                if (role != null && !role.isBlank()) {
                    matches &= role.equals(user.getRole());
                }
                return matches;
            }).toList());
        userTable.setItems(filtered);
    }

    @FXML
    private void handleClearFilters() {
        if (searchField != null) searchField.clear();
        if (roleCombo != null) roleCombo.getSelectionModel().clearSelection();
        userTable.setItems(userList);
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) userTable.getScene().getWindow());
    }
} 