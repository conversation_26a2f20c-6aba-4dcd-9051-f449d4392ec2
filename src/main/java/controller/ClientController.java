package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.ClientDAO;
import model.Client;
import model.User;

import java.util.List;
import java.util.stream.Collectors;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.scene.control.DatePicker;
import javafx.stage.FileChooser;
import javafx.application.Platform;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import java.time.LocalDate;
import java.util.Optional;
import util.ExportUtil;
import util.ExportOptions;
import java.io.File;
import javafx.scene.image.ImageView;

public class ClientController {
    @FXML private TextField txtSearch;
    @FXML private TableView<Client> clientTable;
    @FXML private TableColumn<Client, Long> idColumn;
    @FXML private TableColumn<Client, String> nomColumn;
    @FXML private TableColumn<Client, String> prenomColumn;
    @FXML private TableColumn<Client, String> cinColumn;
    @FXML private TableColumn<Client, String> telephoneColumn;
    @FXML private TableColumn<Client, String> emailColumn;
    @FXML private TableColumn<Client, String> permisColumn;
    @FXML private TableColumn<Client, String> adresseColumn;
    @FXML private Label lblTotalCount;
    
    // Form elements
    @FXML private TextField txtNomClientForm;
    @FXML private TextField txtPrenomClientForm;
    @FXML private TextField txtCinClientForm;
    @FXML private TextField txtTelephoneClientForm;
    @FXML private TextField txtEmailClientForm;
    @FXML private TextField txtPermisClientForm;
    @FXML private TextField txtAdresseClientForm;
    @FXML private ImageView imgPermisRecto;
    @FXML private ImageView imgPermisVerso;
    @FXML private ImageView imgCinRecto;
    @FXML private ImageView imgCinVerso;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;
    @FXML private Button btnUploadPermisRecto;
    @FXML private Button btnUploadPermisVerso;
    @FXML private Button btnUploadCinRecto;
    @FXML private Button btnUploadCinVerso;

    private String permisRectoPath = null;
    private String permisVersoPath = null;
    private String cinRectoPath = null;
    private String cinVersoPath = null;

    private final ClientDAO clientDAO = new ClientDAO();
    private ObservableList<Client> clientList = FXCollections.observableArrayList();
    private Client selectedClient = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        nomColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getNom()));
        prenomColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getPrenom()));
        cinColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getCin()));
        telephoneColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getTelephone()));
        emailColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getEmail()));
        permisColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getPermis()));
        adresseColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getAdresse()));
        
        // Add table selection listener with error handling
        clientTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedClient = newSelection;
                    loadClientToForm(newSelection);
                    isEditMode = true;
                    btnSave.setText("Modifier");
                } else {
                    clearForm();
                }
            } catch (Exception e) {
                System.err.println("Error in client selection: " + e.getMessage());
                clearForm();
            }
        });
        
        loadClients();
        clearForm();
    }

    private void loadClients() {
        List<Client> clients = clientDAO.findAll();
        clientList.setAll(clients);
        clientTable.setItems(clientList);
        lblTotalCount.setText("Total: " + clients.size());
    }

    private void loadClientToForm(Client client) {
        if (client == null) {
            clearForm();
            return;
        }

        // Use null-safe setText operations
        txtNomClientForm.setText(client.getNom() != null ? client.getNom() : "");
        txtPrenomClientForm.setText(client.getPrenom() != null ? client.getPrenom() : "");
        txtCinClientForm.setText(client.getCin() != null ? client.getCin() : "");
        txtTelephoneClientForm.setText(client.getTelephone() != null ? client.getTelephone() : "");
        txtEmailClientForm.setText(client.getEmail() != null ? client.getEmail() : "");
        txtPermisClientForm.setText(client.getPermis() != null ? client.getPermis() : "");
        txtAdresseClientForm.setText(client.getAdresse() != null ? client.getAdresse() : "");

        // Load images safely
        loadImageToView(imgPermisRecto, client.getPermisRecto());
        loadImageToView(imgPermisVerso, client.getPermisVerso());
        loadImageToView(imgCinRecto, client.getCinRecto());
        loadImageToView(imgCinVerso, client.getCinVerso());

        // Store image paths
        permisRectoPath = client.getPermisRecto();
        permisVersoPath = client.getPermisVerso();
        cinRectoPath = client.getCinRecto();
        cinVersoPath = client.getCinVerso();
    }

    private void clearForm() {
        txtNomClientForm.clear();
        txtPrenomClientForm.clear();
        txtCinClientForm.clear();
        txtTelephoneClientForm.clear();
        txtEmailClientForm.clear();
        txtPermisClientForm.clear();
        txtAdresseClientForm.clear();
        imgPermisRecto.setImage(null);
        imgPermisVerso.setImage(null);
        imgCinRecto.setImage(null);
        imgCinVerso.setImage(null);
        permisRectoPath = null;
        permisVersoPath = null;
        cinRectoPath = null;
        cinVersoPath = null;
        selectedClient = null;
        isEditMode = false;
        btnSave.setText("Enregistrer");
    }

    @FXML
    private void handleAjouter() {
        clearForm();
    }

    @FXML
    private void handleModifier() {
        // No longer needed, but keep for compatibility
        if (selectedClient != null) {
            isEditMode = true;
            btnSave.setText("Modifier");
        } else {
            showAlert("Veuillez sélectionner un client à modifier", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSupprimer() {
        // Check if user is admin - only admins can delete clients
        if (!isAdmin()) {
            showAlert("Seuls les administrateurs peuvent supprimer des clients.", AlertType.WARNING);
            return;
        }

        Client selected = clientTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Supprimer le client");
            alert.setContentText("Êtes-vous sûr de vouloir supprimer " + selected.getNom() + " " + selected.getPrenom() + " ?");

            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        clientDAO.delete(selected);
                        loadClients();
                        clearForm();
                        showAlert("Client supprimé avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de la suppression: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner un client à supprimer", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSave() {
        String nom = txtNomClientForm.getText().trim();
        String prenom = txtPrenomClientForm.getText().trim();
        String cin = txtCinClientForm.getText().trim();
        String telephone = txtTelephoneClientForm.getText().trim();
        String email = txtEmailClientForm.getText().trim();
        String permis = txtPermisClientForm.getText().trim();
        String adresse = txtAdresseClientForm.getText().trim();
        // image paths already set
        if (nom.isEmpty() || prenom.isEmpty() || cin.isEmpty()) {
            showAlert("Veuillez remplir tous les champs obligatoires", AlertType.WARNING);
            return;
        }
        try {
            if (isEditMode && selectedClient != null) {
                selectedClient.setNom(nom);
                selectedClient.setPrenom(prenom);
                selectedClient.setCin(cin);
                selectedClient.setTelephone(telephone);
                selectedClient.setEmail(email);
                selectedClient.setPermis(permis);
                selectedClient.setAdresse(adresse);
                selectedClient.setPermisRecto(permisRectoPath);
                selectedClient.setPermisVerso(permisVersoPath);
                selectedClient.setCinRecto(cinRectoPath);
                selectedClient.setCinVerso(cinVersoPath);
                clientDAO.save(selectedClient);
                showAlert("Client modifié avec succès", AlertType.INFORMATION);
            } else {
                Client newClient = new Client();
                newClient.setNom(nom);
                newClient.setPrenom(prenom);
                newClient.setCin(cin);
                newClient.setTelephone(telephone);
                newClient.setEmail(email);
                newClient.setPermis(permis);
                newClient.setAdresse(adresse);
                newClient.setPermisRecto(permisRectoPath);
                newClient.setPermisVerso(permisVersoPath);
                newClient.setCinRecto(cinRectoPath);
                newClient.setCinVerso(cinVersoPath);
                clientDAO.save(newClient);
                showAlert("Client ajouté avec succès", AlertType.INFORMATION);
            }
            loadClients();
            clearForm();
        } catch (Exception e) {
            showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), AlertType.ERROR);
        }
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    @FXML
    private void handleRefresh() {
        txtSearch.clear();
        loadClients();
        clearForm();
    }

    @FXML
    private void handleExport() {
        // Create export dialog
        Dialog<ExportOptions> dialog = new Dialog<>();
        dialog.setTitle("Exporter les clients");
        dialog.setHeaderText("Choisissez les options d'export");

        // Set the button types
        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(exportButtonType, ButtonType.CANCEL);

        // Create the custom content
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> formatCombo = new ComboBox<>();
        formatCombo.getItems().addAll("CSV", "PDF");
        formatCombo.setValue("CSV");

        ComboBox<String> periodCombo = new ComboBox<>();
        periodCombo.getItems().addAll("Tous les clients", "Période personnalisée");
        periodCombo.setValue("Tous les clients");

        DatePicker startDate = new DatePicker();
        DatePicker endDate = new DatePicker();
        startDate.setDisable(true);
        endDate.setDisable(true);

        periodCombo.valueProperty().addListener((obs, oldVal, newVal) -> {
            boolean custom = "Période personnalisée".equals(newVal);
            startDate.setDisable(!custom);
            endDate.setDisable(!custom);
        });

        grid.add(new Label("Format:"), 0, 0);
        grid.add(formatCombo, 1, 0);
        grid.add(new Label("Période:"), 0, 1);
        grid.add(periodCombo, 1, 1);
        grid.add(new Label("Date début:"), 0, 2);
        grid.add(startDate, 1, 2);
        grid.add(new Label("Date fin:"), 0, 3);
        grid.add(endDate, 1, 3);

        dialog.getDialogPane().setContent(grid);

        // Request focus on the format combo by default
        Platform.runLater(() -> formatCombo.requestFocus());

        // Convert the result when the export button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == exportButtonType) {
                return new ExportOptions(
                    formatCombo.getValue(),
                    periodCombo.getValue(),
                    startDate.getValue(),
                    endDate.getValue()
                );
            }
            return null;
        });

        Optional<ExportOptions> result = dialog.showAndWait();

        result.ifPresent(options -> {
            try {
                // File chooser
                FileChooser fileChooser = new FileChooser();
                fileChooser.setTitle("Sauvegarder l'export des clients");
                
                String extension = "CSV".equals(options.getFormat()) ? "*.csv" : "*.pdf";
                String formatName = "CSV".equals(options.getFormat()) ? "CSV Files" : "PDF Files";
                fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter(formatName, extension));
                
                File file = fileChooser.showSaveDialog(clientTable.getScene().getWindow());
                
                if (file != null) {
                    // Filter data based on period (for clients, we'll export all since there's no date field)
                    List<Client> dataToExport = clientList;

                    String[] headers = {"ID", "Nom", "Prénom", "CIN", "Téléphone", "Email"};
                    
                    if ("CSV".equals(options.getFormat())) {
                        ExportUtil.exportToCSV(dataToExport, headers, client -> new String[]{
                            String.valueOf(client.getId()),
                            client.getNom(),
                            client.getPrenom(),
                            client.getCin(),
                            client.getTelephone() != null ? client.getTelephone() : "",
                            client.getEmail() != null ? client.getEmail() : ""
                        }, file.getAbsolutePath());
                    } else {
                        ExportUtil.exportToPDF(dataToExport, headers, client -> new String[]{
                            String.valueOf(client.getId()),
                            client.getNom(),
                            client.getPrenom(),
                            client.getCin(),
                            client.getTelephone() != null ? client.getTelephone() : "",
                            client.getEmail() != null ? client.getEmail() : ""
                        }, file.getAbsolutePath());
                    }

                    showAlert("Export réussi!\nFichier sauvegardé: " + file.getAbsolutePath(), AlertType.INFORMATION);
                }
            } catch (Exception e) {
                showAlert("Erreur lors de l'export: " + e.getMessage(), AlertType.ERROR);
            }
        });
    }



    @FXML
    private void handleSearch() {
        String search = txtSearch.getText().trim().toLowerCase();
        if (search.isEmpty()) {
            loadClients();
            return;
        }
        List<Client> filtered = clientDAO.findAll().stream()
            .filter(c -> c.getNom().toLowerCase().contains(search)
                || c.getPrenom().toLowerCase().contains(search)
                || c.getCin().toLowerCase().contains(search))
            .collect(Collectors.toList());
        clientList.setAll(filtered);
        clientTable.setItems(clientList);
        lblTotalCount.setText("Total: " + filtered.size());
    }

    @FXML
    private void handleRechercher() {
        handleSearch();
    }

    @FXML
    private void handleClearFilters() {
        txtSearch.clear();
        loadClients();
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private boolean isAdmin() {
        // Check if logged in user is admin
        if (LoginController.loggedInUser != null) {
            try {
                User user = (User) LoginController.loggedInUser;
                return "admin".equals(user.getRole());
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) clientTable.getScene().getWindow());
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            javafx.scene.Parent root = javafx.fxml.FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            Stage stage = (Stage) clientTable.getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleViewClient() {
        Client selected = clientTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner un client à visualiser", AlertType.WARNING);
            return;
        }
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/client_detail.fxml"));
            javafx.scene.Parent detailRoot = loader.load();
            controller.ClientDetailController detailController = loader.getController();
            detailController.setClient(selected);
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Détails du Client");
            stage.setScene(new javafx.scene.Scene(detailRoot));
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            stage.setX(visualBounds.getMinX());
            stage.setY(visualBounds.getMinY());
            stage.setWidth(visualBounds.getWidth());
            stage.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            stage.show();
        } catch (Exception e) {
            showAlert("Erreur lors de l'ouverture des détails: " + e.getMessage(), AlertType.ERROR);
        }
    }

    private void loadImageToView(ImageView view, String path) {
        if (path != null && !path.isEmpty()) {
            try {
                view.setImage(new javafx.scene.image.Image(path));
            } catch (Exception e) { view.setImage(null); }
        } else {
            view.setImage(null);
        }
    }

    @FXML
    private void handleUploadPermisRecto() { permisRectoPath = handleImageUpload(imgPermisRecto); }
    @FXML
    private void handleUploadPermisVerso() { permisVersoPath = handleImageUpload(imgPermisVerso); }
    @FXML
    private void handleUploadCinRecto() { cinRectoPath = handleImageUpload(imgCinRecto); }
    @FXML
    private void handleUploadCinVerso() { cinVersoPath = handleImageUpload(imgCinVerso); }

    private String handleImageUpload(ImageView targetView) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choisir une image");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Images", "*.png", "*.jpg", "*.jpeg", "*.gif")
        );
        File file = fileChooser.showOpenDialog(null);
        if (file != null) {
            String url = file.toURI().toString();
            targetView.setImage(new javafx.scene.image.Image(url));
            return url;
        }
        return null;
    }
}
