
package controller;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.event.ActionEvent;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import javafx.fxml.FXMLLoader;
import util.FXMLUtil;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import model.Client;
import model.Vehicule;
import model.Location;
import model.Paiement;
import model.User;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.LocationDAO;
import dao.PaiementDAO;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.NumberAxis;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.GridPane;
import javafx.event.ActionEvent;
import javafx.stage.FileChooser;
import javafx.geometry.Insets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import model.Client;
import model.Vehicule;
import model.Location;
import model.Paiement;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.LocationDAO;
import dao.PaiementDAO;
import util.ExportUtil;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;


public class DashboardController {
    public void setUserRole(String userRole) {
        this.userRole = userRole;
        // Load dashboard content when user role is set
        if (contentPane != null) {
            loadView("/view/dashboard_content.fxml");
        }
        applyRoleRestrictions();
    }
    // Navigation
    @FXML private StackPane contentPane;
    @FXML private Button btnVehicules, btnUserManagement, btnClients, btnCatalogue, btnLocations, btnPaiements, btnDashboard, btnRentalHistory, btnPaymentHistory, btnNewLocation, btnReturnPreview, btnMaintenance;
    @FXML private Button btnLogout;
    @FXML private Label lblUserName, lblUserRole;

    // Dashboard stats and charts (keep only what is used)
    @FXML private Label lblCurrentDate, lblTotalClients, lblTotalVehicules, lblActiveLocations, lblTotalRevenue;
    @FXML private TableView<Location> reservationTable;
    @FXML private TableColumn<Location, Long> idColumn;
    @FXML private TableColumn<Location, String> clientColumn, vehiculeColumn, dateDebutColumn, dateFinColumn, statutColumn;
    @FXML private Button btnRefreshReservations;
    @FXML private TableView<Paiement> paymentTable;
    @FXML private TableColumn<Paiement, Long> payIdColumn;
    @FXML private TableColumn<Paiement, String> payClientColumn, payVehiculeColumn, payDateColumn;
    @FXML private TableColumn<Paiement, Double> payMontantColumn;
    @FXML private Button btnRefreshPayments;

    private final ClientDAO clientDAO = new ClientDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private final LocationDAO locationDAO = new LocationDAO();
    private final PaiementDAO paiementDAO = new PaiementDAO();
    private ObservableList<Location> reservationList;
    private ObservableList<Paiement> paymentList;
    private String userRole = ""; // Should be set on login ("admin" or "agent")

    @FXML
    public void initialize() {
        if (lblCurrentDate != null) {
            lblCurrentDate.setText("Date: " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        }
        // Set userRole from LoginController or session
        if (LoginController.loggedInUser != null) {
            // Cast to your user class, e.g., User, Admin, or Agent
            // Example: if you have a User class:
            // userRole = ((User)LoginController.loggedInUser).getRole();
            // If you use Admin/Agent, adjust accordingly
            try {
                userRole = ((User)LoginController.loggedInUser).getRole();
            } catch (Exception e) {
                userRole = "";
            }
        }

        // Load dashboard content by default
        loadView("/view/dashboard_content.fxml");

        loadDashboardStats();
        loadReservations();
        loadPayments();
        applyRoleRestrictions();
    }

    private void applyRoleRestrictions() {
        if (userRole == null || userRole.isEmpty()) {
            // Default to agent restrictions if role is not set
            userRole = "agent";
        }

        // Apply agent restrictions
        if ("agent".equals(userRole)) {
            // Hide admin-only navigation buttons
            if (btnUserManagement != null) {
                btnUserManagement.setVisible(false);
                btnUserManagement.setManaged(false);
            }

            // Update user display to show agent role
            if (lblUserRole != null) {
                lblUserRole.setText("<EMAIL>");
            }

            // You can add more agent restrictions here:
            // - Disable certain buttons
            // - Hide sensitive information
            // - Restrict access to certain features
        } else if ("admin".equals(userRole)) {
            // Admin has full access - ensure all buttons are visible
            if (btnUserManagement != null) {
                btnUserManagement.setVisible(true);
                btnUserManagement.setManaged(true);
            }

            if (lblUserRole != null) {
                lblUserRole.setText("<EMAIL>");
            }
        }
    }

    private void loadDashboardStats() {
        List<Client> clients = clientDAO.findAll();
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        List<Location> locations = locationDAO.findAll();
        List<Paiement> paiements = paiementDAO.findAll();
        if (lblTotalClients != null) lblTotalClients.setText(String.valueOf(clients.size()));
        if (lblTotalVehicules != null) lblTotalVehicules.setText(String.valueOf(vehicules.size()));
        if (lblActiveLocations != null) {
            long active = locations.stream().filter(l -> l.getDateFinReelle() == null).count();
            lblActiveLocations.setText(String.valueOf(active));
        }
        if (lblTotalRevenue != null) {
            double total = paiements.stream().mapToDouble(Paiement::getMontant).sum();
            lblTotalRevenue.setText(String.format("%.2f DH", total));
        }
    }

    private void loadReservations() {
        reservationList = FXCollections.observableArrayList(locationDAO.findAll());
        if (reservationTable != null) reservationTable.setItems(reservationList);
    }

    private void loadPayments() {
        paymentList = FXCollections.observableArrayList(paiementDAO.findAll());
        if (paymentTable != null) paymentTable.setItems(paymentList);
    }

    @FXML
    private void handleRefreshReservations() { loadReservations(); }
    @FXML
    private void handleRefreshPayments() { loadPayments(); }

    // Navigation methods (single implementation)
    @FXML private void showVehicules() { loadView("/view/vehicule.fxml"); }
    @FXML private void showClients() { loadView("/view/client.fxml"); }
    @FXML private void showCatalogue() { loadView("/view/catalogue.fxml"); }
    @FXML private void showLocations() { loadView("/view/location.fxml"); }
    @FXML private void showPaiements() { loadView("/view/paiement.fxml"); }
    @FXML private void showUserManagement() { if ("admin".equals(userRole)) loadView("/view/user_management.fxml"); }
    @FXML private void showDashboard() { loadView("/view/dashboard_content.fxml"); }
    @FXML private void showRentalHistory() { loadView("/view/historique_rentals.fxml"); }
    @FXML private void showPaymentHistory() { loadView("/view/historique_paiements.fxml"); }
    @FXML private void showNewLocation() { loadView("/view/location_create.fxml"); }
    @FXML private void showReturnPreview() {
        FXMLUtil.createMaximizedWindow("/view/vehicle_return_preview.fxml",
                                      "Prévisions de Retour des Véhicules",
                                      getClass());
    }

    // Remove handleRefreshData and any references to loadDashboardStats or loadDashboardContent

    // Remove unused export logic for now

    @FXML
    private void navigateToPage(ActionEvent event) {
        if (event.getSource() instanceof Button) {
            Button btn = (Button) event.getSource();
            String id = btn.getId();
            if (id == null) return;
            switch (id) {
                case "btnDashboard": showDashboard(); break;
                case "btnClients": showClients(); break;
                case "btnVehicules": showVehicules(); break;
                case "btnReturnPreview": showReturnPreview(); break;
                case "btnMaintenance": showMaintenance(); break;
                case "btnCatalogue": showCatalogue(); break;
                case "btnLocations": showLocations(); break;
                case "btnPaiements": showPaiements(); break;
                case "btnUserManagement": if ("admin".equals(userRole)) showUserManagement(); break;
                case "btnRentalHistory": showRentalHistory(); break;
                case "btnPaymentHistory": showPaymentHistory(); break;
                case "btnNewLocation": showNewLocation(); break;
            }
        }
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            // Clear logged in user
            LoginController.loggedInUser = null;
            
            Stage stage = (Stage) contentPane.getScene().getWindow();
            Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            stage.setScene(new Scene(root));
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            stage.setX(visualBounds.getMinX());
            stage.setY(visualBounds.getMinY());
            stage.setWidth(visualBounds.getWidth());
            stage.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            stage.setTitle("LocationV1 - Connexion");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void loadView(String fxmlPath) {
        if (contentPane == null) {
            System.err.println("Error: contentPane is null. FXML injection may have failed.");
            return;
        }
        try {
            Node node = FXMLLoader.load(getClass().getResource(fxmlPath));
            contentPane.getChildren().setAll(node);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML private void showMaintenance() {
        FXMLUtil.createMaximizedWindow("/view/vehicle_maintenance.fxml",
                                      "Gestion de la Maintenance des Véhicules",
                                      getClass());
    }

    private void showAlert(String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}