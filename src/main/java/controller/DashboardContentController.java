package controller;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.TableView;
import javafx.scene.control.TableColumn;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.XYChart;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import model.Client;
import model.Vehicule;
import model.Location;
import model.Paiement;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.LocationDAO;
import dao.PaiementDAO;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javafx.scene.control.Button;
import javafx.scene.control.Dialog;
import javafx.scene.control.ToggleGroup;
import javafx.scene.control.RadioButton;
import javafx.scene.control.ButtonBar;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.TextField;
import javafx.scene.control.ButtonType;

public class DashboardContentController {
    @FXML private Label lblCurrentDate;
    @FXML private Label lblTotalClients;
    @FXML private Label lblTotalVehicules;
    @FXML private Label lblActiveLocations;
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblReturnsToday;
    @FXML private Button btnAllLocations;
    @FXML private Button btnAllPayments;
    @FXML private Button btnAllActivity;
    @FXML private Button btnExportLocations;
    @FXML private TableView<Object> recentActivityTable;
    @FXML private TableColumn<Object, String> activityDateColumn;
    @FXML private TableColumn<Object, String> activityTypeColumn;
    @FXML private TableColumn<Object, String> activityDescriptionColumn;
    @FXML private TableColumn<Object, String> activityUserColumn;
    @FXML private PieChart vehiculeStatusPie;
    @FXML private BarChart<String, Number> locationsBarChart;
    @FXML private CategoryAxis locationsMonthAxis;
    @FXML private NumberAxis locationsCountAxis;
    @FXML private BarChart<String, Number> revenueBarChart;
    @FXML private CategoryAxis revenueMonthAxis;
    @FXML private NumberAxis revenueAmountAxis;
    @FXML private TableView<Location> reservationTable;
    @FXML private TableColumn<Location, Long> idColumn;
    @FXML private TableColumn<Location, String> clientColumn;
    @FXML private TableColumn<Location, String> vehiculeColumn;
    @FXML private TableColumn<Location, String> dateDebutColumn;
    @FXML private TableColumn<Location, String> dateFinColumn;
    @FXML private TableColumn<Location, String> statutColumn;
    @FXML private Button btnRefreshReservations;
    @FXML private TableView<Paiement> paymentTable;
    @FXML private TableColumn<Paiement, Long> payIdColumn;
    @FXML private TableColumn<Paiement, String> payClientColumn;
    @FXML private TableColumn<Paiement, String> payVehiculeColumn;
    @FXML private TableColumn<Paiement, Double> payMontantColumn;
    @FXML private TableColumn<Paiement, String> payDateColumn;
    @FXML private Button btnRefreshPayments;
    private ObservableList<Location> reservationList;
    private ObservableList<Paiement> paymentList;

    @FXML
    public void initialize() {
        if (lblCurrentDate != null) {
            lblCurrentDate.setText("Date: " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        }
        // Custom cell factories for recent activity table
        if (activityDateColumn != null) {
            activityDateColumn.setCellValueFactory(cellData -> {
                Object obj = cellData.getValue();
                if (obj instanceof Location) {
                    Location l = (Location) obj;
                    return new javafx.beans.property.SimpleStringProperty(l.getDateDebut() != null ? l.getDateDebut().toString() : "");
                } else if (obj instanceof Paiement) {
                    Paiement p = (Paiement) obj;
                    return new javafx.beans.property.SimpleStringProperty(p.getDatePaiement() != null ? p.getDatePaiement().toString() : "");
                }
                return new javafx.beans.property.SimpleStringProperty("");
            });
        }
        if (activityTypeColumn != null) {
            activityTypeColumn.setCellValueFactory(cellData -> {
                Object obj = cellData.getValue();
                if (obj instanceof Location) {
                    return new javafx.beans.property.SimpleStringProperty("Location");
                } else if (obj instanceof Paiement) {
                    return new javafx.beans.property.SimpleStringProperty("Paiement");
                }
                return new javafx.beans.property.SimpleStringProperty("");
            });
        }
        if (activityDescriptionColumn != null) {
            activityDescriptionColumn.setCellValueFactory(cellData -> {
                Object obj = cellData.getValue();
                if (obj instanceof Location) {
                    Location l = (Location) obj;
                    String desc = l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "";
                    desc += l.getPrixTotal() > 0 ? String.format(" (%.2f DH)", l.getPrixTotal()) : "";
                    return new javafx.beans.property.SimpleStringProperty(desc);
                } else if (obj instanceof Paiement) {
                    Paiement p = (Paiement) obj;
                    String desc = p.getMontant() + " DH";
                    if (p.getLocation() != null && p.getLocation().getVehicule() != null) {
                        desc += " pour " + p.getLocation().getVehicule().getMarque() + " " + p.getLocation().getVehicule().getModele();
                    }
                    return new javafx.beans.property.SimpleStringProperty(desc);
                }
                return new javafx.beans.property.SimpleStringProperty("");
            });
        }
        if (activityUserColumn != null) {
            activityUserColumn.setCellValueFactory(cellData -> {
                Object obj = cellData.getValue();
                if (obj instanceof Location) {
                    Location l = (Location) obj;
                    return new javafx.beans.property.SimpleStringProperty(l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "");
                } else if (obj instanceof Paiement) {
                    Paiement p = (Paiement) obj;
                    if (p.getLocation() != null && p.getLocation().getClient() != null) {
                        return new javafx.beans.property.SimpleStringProperty(p.getLocation().getClient().getNom() + " " + p.getLocation().getClient().getPrenom());
                    }
                }
                return new javafx.beans.property.SimpleStringProperty("");
            });
        }
        // Reservation table columns
        if (idColumn != null) idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        if (clientColumn != null) clientColumn.setCellValueFactory(data -> {
            var c = data.getValue().getClient();
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        if (vehiculeColumn != null) vehiculeColumn.setCellValueFactory(data -> {
            var v = data.getValue().getVehicule();
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        if (dateDebutColumn != null) dateDebutColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateDebut();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        if (dateFinColumn != null) dateFinColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateFinPrevue();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        if (statutColumn != null) statutColumn.setCellValueFactory(data -> {
            var l = data.getValue();
            String statut = l.getDateFinReelle() == null ? "En cours" : "Terminée";
            return new javafx.beans.property.SimpleStringProperty(statut);
        });
        // Payment table columns
        if (payIdColumn != null) payIdColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        if (payClientColumn != null) payClientColumn.setCellValueFactory(data -> {
            var loc = data.getValue().getLocation();
            var c = loc != null ? loc.getClient() : null;
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        if (payVehiculeColumn != null) payVehiculeColumn.setCellValueFactory(data -> {
            var loc = data.getValue().getLocation();
            var v = loc != null ? loc.getVehicule() : null;
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        if (payMontantColumn != null) payMontantColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleDoubleProperty(data.getValue().getMontant()).asObject());
        if (payDateColumn != null) payDateColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDatePaiement();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        btnAllLocations.setOnAction(e -> navigateToFullLocations());
        btnAllPayments.setOnAction(e -> navigateToFullPayments());
        btnAllActivity.setOnAction(e -> navigateToFullActivity());
        btnExportLocations.setOnAction(e -> showExportLocationsDialog());
        loadDashboardStats();
        loadReservations();
        loadPayments();
    }

    @FXML
    private void handleRefreshReservations() {
        loadReservations();
        loadDashboardStats(); // Refresh dashboard stats after reservation changes
    }

    @FXML
    private void handleRefreshPayments() {
        loadPayments();
        loadDashboardStats(); // Refresh dashboard stats after payment changes
    }

    public void loadDashboardStats() {
        try {
            ClientDAO clientDAO = new ClientDAO();
            VehiculeDAO vehiculeDAO = new VehiculeDAO();
            LocationDAO locationDAO = new LocationDAO();
            PaiementDAO paiementDAO = new PaiementDAO();
            List<Client> clients = clientDAO.findAll();
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            List<Location> locations = locationDAO.findAll();
            List<Paiement> paiements = paiementDAO.findAll();
            // Stat cards
            if (lblTotalClients != null) lblTotalClients.setText(String.valueOf(clients.size()));
            if (lblTotalVehicules != null) lblTotalVehicules.setText(String.valueOf(vehicules.size()));
            if (lblActiveLocations != null) {
                long active = locations.stream().filter(l -> l.getDateFinReelle() == null).count();
                lblActiveLocations.setText(String.valueOf(active));
            }
            if (lblTotalRevenue != null) {
                double total = paiements.stream().mapToDouble(Paiement::getMontant).sum();
                lblTotalRevenue.setText(String.format("%.2f DH", total));
            }
            if (lblReturnsToday != null) {
                java.time.LocalDate today = java.time.LocalDate.now();
                long returnsToday = locations.stream().filter(l -> l.getDateFinPrevue() != null && l.getDateFinPrevue().equals(today)).count();
                lblReturnsToday.setText(String.valueOf(returnsToday));
            }
            updateVehiculeStatusPie(vehicules);
            updateLocationsBarChart(locations);
            updateRevenueBarChart(paiements);
            updateRecentActivityTable(locations, paiements);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateVehiculeStatusPie(List<Vehicule> vehicules) {
        if (vehiculeStatusPie == null) return;
        ObservableList<PieChart.Data> pieData = FXCollections.observableArrayList();
        vehicules.stream().map(Vehicule::getEtat).distinct().forEach(etat -> {
            long count = vehicules.stream().filter(v -> etat.equals(v.getEtat())).count();
            pieData.add(new PieChart.Data(etat + " (" + count + ")", count));
        });
        vehiculeStatusPie.setData(pieData);
        vehiculeStatusPie.setLegendVisible(true);
        vehiculeStatusPie.setLabelsVisible(true);

        // Apply custom colors to pie chart
        Platform.runLater(() -> {
            String[] colors = {"#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"};
            for (int i = 0; i < pieData.size() && i < colors.length; i++) {
                PieChart.Data data = pieData.get(i);
                data.getNode().setStyle("-fx-pie-color: " + colors[i] + ";");
            }
        });
    }

    private void updateLocationsBarChart(List<Location> locations) {
        if (locationsBarChart == null || locationsMonthAxis == null) return;
        locationsBarChart.getData().clear();
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Nombre de locations");
        java.time.YearMonth now = java.time.YearMonth.now();
        for (int i = 11; i >= 0; i--) {
            java.time.YearMonth ym = now.minusMonths(i);
            String label = ym.getMonth().toString().substring(0, 3) + " " + ym.getYear();
            long count = locations.stream().filter(l -> l.getDateDebut() != null && java.time.YearMonth.from(l.getDateDebut()).equals(ym)).count();
            series.getData().add(new XYChart.Data<>(label, count));
        }
        locationsBarChart.getData().add(series);
        locationsBarChart.setLegendVisible(false);
        locationsBarChart.setTitle("");

        // Apply custom styling to bar chart
        Platform.runLater(() -> {
            for (XYChart.Data<String, Number> data : series.getData()) {
                data.getNode().setStyle("-fx-bar-fill: linear-gradient(to bottom, #3b82f6, #1d4ed8);");
            }
        });
    }

    private void updateRevenueBarChart(List<Paiement> paiements) {
        if (revenueBarChart == null || revenueMonthAxis == null) return;
        revenueBarChart.getData().clear();
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Revenus (DH)");
        java.time.YearMonth now = java.time.YearMonth.now();
        for (int i = 11; i >= 0; i--) {
            java.time.YearMonth ym = now.minusMonths(i);
            String label = ym.getMonth().toString().substring(0, 3) + " " + ym.getYear();
            double sum = paiements.stream().filter(p -> p.getDatePaiement() != null && java.time.YearMonth.from(p.getDatePaiement()).equals(ym)).mapToDouble(Paiement::getMontant).sum();
            series.getData().add(new XYChart.Data<>(label, Math.round(sum * 100.0) / 100.0));
        }
        revenueBarChart.getData().add(series);
        revenueBarChart.setLegendVisible(false);
        revenueBarChart.setTitle("");

        // Apply custom styling to revenue bar chart
        Platform.runLater(() -> {
            for (XYChart.Data<String, Number> data : series.getData()) {
                data.getNode().setStyle("-fx-bar-fill: linear-gradient(to bottom, #8b5cf6, #7c3aed);");
            }
        });
    }

    private void updateRecentActivityTable(List<Location> locations, List<Paiement> paiements) {
        if (recentActivityTable == null) return;
        ObservableList<Object> activity = FXCollections.observableArrayList();
        locations.stream().sorted((a, b) -> {
            if (a.getDateDebut() == null) return 1;
            if (b.getDateDebut() == null) return -1;
            return b.getDateDebut().compareTo(a.getDateDebut());
        }).limit(10).forEach(activity::add);
        paiements.stream().sorted((a, b) -> {
            if (a.getDatePaiement() == null) return 1;
            if (b.getDatePaiement() == null) return -1;
            return b.getDatePaiement().compareTo(a.getDatePaiement());
        }).limit(10).forEach(activity::add);
        activity.sort((a, b) -> {
            LocalDate da = null, db = null;
            if (a instanceof Location) da = ((Location) a).getDateDebut();
            if (a instanceof Paiement) da = ((Paiement) a).getDatePaiement();
            if (b instanceof Location) db = ((Location) b).getDateDebut();
            if (b instanceof Paiement) db = ((Paiement) b).getDatePaiement();
            if (da == null) return 1;
            if (db == null) return -1;
            return db.compareTo(da);
        });
        recentActivityTable.setItems(activity);
    }

    private void loadReservations() {
        reservationList = FXCollections.observableArrayList(new dao.LocationDAO().findAll());
        if (reservationTable != null) reservationTable.setItems(reservationList);
    }

    private void loadPayments() {
        paymentList = FXCollections.observableArrayList(new dao.PaiementDAO().findAll());
        if (paymentTable != null) paymentTable.setItems(paymentList);
    }

    private void navigateToFullLocations() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location.fxml"));
            javafx.scene.Parent root = null;
            try {
                root = loader.load();
            } catch (java.io.IOException ex) {
                ex.printStackTrace();
                return;
            }
            javafx.scene.Scene scene = btnAllLocations.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                contentPane.getChildren().setAll(root);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    private void navigateToFullPayments() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/paiement.fxml"));
            javafx.scene.Parent root = null;
            try {
                root = loader.load();
            } catch (java.io.IOException ex) {
                ex.printStackTrace();
                return;
            }
            javafx.scene.Scene scene = btnAllPayments.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                contentPane.getChildren().setAll(root);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    private void navigateToFullActivity() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/historique.fxml"));
            javafx.scene.Parent root = null;
            try {
                root = loader.load();
            } catch (java.io.IOException ex) {
                ex.printStackTrace();
                return;
            }
            javafx.scene.Scene scene = btnAllActivity.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                contentPane.getChildren().setAll(root);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void showExportLocationsDialog() {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Exporter les Locations");
        dialog.getDialogPane().setPrefWidth(420);
        VBox content = new VBox(18);
        content.setStyle("-fx-padding: 24;");
        // Format selection
        Label formatLabel = new Label("Format du fichier :");
        ToggleGroup formatGroup = new ToggleGroup();
        RadioButton csvRadio = new RadioButton("CSV");
        csvRadio.setToggleGroup(formatGroup);
        csvRadio.setSelected(true);
        RadioButton pdfRadio = new RadioButton("PDF");
        pdfRadio.setToggleGroup(formatGroup);
        HBox formatBox = new HBox(16, csvRadio, pdfRadio);
        // Period selection
        Label periodLabel = new Label("Période à exporter :");
        ToggleGroup periodGroup = new ToggleGroup();
        RadioButton allRadio = new RadioButton("Tout");
        allRadio.setToggleGroup(periodGroup);
        allRadio.setSelected(true);
        RadioButton customRadio = new RadioButton("Personnalisée");
        customRadio.setToggleGroup(periodGroup);
        HBox periodBox = new HBox(16, allRadio, customRadio);
        DatePicker startDate = new DatePicker();
        DatePicker endDate = new DatePicker();
        HBox dateBox = new HBox(8, new Label("Du :"), startDate, new Label("au :"), endDate);
        dateBox.setVisible(false);
        customRadio.selectedProperty().addListener((obs, oldV, newV) -> dateBox.setVisible(newV));
        // File chooser
        Label fileLabel = new Label("Fichier de destination :");
        TextField fileField = new TextField();
        fileField.setEditable(false);
        Button fileBtn = new Button("Choisir...");
        HBox fileBox = new HBox(8, fileField, fileBtn);
        // Export/Cancel buttons
        ButtonType exportType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelType = new ButtonType("Annuler", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(exportType, cancelType);
        content.getChildren().addAll(formatLabel, formatBox, periodLabel, periodBox, dateBox, fileLabel, fileBox);
        dialog.getDialogPane().setContent(content);
        // File chooser logic
        fileBtn.setOnAction(ev -> {
            javafx.stage.FileChooser chooser = new javafx.stage.FileChooser();
            chooser.setTitle("Enregistrer sous...");
            if (csvRadio.isSelected())
                chooser.getExtensionFilters().add(new javafx.stage.FileChooser.ExtensionFilter("CSV", "*.csv"));
            else
                chooser.getExtensionFilters().add(new javafx.stage.FileChooser.ExtensionFilter("PDF", "*.pdf"));
            java.io.File file = null;
            try {
                file = chooser.showSaveDialog(btnExportLocations.getScene().getWindow());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (file != null) fileField.setText(file.getAbsolutePath());
        });
        // Export logic
        dialog.setResultConverter(btn -> {
            if (btn == exportType) {
                String format = csvRadio.isSelected() ? "csv" : "pdf";
                String filePath = fileField.getText();
                if (filePath == null || filePath.isBlank()) return null;
                java.util.List<model.Location> all = new dao.LocationDAO().findAll();
                java.util.List<model.Location> filtered = all;
                if (customRadio.isSelected() && startDate.getValue() != null && endDate.getValue() != null) {
                    filtered = all.stream().filter(l -> l.getDateDebut() != null &&
                        !l.getDateDebut().isBefore(startDate.getValue()) && !l.getDateDebut().isAfter(endDate.getValue())).toList();
                }
                if (format.equals("csv")) {
                    String[] headers = {"ID", "Client", "Véhicule", "DateDébut", "DateFin", "Statut", "PrixTotal"};
                    try {
                        util.ExportUtil.exportToCSV(filtered, headers, l -> new String[] {
                            String.valueOf(l.getId()),
                            l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "",
                            l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "",
                            l.getDateDebut() != null ? l.getDateDebut().toString() : "",
                            l.getDateFinPrevue() != null ? l.getDateFinPrevue().toString() : "",
                            l.getDateFinReelle() == null ? "En cours" : "Terminée",
                            String.format("%.2f", l.getPrixTotal())
                        }, filePath);
                    } catch (java.io.IOException ex) {
                        ex.printStackTrace();
                    }
                } else {
                    // PDF export using ExportUtil
                    String[] headers = {"ID", "Client", "Véhicule", "DateDébut", "DateFin", "Statut", "PrixTotal"};
                    try {
                        util.ExportUtil.exportToPDF(filtered, headers, l -> new String[] {
                            String.valueOf(l.getId()),
                            l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "",
                            l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "",
                            l.getDateDebut() != null ? l.getDateDebut().toString() : "",
                            l.getDateFinPrevue() != null ? l.getDateFinPrevue().toString() : "",
                            l.getDateFinReelle() == null ? "En cours" : "Terminée",
                            String.format("%.2f", l.getPrixTotal())
                        }, filePath);
                    } catch (java.io.IOException ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return null;
        });
        dialog.showAndWait();
    }
} 