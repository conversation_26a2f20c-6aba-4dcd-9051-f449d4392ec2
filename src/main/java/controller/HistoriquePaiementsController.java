package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.beans.property.SimpleStringProperty;
import javafx.stage.Stage;
import javafx.stage.FileChooser;
import javafx.application.Platform;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.fxml.FXMLLoader;

import dao.PaiementDAO;
import dao.LocationDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import model.Paiement;
import model.Location;
import model.Client;
import model.Vehicule;

import java.util.List;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.IOException;

public class HistoriquePaiementsController {

    // Filter Controls
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterMethodCombo;
    @FXML private ComboBox<String> filterClientCombo;
    @FXML private DatePicker filterDateFrom;
    @FXML private DatePicker filterDateTo;
    @FXML private TextField filterAmountMin;
    @FXML private TextField filterAmountMax;
    @FXML private ComboBox<String> filterVehicleCombo;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterPartialOnly;

    // Statistics Labels
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblRevenueChange;
    @FXML private Label lblPaidCount;
    @FXML private Label lblPaidPercentage;
    @FXML private Label lblPendingCount;
    @FXML private Label lblPendingAmount;
    @FXML private Label lblOverdueCount;
    @FXML private Label lblOverdueAmount;

    // Charts
    @FXML private VBox paymentMethodsChart;
    @FXML private VBox monthlyTrendsChart;

    // Table and Search
    @FXML private TextField searchField;
    @FXML private TableView<Paiement> paymentsTable;
    @FXML private TableColumn<Paiement, String> colSelect;
    @FXML private TableColumn<Paiement, String> colId;
    @FXML private TableColumn<Paiement, String> colClient;
    @FXML private TableColumn<Paiement, String> colVehicle;
    @FXML private TableColumn<Paiement, String> colAmount;
    @FXML private TableColumn<Paiement, String> colMethod;
    @FXML private TableColumn<Paiement, String> colDate;
    @FXML private TableColumn<Paiement, String> colStatus;
    @FXML private TableColumn<Paiement, String> colDueDate;
    @FXML private TableColumn<Paiement, String> colReference;
    @FXML private TableColumn<Paiement, String> colActions;

    // Sidebar Details
    @FXML private VBox paymentDetailCard;
    @FXML private Label lblSelectedPayment;
    @FXML private VBox paymentDetails;
    @FXML private VBox paymentTimeline;

    // Bottom Statistics
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredRevenue;
    @FXML private Label lblLastUpdate;

    // DAOs
    private PaiementDAO paiementDAO;
    private LocationDAO locationDAO;
    private ClientDAO clientDAO;
    private VehiculeDAO vehiculeDAO;

    // Data
    private ObservableList<Paiement> allPayments;
    private ObservableList<Paiement> filteredPayments;
    private Paiement selectedPayment;

    // Legacy fields for backward compatibility
    @FXML private TextField searchClientField;
    @FXML private TextField searchVehiculeField;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;
    @FXML private TableView<Paiement> paiementHistoryTable;
    @FXML private Label detailsLabel;
    private ObservableList<Paiement> allPaiements;

    @FXML
    private void initialize() {
        try {
            // Initialize DAOs
            paiementDAO = new PaiementDAO();
            locationDAO = new LocationDAO();
            clientDAO = new ClientDAO();
            vehiculeDAO = new VehiculeDAO();

            // Initialize data
            allPayments = FXCollections.observableArrayList();
            filteredPayments = FXCollections.observableArrayList();
            allPaiements = FXCollections.observableArrayList(); // Legacy compatibility

            // Setup table columns
            setupTableColumns();

            // Setup filters
            setupFilters();

            // Load data
            loadData();

            // Setup search
            setupSearch();

            // Update statistics
            updateStatistics();

            // Update last update time
            updateLastUpdateTime();

        } catch (Exception e) {
            System.err.println("Error initializing HistoriquePaiementsController: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur d'initialisation", "Erreur lors de l'initialisation: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void setupTableColumns() {
        // Select column with checkboxes
        if (colSelect != null) {
            colSelect.setCellValueFactory(cellData -> new SimpleStringProperty(""));
            colSelect.setCellFactory(column -> new TableCell<Paiement, String>() {
                private final CheckBox checkBox = new CheckBox();

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        setGraphic(checkBox);
                        checkBox.setOnAction(e -> updateSelectedCount());
                    }
                }
            });
        }

        // ID column
        if (colId != null) {
            colId.setCellValueFactory(cellData ->
                new SimpleStringProperty(String.valueOf(cellData.getValue().getId()))
            );
        }

        // Client column
        if (colClient != null) {
            colClient.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getLocation() != null && cellData.getValue().getLocation().getClient() != null ?
                    cellData.getValue().getLocation().getClient().getNom() + " " + cellData.getValue().getLocation().getClient().getPrenom() :
                    "N/A"
                )
            );
        }

        // Vehicle column
        if (colVehicle != null) {
            colVehicle.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getLocation() != null && cellData.getValue().getLocation().getVehicule() != null ?
                    cellData.getValue().getLocation().getVehicule().getMarque() + " " + cellData.getValue().getLocation().getVehicule().getModele() :
                    "N/A"
                )
            );
        }

        // Amount column
        if (colAmount != null) {
            colAmount.setCellValueFactory(cellData ->
                new SimpleStringProperty(String.format("%.2f DH", cellData.getValue().getMontant()))
            );
        }

        // Method column
        if (colMethod != null) {
            colMethod.setCellValueFactory(cellData ->
                new SimpleStringProperty(cellData.getValue().getMethodePaiement() != null ? cellData.getValue().getMethodePaiement() : "N/A")
            );
        }

        // Date column
        if (colDate != null) {
            colDate.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getDatePaiement() != null ?
                    cellData.getValue().getDatePaiement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) :
                    "N/A"
                )
            );
        }

        // Status column
        if (colStatus != null) {
            colStatus.setCellValueFactory(cellData ->
                new SimpleStringProperty(cellData.getValue().getStatut() != null ? cellData.getValue().getStatut() : "N/A")
            );
        }

        // Due date column
        if (colDueDate != null) {
            colDueDate.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getDateEcheance() != null ?
                    cellData.getValue().getDateEcheance().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) :
                    "N/A"
                )
            );
        }

        // Reference column
        if (colReference != null) {
            colReference.setCellValueFactory(cellData ->
                new SimpleStringProperty(cellData.getValue().getReference() != null ? cellData.getValue().getReference() : "N/A")
            );
        }

        // Actions column
        if (colActions != null) {
            colActions.setCellValueFactory(cellData -> new SimpleStringProperty(""));
            colActions.setCellFactory(column -> new TableCell<Paiement, String>() {
                private final HBox actionBox = new HBox(5);
                private final Button viewBtn = new Button("👁️");
                private final Button editBtn = new Button("✏️");
                private final Button invoiceBtn = new Button("🧾");

                {
                    viewBtn.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");
                    editBtn.setStyle("-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");
                    invoiceBtn.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");

                    actionBox.getChildren().addAll(viewBtn, editBtn, invoiceBtn);
                    actionBox.setAlignment(Pos.CENTER);
                }

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        Paiement payment = getTableView().getItems().get(getIndex());
                        viewBtn.setOnAction(e -> showPaymentDetails(payment));
                        editBtn.setOnAction(e -> handleEditPayment(payment));
                        invoiceBtn.setOnAction(e -> handleGenerateInvoice(payment));
                        setGraphic(actionBox);
                    }
                }
            });
        }

        // Set table selection listener
        if (paymentsTable != null) {
            paymentsTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    showPaymentDetails(newSelection);
                }
            });
        }
    }

    private void setupFilters() {
        // Status filter
        if (filterStatusCombo != null) {
            filterStatusCombo.setItems(FXCollections.observableArrayList(
                "Tous", "Payé", "En attente", "En retard", "Annulé"
            ));
            filterStatusCombo.setValue("Tous");
            filterStatusCombo.setOnAction(e -> applyFilters());
        }

        // Method filter
        if (filterMethodCombo != null) {
            filterMethodCombo.setItems(FXCollections.observableArrayList(
                "Toutes", "Espèces", "Carte", "Virement", "Chèque"
            ));
            filterMethodCombo.setValue("Toutes");
            filterMethodCombo.setOnAction(e -> applyFilters());
        }

        // Populate client and vehicle filters
        populateClientFilter();
        populateVehicleFilter();

        // Add listeners for other filters
        if (filterDateFrom != null) filterDateFrom.setOnAction(e -> applyFilters());
        if (filterDateTo != null) filterDateTo.setOnAction(e -> applyFilters());
        if (filterOverdueOnly != null) filterOverdueOnly.setOnAction(e -> applyFilters());
        if (filterPartialOnly != null) filterPartialOnly.setOnAction(e -> applyFilters());

        // Add listeners for amount filters
        if (filterAmountMin != null) filterAmountMin.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        if (filterAmountMax != null) filterAmountMax.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void populateClientFilter() {
        try {
            if (filterClientCombo != null && clientDAO != null) {
                List<Client> clients = clientDAO.findAll();
                List<String> clientNames = clients.stream()
                    .map(c -> c.getNom() + " " + c.getPrenom())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                clientNames.add(0, "Tous");
                filterClientCombo.setItems(FXCollections.observableArrayList(clientNames));
                filterClientCombo.setValue("Tous");
            }
        } catch (Exception e) {
            System.err.println("Error populating client filter: " + e.getMessage());
        }
    }

    private void populateVehicleFilter() {
        try {
            if (filterVehicleCombo != null && vehiculeDAO != null) {
                List<Vehicule> vehicles = vehiculeDAO.findAll();
                List<String> vehicleNames = vehicles.stream()
                    .map(v -> v.getMarque() + " " + v.getModele())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                vehicleNames.add(0, "Tous");
                filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicleNames));
                filterVehicleCombo.setValue("Tous");
            }
        } catch (Exception e) {
            System.err.println("Error populating vehicle filter: " + e.getMessage());
        }
    }

    private void setupSearch() {
        if (searchField != null) {
            searchField.textProperty().addListener((observable, oldValue, newValue) -> {
                applyFilters();
            });
        }
    }

    private void loadData() {
        try {
            List<Paiement> payments = paiementDAO.findAll();
            allPayments.setAll(payments);
            filteredPayments.setAll(payments);
            allPaiements.setAll(payments); // Legacy compatibility

            if (paymentsTable != null) {
                paymentsTable.setItems(filteredPayments);
            }

            // Legacy table setup
            if (paiementHistoryTable != null) {
                paiementHistoryTable.setItems(allPaiements);
                paiementHistoryTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSel, newSel) -> showDetails(newSel));
            }

            updateStatistics();

        } catch (Exception e) {
            System.err.println("Error loading payments: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur de chargement", "Erreur lors du chargement des données: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void applyFilters() {
        try {
            List<Paiement> filtered = allPayments.stream()
                .filter(this::matchesFilters)
                .collect(Collectors.toList());

            filteredPayments.setAll(filtered);
            updateStatistics();
            updateFilteredCount();

        } catch (Exception e) {
            System.err.println("Error applying filters: " + e.getMessage());
        }
    }

    private boolean matchesFilters(Paiement payment) {
        // Search filter
        if (searchField != null) {
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                String clientName = payment.getLocation() != null && payment.getLocation().getClient() != null ?
                    (payment.getLocation().getClient().getNom() + " " + payment.getLocation().getClient().getPrenom()).toLowerCase() : "";
                String vehicleName = payment.getLocation() != null && payment.getLocation().getVehicule() != null ?
                    (payment.getLocation().getVehicule().getMarque() + " " + payment.getLocation().getVehicule().getModele()).toLowerCase() : "";

                if (!clientName.contains(searchLower) && !vehicleName.contains(searchLower) &&
                    !String.valueOf(payment.getId()).contains(searchLower) &&
                    !String.format("%.2f", payment.getMontant()).contains(searchLower)) {
                    return false;
                }
            }
        }

        // Status filter
        if (filterStatusCombo != null && filterStatusCombo.getValue() != null && !filterStatusCombo.getValue().equals("Tous")) {
            if (payment.getStatut() == null || !payment.getStatut().equals(filterStatusCombo.getValue())) {
                return false;
            }
        }

        // Date filters
        if (filterDateFrom != null && filterDateFrom.getValue() != null) {
            if (payment.getDatePaiement() == null || payment.getDatePaiement().isBefore(filterDateFrom.getValue())) {
                return false;
            }
        }
        if (filterDateTo != null && filterDateTo.getValue() != null) {
            if (payment.getDatePaiement() == null || payment.getDatePaiement().isAfter(filterDateTo.getValue())) {
                return false;
            }
        }

        // Amount filters
        try {
            if (filterAmountMin != null && filterAmountMin.getText() != null && !filterAmountMin.getText().trim().isEmpty()) {
                double minAmount = Double.parseDouble(filterAmountMin.getText().trim());
                if (payment.getMontant() < minAmount) {
                    return false;
                }
            }
            if (filterAmountMax != null && filterAmountMax.getText() != null && !filterAmountMax.getText().trim().isEmpty()) {
                double maxAmount = Double.parseDouble(filterAmountMax.getText().trim());
                if (payment.getMontant() > maxAmount) {
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            // Ignore invalid amount inputs
        }

        return true;
    }

    private void updateStatistics() {
        try {
            // Total revenue
            double totalRevenue = filteredPayments.stream()
                .mapToDouble(Paiement::getMontant)
                .sum();
            if (lblTotalRevenue != null) {
                lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));
            }

            // Paid count
            long paidCount = filteredPayments.stream()
                .filter(p -> "Payé".equals(p.getStatut()))
                .count();
            if (lblPaidCount != null) {
                lblPaidCount.setText(String.valueOf(paidCount));
            }

            // Pending count
            long pendingCount = filteredPayments.stream()
                .filter(p -> "En attente".equals(p.getStatut()))
                .count();
            if (lblPendingCount != null) {
                lblPendingCount.setText(String.valueOf(pendingCount));
            }

            // Overdue count
            long overdueCount = filteredPayments.stream()
                .filter(p -> "En retard".equals(p.getStatut()))
                .count();
            if (lblOverdueCount != null) {
                lblOverdueCount.setText(String.valueOf(overdueCount));
            }

            // Update percentages and amounts
            if (lblPaidPercentage != null && !filteredPayments.isEmpty()) {
                double percentage = (double) paidCount / filteredPayments.size() * 100;
                lblPaidPercentage.setText(String.format("%.1f%% du total", percentage));
            }

            updateFilteredCount();

        } catch (Exception e) {
            System.err.println("Error updating statistics: " + e.getMessage());
        }
    }

    private void updateFilteredCount() {
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filteredPayments.size() + " paiements");
        }

        double filteredRevenue = filteredPayments.stream()
            .mapToDouble(Paiement::getMontant)
            .sum();
        if (lblFilteredRevenue != null) {
            lblFilteredRevenue.setText(String.format("Revenus filtrés: %.2f DH", filteredRevenue));
        }
    }

    private void updateSelectedCount() {
        // Simplified count - in real implementation, you'd track checkbox states
        if (lblSelectedCount != null) {
            lblSelectedCount.setText("Sélectionnés: 0");
        }
    }

    private void updateLastUpdateTime() {
        Platform.runLater(() -> {
            String currentTime = java.time.LocalTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("HH:mm")
            );
            if (lblLastUpdate != null) {
                lblLastUpdate.setText("Dernière mise à jour: " + currentTime);
            }
        });
    }

    @FXML
    private void handleClearFilters() {
        // Clear all filters
        if (filterStatusCombo != null) filterStatusCombo.setValue("Tous");
        if (filterMethodCombo != null) filterMethodCombo.setValue("Toutes");
        if (filterClientCombo != null) filterClientCombo.setValue("Tous");
        if (filterDateFrom != null) filterDateFrom.setValue(null);
        if (filterDateTo != null) filterDateTo.setValue(null);
        if (filterAmountMin != null) filterAmountMin.clear();
        if (filterAmountMax != null) filterAmountMax.clear();
        if (filterVehicleCombo != null) filterVehicleCombo.setValue("Tous");
        if (filterOverdueOnly != null) filterOverdueOnly.setSelected(false);
        if (filterPartialOnly != null) filterPartialOnly.setSelected(false);
        if (searchField != null) searchField.clear();

        // Also clear legacy fields if they exist
        if (searchClientField != null) searchClientField.clear();
        if (searchVehiculeField != null) searchVehiculeField.clear();
        if (dateDebutFilter != null) dateDebutFilter.setValue(null);
        if (dateFinFilter != null) dateFinFilter.setValue(null);

        // Refresh data
        loadData();
    }

    // Legacy filter handler for backward compatibility
    @FXML
    private void handleFilter() {
        try {
            String client = searchClientField != null ? searchClientField.getText().toLowerCase() : "";
            String vehicule = searchVehiculeField != null ? searchVehiculeField.getText().toLowerCase() : "";
            LocalDate debut = dateDebutFilter != null ? dateDebutFilter.getValue() : null;
            LocalDate fin = dateFinFilter != null ? dateFinFilter.getValue() : null;

            List<Paiement> filtered = allPaiements.stream().filter(p -> {
                boolean match = true;
                Location l = p.getLocation();
                if (!client.isEmpty()) {
                    match &= l != null && l.getClient() != null && (l.getClient().getNom() + " " + l.getClient().getPrenom()).toLowerCase().contains(client);
                }
                if (!vehicule.isEmpty()) {
                    match &= l != null && l.getVehicule() != null && (l.getVehicule().getMarque() + " " + l.getVehicule().getModele()).toLowerCase().contains(vehicule);
                }
                if (debut != null) {
                    match &= p.getDatePaiement() != null && !p.getDatePaiement().isBefore(debut);
                }
                if (fin != null) {
                    match &= p.getDatePaiement() != null && !p.getDatePaiement().isAfter(fin);
                }
                return match;
            }).collect(Collectors.toList());

            if (paiementHistoryTable != null) {
                paiementHistoryTable.setItems(FXCollections.observableArrayList(filtered));
            }
        } catch (Exception e) {
            System.err.println("Error in legacy filter: " + e.getMessage());
        }
    }

    @FXML
    private void handleExport() {
        // Export logic placeholder (CSV, Excel, etc.)
        // You can implement file writing here
        Alert alert = new Alert(Alert.AlertType.INFORMATION, "Exportation des paiements terminée (fonctionnalité à compléter).", ButtonType.OK);
        alert.showAndWait();
    }

    private void showDetails(Paiement p) {
        if (p == null) {
            if (detailsLabel != null) {
                detailsLabel.setText("Sélectionnez un paiement pour voir les détails.");
            }
            return;
        }
        StringBuilder sb = new StringBuilder();
        Location l = p.getLocation();
        sb.append("Client: ").append(l != null && l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "").append("\n");
        sb.append("Véhicule: ").append(l != null && l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "").append("\n");
        sb.append("Date paiement: ").append(p.getDatePaiement() != null ? p.getDatePaiement().toString() : "").append("\n");
        sb.append("Montant: ").append(String.format("%.2f DH", p.getMontant())).append("\n");
        sb.append("Méthode: ").append(p.getMethodePaiement() != null ? p.getMethodePaiement() : "N/A").append("\n");
        if (detailsLabel != null) {
            detailsLabel.setText(sb.toString());
        }
    }

    // Enhanced FXML Action Handlers
    @FXML
    private void handleRefresh() {
        initialize();
    }

    @FXML
    private void handleExportCSV() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les paiements");
            fileChooser.setInitialFileName("historique_paiements.csv");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );

            File file = fileChooser.showSaveDialog(paymentsTable != null ? paymentsTable.getScene().getWindow() : null);
            if (file != null) {
                exportToCSV(file);
                showAlert("Export réussi", "Les paiements ont été exportés vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur d'export", "Erreur lors de l'export: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleExportExcel() {
        showAlert("Export Excel", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportPDF() {
        showAlert("Export PDF", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportUnpaid() {
        showAlert("Export Impayés", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportOverdue() {
        showAlert("Export En Retard", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleBulkActions() {
        showAlert("Actions Groupées", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleMarkPaid() {
        if (selectedPayment != null) {
            showAlert("Marquer Payé", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner un paiement", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleSendReminder() {
        if (selectedPayment != null) {
            showAlert("Envoyer Rappel", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner un paiement", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleViewContract() {
        if (selectedPayment != null && selectedPayment.getLocation() != null) {
            showAlert("Voir Contrat", "Contrat de la location #" + selectedPayment.getLocation().getId(), Alert.AlertType.INFORMATION);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner un paiement avec une location", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleGenerateInvoice() {
        if (selectedPayment != null) {
            handleGenerateInvoice(selectedPayment);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner un paiement", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleReconcile() {
        showAlert("Rapprochement", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleAnalytics() {
        showAlert("Analyses", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void closeWindow() {
        Stage stage = (Stage) (paymentsTable != null ? paymentsTable.getScene().getWindow() : null);
        if (stage != null) {
            stage.close();
        }
    }

    // Helper methods for actions
    private void handleEditPayment(Paiement payment) {
        showAlert("Modifier Paiement", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    private void handleGenerateInvoice(Paiement payment) {
        showAlert("Générer Facture", "Facture pour le paiement #" + payment.getId(), Alert.AlertType.INFORMATION);
    }

    private void showPaymentDetails(Paiement payment) {
        showDetails(payment);
        selectedPayment = payment;
    }

    private void exportToCSV(File file) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            // Write CSV header
            writer.println("ID,Client,Véhicule,Date,Montant,Statut,Méthode,Référence");

            // Write payment data
            List<Paiement> paymentsToExport = filteredPayments != null ? filteredPayments : allPayments;
            for (Paiement payment : paymentsToExport) {
                String clientName = payment.getLocation() != null && payment.getLocation().getClient() != null ?
                    escapeCSV(payment.getLocation().getClient().getNom() + " " + payment.getLocation().getClient().getPrenom()) : "N/A";
                String vehicleName = payment.getLocation() != null && payment.getLocation().getVehicule() != null ?
                    escapeCSV(payment.getLocation().getVehicule().getMarque() + " " + payment.getLocation().getVehicule().getModele()) : "N/A";
                String date = payment.getDatePaiement() != null ?
                    payment.getDatePaiement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A";

                writer.printf("%d,%s,%s,%s,%.2f,%s,%s,%s%n",
                    payment.getId(),
                    clientName,
                    vehicleName,
                    date,
                    payment.getMontant(),
                    escapeCSV(payment.getStatut() != null ? payment.getStatut() : "N/A"),
                    escapeCSV(payment.getMethodePaiement() != null ? payment.getMethodePaiement() : "N/A"),
                    escapeCSV(payment.getReference() != null ? payment.getReference() : "N/A")
                );
            }
        }
    }

    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Platform.runLater(() -> {
            Alert alert = new Alert(type);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
}