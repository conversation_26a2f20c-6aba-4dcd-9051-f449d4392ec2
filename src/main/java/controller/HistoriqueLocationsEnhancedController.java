package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.geometry.Pos;
import model.*;
import dao.*;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

public class HistoriqueLocationsEnhancedController implements Initializable {

    // Filter Controls
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<Vehicule> filterVehicleCombo;
    @FXML private ComboBox<Client> filterClientCombo;
    @FXML private DatePicker filterDateFrom;
    @FXML private DatePicker filterDateTo;
    @FXML private TextField filterPriceMin;
    @FXML private TextField filterPriceMax;
    @FXML private ComboBox<String> filterDurationCombo;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterProfitableOnly;
    @FXML private TextField searchField;

    // Statistics Labels
    @FXML private Label lblTotalLocations;
    @FXML private Label lblActiveLocations;
    @FXML private Label lblCompletedLocations;
    @FXML private Label lblOverdueLocations;
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblAverageRevenue;
    @FXML private Label lblTopClient;
    @FXML private Label lblTopVehicle;

    // Charts and Analytics
    @FXML private VBox vehicleUsageChart;
    @FXML private VBox revenueChart;

    // Table and Controls
    @FXML private TableView<LocationRow> locationsTable;
    @FXML private TableColumn<LocationRow, Boolean> colSelect;
    @FXML private TableColumn<LocationRow, Long> colId;
    @FXML private TableColumn<LocationRow, String> colClient;
    @FXML private TableColumn<LocationRow, String> colVehicle;
    @FXML private TableColumn<LocationRow, String> colDateDebut;
    @FXML private TableColumn<LocationRow, String> colDateFin;
    @FXML private TableColumn<LocationRow, String> colStatus;
    @FXML private TableColumn<LocationRow, Double> colPrice;
    @FXML private TableColumn<LocationRow, String> colDuration;
    @FXML private TableColumn<LocationRow, String> colProfitability;
    @FXML private TableColumn<LocationRow, Void> colActions;

    // Sidebar
    @FXML private VBox locationDetailCard;
    @FXML private Label lblSelectedLocation;
    @FXML private VBox locationDetails;
    @FXML private VBox locationTimeline;

    // Bottom Statistics
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredRevenue;
    @FXML private Label lblLastUpdate;

    // Action Buttons
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnBulkActions;

    private LocationDAO locationDAO;
    private ClientDAO clientDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<LocationRow> allLocations;
    private ObservableList<LocationRow> filteredLocations;
    private LocationRow selectedLocation;

    // Inner class for table rows with selection
    public static class LocationRow {
        private final BooleanProperty selected = new SimpleBooleanProperty(false);
        private final Location location;
        private final Client client;
        private final Vehicule vehicule;

        public LocationRow(Location location) {
            this.location = location;
            this.client = location.getClient();
            this.vehicule = location.getVehicule();
        }

        // Getters
        public BooleanProperty selectedProperty() { return selected; }
        public boolean isSelected() { return selected.get(); }
        public void setSelected(boolean selected) { this.selected.set(selected); }
        public Location getLocation() { return location; }
        public Client getClient() { return client; }
        public Vehicule getVehicule() { return vehicule; }

        // Table column getters
        public Long getId() { return location.getId(); }
        public String getClientName() { 
            return client != null ? client.getPrenom() + " " + client.getNom() : "N/A"; 
        }
        public String getVehicleName() { 
            return vehicule != null ? vehicule.getMarque() + " " + vehicule.getModele() : "N/A"; 
        }
        public String getDateDebut() { 
            return location.getDateDebut() != null ? 
                location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A"; 
        }
        public String getDateFin() { 
            return location.getDateFinPrevue() != null ? 
                location.getDateFinPrevue().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A"; 
        }
        public String getStatus() { 
            return location.getStatus() != null ? 
                getStatusDisplayName(location.getStatus()) : "N/A"; 
        }
        public Double getPrice() { return location.getPrixTotal(); }
        public String getDuration() {
            if (location.getDateDebut() != null && location.getDateFinPrevue() != null) {
                long days = ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFinPrevue());
                return days + " jours";
            }
            return "N/A";
        }
        public String getProfitability() {
            double profit = calculateProfitability();
            return profit > 0 ? "Rentable" : "Non rentable";
        }

        private double calculateProfitability() {
            // Simple profitability calculation (30% cost assumption)
            return location.getPrixTotal() - (location.getPrixTotal() * 0.3);
        }

        private String getStatusDisplayName(Location.Status status) {
            return switch (status) {
                case RESERVE -> "Réservé";
                case EN_COURS -> "En cours";
                case TERMINE -> "Terminé";
                case ANNULE -> "Annulé";
            };
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        locationDAO = new LocationDAO();
        clientDAO = new ClientDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilterControls();
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    private void setupTableColumns() {
        // Selection column
        colSelect.setCellValueFactory(cellData -> cellData.getValue().selectedProperty());
        colSelect.setCellFactory(CheckBoxTableCell.forTableColumn(colSelect));
        colSelect.setEditable(true);

        // Data columns
        colId.setCellValueFactory(new PropertyValueFactory<>("id"));
        colClient.setCellValueFactory(new PropertyValueFactory<>("clientName"));
        colVehicle.setCellValueFactory(new PropertyValueFactory<>("vehicleName"));
        colDateDebut.setCellValueFactory(new PropertyValueFactory<>("dateDebut"));
        colDateFin.setCellValueFactory(new PropertyValueFactory<>("dateFin"));
        colStatus.setCellValueFactory(new PropertyValueFactory<>("status"));
        colPrice.setCellValueFactory(new PropertyValueFactory<>("price"));
        colDuration.setCellValueFactory(new PropertyValueFactory<>("duration"));
        colProfitability.setCellValueFactory(new PropertyValueFactory<>("profitability"));

        // Format price column
        colPrice.setCellFactory(column -> new TableCell<LocationRow, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });

        // Actions column
        colActions.setCellFactory(column -> new TableCell<LocationRow, Void>() {
            private final Button viewBtn = new Button("👁");
            private final Button editBtn = new Button("✏");
            private final HBox buttons = new HBox(5, viewBtn, editBtn);

            {
                buttons.setAlignment(Pos.CENTER);
                viewBtn.setOnAction(e -> {
                    LocationRow row = getTableView().getItems().get(getIndex());
                    showLocationDetails(row);
                });
                editBtn.setOnAction(e -> {
                    LocationRow row = getTableView().getItems().get(getIndex());
                    editLocation(row);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : buttons);
            }
        });

        // Table selection listener
        locationsTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    selectedLocation = newSelection;
                    showLocationDetails(newSelection);
                }
            }
        );

        locationsTable.setEditable(true);
    }

    private void setupFilterControls() {
        // Status filter
        filterStatusCombo.setItems(FXCollections.observableArrayList(
            "Tous", "Réservé", "En cours", "Terminé", "Annulé"
        ));
        filterStatusCombo.setValue("Tous");

        // Duration filter
        filterDurationCombo.setItems(FXCollections.observableArrayList(
            "Toutes", "1-3 jours", "4-7 jours", "1-2 semaines", "Plus de 2 semaines"
        ));
        filterDurationCombo.setValue("Toutes");

        // Load clients and vehicles
        List<Client> clients = clientDAO.findAll();
        filterClientCombo.setItems(FXCollections.observableArrayList(clients));

        List<Vehicule> vehicules = vehiculeDAO.findAll();
        filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicules));

        // Add listeners for real-time filtering
        filterStatusCombo.setOnAction(e -> applyFilters());
        filterVehicleCombo.setOnAction(e -> applyFilters());
        filterClientCombo.setOnAction(e -> applyFilters());
        filterDateFrom.setOnAction(e -> applyFilters());
        filterDateTo.setOnAction(e -> applyFilters());
        filterDurationCombo.setOnAction(e -> applyFilters());
        filterOverdueOnly.setOnAction(e -> applyFilters());
        filterProfitableOnly.setOnAction(e -> applyFilters());

        // Text field listeners
        filterPriceMin.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        filterPriceMax.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void loadData() {
        List<Location> locations = locationDAO.findAll();
        allLocations = FXCollections.observableArrayList(
            locations.stream().map(LocationRow::new).collect(Collectors.toList())
        );
        filteredLocations = FXCollections.observableArrayList(allLocations);
        locationsTable.setItems(filteredLocations);
    }

    private void applyFilters() {
        if (allLocations == null) return;

        List<LocationRow> filtered = allLocations.stream()
            .filter(this::matchesFilters)
            .collect(Collectors.toList());

        filteredLocations.setAll(filtered);
        updateStatistics();
        updateSelectionCount();
    }

    private boolean matchesFilters(LocationRow row) {
        Location location = row.getLocation();

        // Status filter
        String selectedStatus = filterStatusCombo.getValue();
        if (!"Tous".equals(selectedStatus)) {
            String statusName = row.getStatus();
            if (!selectedStatus.equals(statusName)) return false;
        }

        // Client filter
        Client selectedClient = filterClientCombo.getValue();
        if (selectedClient != null) {
            if (row.getClient() == null || !row.getClient().getId().equals(selectedClient.getId())) {
                return false;
            }
        }

        // Vehicle filter
        Vehicule selectedVehicle = filterVehicleCombo.getValue();
        if (selectedVehicle != null) {
            if (row.getVehicule() == null || !row.getVehicule().getId().equals(selectedVehicle.getId())) {
                return false;
            }
        }

        // Date range filter
        LocalDate fromDate = filterDateFrom.getValue();
        LocalDate toDate = filterDateTo.getValue();
        LocalDate locationDate = location.getDateDebut();
        if (locationDate != null) {
            if (fromDate != null && locationDate.isBefore(fromDate)) return false;
            if (toDate != null && locationDate.isAfter(toDate)) return false;
        }

        // Price range filter
        try {
            String minText = filterPriceMin.getText();
            if (!minText.isEmpty()) {
                double min = Double.parseDouble(minText);
                if (location.getPrixTotal() < min) return false;
            }

            String maxText = filterPriceMax.getText();
            if (!maxText.isEmpty()) {
                double max = Double.parseDouble(maxText);
                if (location.getPrixTotal() > max) return false;
            }
        } catch (NumberFormatException e) {
            // Invalid number format, ignore filter
        }

        // Duration filter
        String selectedDuration = filterDurationCombo.getValue();
        if (!"Toutes".equals(selectedDuration)) {
            if (location.getDateDebut() != null && location.getDateFinPrevue() != null) {
                long days = ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFinPrevue());
                boolean matches = switch (selectedDuration) {
                    case "1-3 jours" -> days >= 1 && days <= 3;
                    case "4-7 jours" -> days >= 4 && days <= 7;
                    case "1-2 semaines" -> days >= 8 && days <= 14;
                    case "Plus de 2 semaines" -> days > 14;
                    default -> true;
                };
                if (!matches) return false;
            }
        }

        // Overdue filter
        if (filterOverdueOnly.isSelected()) {
            if (location.getDateFinPrevue() == null || 
                location.getDateFinReelle() != null ||
                !LocalDate.now().isAfter(location.getDateFinPrevue())) {
                return false;
            }
        }

        // Profitability filter
        if (filterProfitableOnly.isSelected()) {
            LocationRow locationRow = new LocationRow(location);
            if (locationRow.calculateProfitability() <= 0) return false;
        }

        // Search filter
        String searchText = searchField.getText();
        if (searchText != null && !searchText.trim().isEmpty()) {
            String searchLower = searchText.toLowerCase();
            return row.getClientName().toLowerCase().contains(searchLower) ||
                   row.getVehicleName().toLowerCase().contains(searchLower) ||
                   String.valueOf(location.getPrixTotal()).contains(searchLower);
        }

        return true;
    }

    private void updateStatistics() {
        List<LocationRow> locations = filteredLocations;

        lblTotalLocations.setText(String.valueOf(locations.size()));

        long active = locations.stream()
            .filter(l -> l.getLocation().getStatus() == Location.Status.EN_COURS)
            .count();
        lblActiveLocations.setText(String.valueOf(active));

        long completed = locations.stream()
            .filter(l -> l.getLocation().getStatus() == Location.Status.TERMINE)
            .count();
        lblCompletedLocations.setText(String.valueOf(completed));

        long overdue = locations.stream()
            .filter(l -> l.getLocation().getDateFinPrevue() != null && 
                        l.getLocation().getDateFinReelle() == null &&
                        LocalDate.now().isAfter(l.getLocation().getDateFinPrevue()))
            .count();
        lblOverdueLocations.setText(String.valueOf(overdue));

        double totalRevenue = locations.stream()
            .mapToDouble(l -> l.getLocation().getPrixTotal())
            .sum();
        lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));

        double avgRevenue = locations.isEmpty() ? 0 : totalRevenue / locations.size();
        lblAverageRevenue.setText(String.format("%.2f DH", avgRevenue));

        // Update charts
        updateVehicleUsageChart(locations);
        updateRevenueChart(locations);

        // Bottom statistics
        lblTotalCount.setText(String.format("Total: %d locations", locations.size()));
        lblFilteredRevenue.setText(String.format("Revenus filtrés: %.2f DH", totalRevenue));
    }

    private void updateVehicleUsageChart(List<LocationRow> locations) {
        vehicleUsageChart.getChildren().clear();

        Map<String, Long> vehicleUsage = locations.stream()
            .collect(Collectors.groupingBy(LocationRow::getVehicleName, Collectors.counting()));

        vehicleUsage.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .limit(5)
            .forEach(entry -> {
                HBox vehicleRow = new HBox(10);
                vehicleRow.setAlignment(Pos.CENTER_LEFT);

                Label vehicleLabel = new Label(entry.getKey());
                vehicleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                Label countLabel = new Label(entry.getValue().toString());
                countLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

                vehicleRow.getChildren().addAll(vehicleLabel, countLabel);
                vehicleUsageChart.getChildren().add(vehicleRow);
            });
    }

    private void updateRevenueChart(List<LocationRow> locations) {
        revenueChart.getChildren().clear();

        Map<String, Double> monthlyRevenue = locations.stream()
            .filter(l -> l.getLocation().getDateDebut() != null)
            .collect(Collectors.groupingBy(
                l -> l.getLocation().getDateDebut().format(DateTimeFormatter.ofPattern("MM/yyyy")),
                Collectors.summingDouble(l -> l.getLocation().getPrixTotal())
            ));

        monthlyRevenue.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByKey().reversed())
            .limit(6)
            .forEach(entry -> {
                HBox monthRow = new HBox(10);
                monthRow.setAlignment(Pos.CENTER_LEFT);

                Label monthLabel = new Label(entry.getKey());
                monthLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                Label revenueLabel = new Label(String.format("%.2f DH", entry.getValue()));
                revenueLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

                monthRow.getChildren().addAll(monthLabel, revenueLabel);
                revenueChart.getChildren().add(monthRow);
            });
    }

    private void showLocationDetails(LocationRow row) {
        if (row == null) return;

        selectedLocation = row;
        Location location = row.getLocation();

        lblSelectedLocation.setText("Location #" + location.getId());

        locationDetails.getChildren().clear();

        // Add location details
        addDetailRow("Client:", row.getClientName());
        addDetailRow("Véhicule:", row.getVehicleName());
        addDetailRow("Prix:", String.format("%.2f DH", location.getPrixTotal()));
        addDetailRow("Statut:", row.getStatus());
        addDetailRow("Début:", row.getDateDebut());
        addDetailRow("Fin prévue:", row.getDateFin());
        addDetailRow("Durée:", row.getDuration());
        addDetailRow("Rentabilité:", row.getProfitability());

        // Update timeline
        updateLocationTimeline(location);
    }

    private void addDetailRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);

        Label labelNode = new Label(label);
        labelNode.setStyle("-fx-font-weight: bold; -fx-text-fill: #374151; -fx-min-width: 80px;");

        Label valueNode = new Label(value);
        valueNode.setStyle("-fx-text-fill: #1f2937;");

        row.getChildren().addAll(labelNode, valueNode);
        locationDetails.getChildren().add(row);
    }

    private void updateLocationTimeline(Location location) {
        locationTimeline.getChildren().clear();

        // Add timeline events
        addTimelineEvent("Création", location.getDateDebut(), "Location créée");
        
        if (location.getStatus() == Location.Status.EN_COURS) {
            addTimelineEvent("Début", location.getDateDebut(), "Location démarrée");
        }
        
        if (location.getStatus() == Location.Status.TERMINE && location.getDateFinReelle() != null) {
            addTimelineEvent("Fin", location.getDateFinReelle(), "Location terminée");
        }
    }

    private void addTimelineEvent(String title, LocalDate date, String description) {
        VBox event = new VBox(5);
        event.setStyle("-fx-padding: 8px; -fx-border-color: #e5e7eb; -fx-border-width: 0 0 1px 0;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #1f2937;");

        Label dateLabel = new Label(date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        dateLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6b7280;");

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #374151;");

        event.getChildren().addAll(titleLabel, dateLabel, descLabel);
        locationTimeline.getChildren().add(event);
    }

    private void updateSelectionCount() {
        long selectedCount = filteredLocations.stream()
            .filter(LocationRow::isSelected)
            .count();
        lblSelectedCount.setText(String.format("Sélectionnés: %d", selectedCount));
    }

    private void updateLastUpdateTime() {
        lblLastUpdate.setText("Dernière mise à jour: " + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private void editLocation(LocationRow row) {
        FXMLUtil.showInfo("Fonctionnalité d'édition en cours de développement.");
    }

    // Event Handlers
    @FXML private void handleRefresh() {
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    @FXML private void handleClearFilters() {
        filterStatusCombo.setValue("Tous");
        filterVehicleCombo.setValue(null);
        filterClientCombo.setValue(null);
        filterDateFrom.setValue(null);
        filterDateTo.setValue(null);
        filterPriceMin.clear();
        filterPriceMax.clear();
        filterDurationCombo.setValue("Toutes");
        filterOverdueOnly.setSelected(false);
        filterProfitableOnly.setSelected(false);
        searchField.clear();
        applyFilters();
    }

    @FXML private void handleBulkActions() {
        List<LocationRow> selected = filteredLocations.stream()
            .filter(LocationRow::isSelected)
            .collect(Collectors.toList());
        
        if (selected.isEmpty()) {
            FXMLUtil.showWarning("Veuillez sélectionner au moins une location.");
            return;
        }
        
        FXMLUtil.showInfo(String.format("Actions groupées pour %d locations sélectionnées.", selected.size()));
    }

    @FXML private void closeWindow() {
        locationsTable.getScene().getWindow().hide();
    }
}
