package controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import util.FXMLUtil;

import java.awt.Desktop;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ResourceBundle;

public class HelpSupportController implements Initializable {

    @FXML private Label lblAppVersion;
    @FXML private Label lblJavaVersion;
    @FXML private Label lblOSVersion;
    @FXML private Label lblDatabaseStatus;

    @FXML private Button btnContactSupport;
    @FXML private Button btnUserGuide;
    @FXML private Button btnVideoTutorials;
    @FXML private Button btnFAQ;
    @FXML private Button btnKeyboardShortcuts;
    @FXML private Button btnGenerateReport;
    @FXML private Button btnCheckUpdates;
    @FXML private Button btnResetSettings;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        loadSystemInformation();
    }

    private void loadSystemInformation() {
        // Load system information
        lblAppVersion.setText("LocationV12 v1.0.0");
        lblJavaVersion.setText(System.getProperty("java.version"));
        lblOSVersion.setText(System.getProperty("os.name") + " " + System.getProperty("os.version"));
        
        // Check database status (simplified)
        try {
            // This would normally check database connectivity
            lblDatabaseStatus.setText("Connectée ✅");
            lblDatabaseStatus.setStyle("-fx-text-fill: #28a745;");
        } catch (Exception e) {
            lblDatabaseStatus.setText("Déconnectée ❌");
            lblDatabaseStatus.setStyle("-fx-text-fill: #dc3545;");
        }
    }

    @FXML
    private void handleContactSupport() {
        // Create contact support dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Contacter le Support");
        dialog.setHeaderText("Envoyer un message au support technique");

        VBox content = new VBox(15);
        content.setStyle("-fx-padding: 20px;");

        TextField subjectField = new TextField();
        subjectField.setPromptText("Objet du message");
        subjectField.setStyle("-fx-pref-width: 400px;");

        TextArea messageArea = new TextArea();
        messageArea.setPromptText("Décrivez votre problème en détail...");
        messageArea.setPrefRowCount(6);
        messageArea.setStyle("-fx-pref-width: 400px;");

        TextField emailField = new TextField();
        emailField.setPromptText("Votre adresse email");
        emailField.setStyle("-fx-pref-width: 400px;");

        CheckBox includeSystemInfo = new CheckBox("Inclure les informations système");
        includeSystemInfo.setSelected(true);

        content.getChildren().addAll(
            new Label("Objet:"), subjectField,
            new Label("Message:"), messageArea,
            new Label("Email de contact:"), emailField,
            includeSystemInfo
        );

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                String subject = subjectField.getText();
                String message = messageArea.getText();
                String email = emailField.getText();

                if (subject.isEmpty() || message.isEmpty() || email.isEmpty()) {
                    FXMLUtil.showWarning("Veuillez remplir tous les champs obligatoires.");
                    return;
                }

                // In a real application, this would send the message to support
                FXMLUtil.showSuccess("Votre message a été envoyé au support technique. Vous recevrez une réponse dans les 24 heures.");
            }
        });
    }

    @FXML
    private void handleUserGuide() {
        // Create user guide dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Guide Utilisateur");
        alert.setHeaderText("Guide d'utilisation de LocationV12");
        
        String guideContent = """
            🚀 DÉMARRAGE RAPIDE
            
            1. CONNEXION
            • Utilisez vos identifiants fournis par l'administrateur
            • Votre rôle détermine vos permissions (Admin/Agent)
            
            2. NAVIGATION
            • Utilisez le menu latéral pour naviguer entre les sections
            • Dashboard: Vue d'ensemble des statistiques
            • Clients: Gestion des clients
            • Véhicules: Gestion de la flotte
            • Locations: Créer et gérer les locations
            • Paiements: Suivi des paiements
            
            3. NOTIFICATIONS
            • Cliquez sur l'icône 🔔 pour voir vos notifications
            • Configurez vos préférences dans Paramètres
            • Les notifications importantes apparaissent dans la barre des tâches
            
            4. RACCOURCIS UTILES
            • Ctrl+N: Nouvelle location
            • Ctrl+F: Rechercher
            • Ctrl+S: Sauvegarder
            • F5: Actualiser
            """;
        
        alert.setContentText(guideContent);
        alert.getDialogPane().setPrefWidth(600);
        alert.showAndWait();
    }

    @FXML
    private void handleVideoTutorials() {
        try {
            // In a real application, this would open video tutorials
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().browse(new URI("https://www.youtube.com/playlist?list=PLLocationV12Tutorials"));
            }
        } catch (Exception e) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setContentText("Les tutoriels vidéo seront bientôt disponibles sur notre site web.");
            alert.showAndWait();
        }
    }

    @FXML
    private void handleFAQ() {
        // Create FAQ dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Questions Fréquemment Posées");
        alert.setHeaderText("FAQ - LocationV12");
        
        String faqContent = """
            ❓ QUESTIONS FRÉQUENTES
            
            Q: Comment créer une nouvelle location ?
            R: Allez dans Locations → Nouvelle Location, sélectionnez un client et un véhicule, puis définissez les dates.
            
            Q: Comment ajouter un nouveau client ?
            R: Dans la section Clients, cliquez sur "Ajouter" et remplissez le formulaire.
            
            Q: Que faire si un véhicule n'est pas disponible ?
            R: Vérifiez dans Véhicules → Disponibilité. Le statut doit être "Disponible".
            
            Q: Comment configurer les notifications ?
            R: Allez dans Profil → Paramètres → Notifications pour personnaliser vos préférences.
            
            Q: Comment générer un rapport ?
            R: Utilisez les boutons d'export dans chaque section ou le tableau de bord.
            
            Q: Que faire en cas de problème technique ?
            R: Contactez le support via le bouton "Contacter le Support" ou par email.
            """;
        
        alert.setContentText(faqContent);
        alert.getDialogPane().setPrefWidth(700);
        alert.showAndWait();
    }

    @FXML
    private void handleKeyboardShortcuts() {
        // Create keyboard shortcuts dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Raccourcis Clavier");
        alert.setHeaderText("Raccourcis clavier disponibles");
        
        String shortcutsContent = """
            ⌨️ RACCOURCIS CLAVIER
            
            NAVIGATION
            • Ctrl+1: Dashboard
            • Ctrl+2: Clients
            • Ctrl+3: Véhicules
            • Ctrl+4: Locations
            • Ctrl+5: Paiements
            
            ACTIONS
            • Ctrl+N: Nouveau (selon la page active)
            • Ctrl+S: Sauvegarder
            • Ctrl+F: Rechercher
            • Ctrl+R: Actualiser
            • Delete: Supprimer l'élément sélectionné
            
            GÉNÉRAL
            • F1: Aide
            • F5: Actualiser la page
            • Ctrl+Q: Quitter l'application
            • Alt+F4: Fermer la fenêtre
            
            NOTIFICATIONS
            • Ctrl+Shift+N: Ouvrir le centre de notifications
            • Ctrl+Shift+M: Marquer toutes les notifications comme lues
            """;
        
        alert.setContentText(shortcutsContent);
        alert.getDialogPane().setPrefWidth(600);
        alert.showAndWait();
    }

    @FXML
    private void handleGenerateReport() {
        // Generate diagnostic report
        StringBuilder report = new StringBuilder();
        report.append("=== RAPPORT DE DIAGNOSTIC LOCATIONV12 ===\n\n");
        report.append("Date: ").append(LocalDateTime.now()).append("\n");
        report.append("Version: LocationV12 v1.0.0\n");
        report.append("Java: ").append(System.getProperty("java.version")).append("\n");
        report.append("OS: ").append(System.getProperty("os.name")).append(" ").append(System.getProperty("os.version")).append("\n");
        report.append("Utilisateur: ").append(LoginController.loggedInUser != null ? LoginController.loggedInUser.getClass() : "Non connecté").append("\n");
        report.append("Mémoire utilisée: ").append((Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / 1024 / 1024).append(" MB\n");
        report.append("Mémoire libre: ").append(Runtime.getRuntime().freeMemory() / 1024 / 1024).append(" MB\n");
        report.append("\n=== FIN DU RAPPORT ===");

        // Show report in dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Rapport de Diagnostic");
        alert.setHeaderText("Informations système générées");
        alert.setContentText(report.toString());
        alert.getDialogPane().setPrefWidth(600);
        alert.showAndWait();
    }

    @FXML
    private void handleCheckUpdates() {
        // Simulate checking for updates
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Vérification des Mises à Jour");
        alert.setHeaderText("Recherche de mises à jour...");
        alert.setContentText("Vous utilisez la dernière version de LocationV12 (v1.0.0).\nAucune mise à jour disponible.");
        alert.showAndWait();
    }

    @FXML
    private void handleResetSettings() {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Réinitialiser les Paramètres");
        confirmAlert.setHeaderText("Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?");
        confirmAlert.setContentText("Cette action restaurera tous les paramètres par défaut de l'application.");

        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // In a real application, this would reset all user settings
                FXMLUtil.showSuccess("Les paramètres ont été réinitialisés aux valeurs par défaut.");
            }
        });
    }
}
