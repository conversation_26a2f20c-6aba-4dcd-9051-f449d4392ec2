package controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import dao.LocationDAO;
import model.Location;
import dao.PaiementDAO;
import model.Paiement;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.XYChart;
import model.Client;
import model.Vehicule;
import java.util.List;
import java.time.LocalDate;
import javafx.event.ActionEvent;
import javafx.scene.Node;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import util.ExportUtil;

public class AgentDashboardController {
    @FXML private TableView<Location> reservationTable;
    @FXML private TableColumn<Location, Long> idColumn;
    @FXML private TableColumn<Location, String> clientColumn;
    @FXML private TableColumn<Location, String> vehiculeColumn;
    @FXML private TableColumn<Location, String> dateDebutColumn;
    @FXML private TableColumn<Location, String> dateFinColumn;
    @FXML private TableColumn<Location, String> statutColumn;
    @FXML private Button btnRefreshReservations;
    @FXML private TableView<Paiement> paymentTable;
    @FXML private TableColumn<Paiement, Long> payIdColumn;
    @FXML private TableColumn<Paiement, String> payClientColumn;
    @FXML private TableColumn<Paiement, String> payVehiculeColumn;
    @FXML private TableColumn<Paiement, Double> payMontantColumn;
    @FXML private TableColumn<Paiement, String> payDateColumn;
    @FXML private Button btnRefreshPayments;
    @FXML private Label lblTotalClients;
    @FXML private Label lblTotalVehicules;
    @FXML private Label lblActiveLocations;
    @FXML private Label lblTotalRevenue;
    @FXML private PieChart vehiculeStatusPie;
    @FXML private BarChart<String, Number> locationsBarChart;
    @FXML private CategoryAxis locationsMonthAxis;
    @FXML private NumberAxis locationsCountAxis;
    @FXML private BarChart<String, Number> revenueBarChart;
    @FXML private CategoryAxis revenueMonthAxis;
    @FXML private NumberAxis revenueAmountAxis;
    @FXML private Button btnDashboard;
    @FXML private Button btnReservations;
    @FXML private Button btnPaiements;
    @FXML private Button btnLogout;

    private final LocationDAO locationDAO = new LocationDAO();
    private ObservableList<Location> reservationList;
    private final PaiementDAO paiementDAO = new PaiementDAO();
    private ObservableList<Paiement> paymentList;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        clientColumn.setCellValueFactory(data -> {
            var c = data.getValue().getClient();
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        vehiculeColumn.setCellValueFactory(data -> {
            var v = data.getValue().getVehicule();
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        dateDebutColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateDebut();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        dateFinColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateFinPrevue();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        statutColumn.setCellValueFactory(data -> {
            var l = data.getValue();
            String statut = l.getDateFinReelle() == null ? "En cours" : "Terminée";
            return new javafx.beans.property.SimpleStringProperty(statut);
        });
        loadReservations();
        // Payment table columns
        payIdColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        payClientColumn.setCellValueFactory(data -> {
            var loc = data.getValue().getLocation();
            var c = loc != null ? loc.getClient() : null;
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        payVehiculeColumn.setCellValueFactory(data -> {
            var loc = data.getValue().getLocation();
            var v = loc != null ? loc.getVehicule() : null;
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        payMontantColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleDoubleProperty(data.getValue().getMontant()).asObject());
        payDateColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDatePaiement();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        loadPayments();
        loadDashboardStats();
    }

    @FXML
    private void handleRefreshReservations() {
        loadReservations();
    }

    private void loadReservations() {
        reservationList = FXCollections.observableArrayList(locationDAO.findAll());
        reservationTable.setItems(reservationList);
    }

    @FXML
    private void handleRefreshPayments() {
        loadPayments();
    }

    private void loadPayments() {
        paymentList = FXCollections.observableArrayList(paiementDAO.findAll());
        paymentTable.setItems(paymentList);
    }

    private void loadDashboardStats() {
        try {
            List<Client> clients = new dao.ClientDAO().findAll();
            List<Vehicule> vehicules = new dao.VehiculeDAO().findAll();
            List<Location> locations = new dao.LocationDAO().findAll();
            List<Paiement> paiements = new dao.PaiementDAO().findAll();
            if (lblTotalClients != null) lblTotalClients.setText(String.valueOf(clients.size()));
            if (lblTotalVehicules != null) lblTotalVehicules.setText(String.valueOf(vehicules.size()));
            if (lblActiveLocations != null) {
                long active = locations.stream().filter(l -> l.getDateFinReelle() == null).count();
                lblActiveLocations.setText(String.valueOf(active));
            }
            if (lblTotalRevenue != null) {
                double total = paiements.stream().mapToDouble(Paiement::getMontant).sum();
                lblTotalRevenue.setText(String.format("%.2f DH", total));
            }
            updateVehiculeStatusPie(vehicules);
            updateLocationsBarChart(locations);
            updateRevenueBarChart(paiements);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateVehiculeStatusPie(List<Vehicule> vehicules) {
        if (vehiculeStatusPie == null) return;
        ObservableList<PieChart.Data> pieData = FXCollections.observableArrayList();
        vehicules.stream().map(Vehicule::getEtat).distinct().forEach(etat -> {
            long count = vehicules.stream().filter(v -> etat.equals(v.getEtat())).count();
            pieData.add(new PieChart.Data(etat, count));
        });
        vehiculeStatusPie.setData(pieData);
    }

    private void updateLocationsBarChart(List<Location> locations) {
        if (locationsBarChart == null || locationsMonthAxis == null) return;
        locationsBarChart.getData().clear();
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        java.time.YearMonth now = java.time.YearMonth.now();
        for (int i = 11; i >= 0; i--) {
            java.time.YearMonth ym = now.minusMonths(i);
            String label = ym.getMonth().toString().substring(0, 3) + " " + ym.getYear();
            long count = locations.stream().filter(l -> l.getDateDebut() != null && java.time.YearMonth.from(l.getDateDebut()).equals(ym)).count();
            series.getData().add(new XYChart.Data<>(label, count));
        }
        locationsBarChart.getData().add(series);
    }

    private void updateRevenueBarChart(List<Paiement> paiements) {
        if (revenueBarChart == null || revenueMonthAxis == null) return;
        revenueBarChart.getData().clear();
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        java.time.YearMonth now = java.time.YearMonth.now();
        for (int i = 11; i >= 0; i--) {
            java.time.YearMonth ym = now.minusMonths(i);
            String label = ym.getMonth().toString().substring(0, 3) + " " + ym.getYear();
            double sum = paiements.stream().filter(p -> p.getDatePaiement() != null && java.time.YearMonth.from(p.getDatePaiement()).equals(ym)).mapToDouble(Paiement::getMontant).sum();
            series.getData().add(new XYChart.Data<>(label, sum));
        }
        revenueBarChart.getData().add(series);
    }

    @FXML
    private void handleDashboard(ActionEvent event) {
        // For now, just scroll to top or show a message
        // In a real app, you might use a ScrollPane and scroll to the dashboard section
        Alert alert = new Alert(Alert.AlertType.INFORMATION, "Vous êtes déjà sur le tableau de bord.");
        alert.showAndWait();
    }

    @FXML
    private void handleReservations(ActionEvent event) {
        // For now, just show a message
        Alert alert = new Alert(Alert.AlertType.INFORMATION, "Faites défiler vers la section Réservations.");
        alert.showAndWait();
    }

    @FXML
    private void handlePaiements(ActionEvent event) {
        // For now, just show a message
        Alert alert = new Alert(Alert.AlertType.INFORMATION, "Faites défiler vers la section Paiements.");
        alert.showAndWait();
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/agent_login.fxml"));
            Parent root = loader.load();
            stage.setScene(new Scene(root, 500, 800));
            stage.setTitle("Connexion Agent");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleExportReservations() {
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("Exporter les réservations");
        fileChooser.getExtensionFilters().add(new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        java.io.File file = fileChooser.showSaveDialog(reservationTable.getScene().getWindow());
        if (file != null) {
            try {
                String[] headers = {"ID", "Client", "Vehicule", "DateDebut", "DateFinPrevue", "Statut"};
                util.ExportUtil.exportToCSV(reservationList, headers, l -> new String[] {
                    String.valueOf(l.getId()),
                    l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "",
                    l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "",
                    l.getDateDebut() != null ? l.getDateDebut().toString() : "",
                    l.getDateFinPrevue() != null ? l.getDateFinPrevue().toString() : "",
                    l.getDateFinReelle() == null ? "En cours" : "Terminée"
                }, file.getAbsolutePath());
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
                alert.setTitle("Export réussi");
                alert.setHeaderText(null);
                alert.setContentText("Réservations exportées avec succès vers :\n" + file.getAbsolutePath());
                alert.showAndWait();
            } catch (Exception e) {
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export : " + e.getMessage());
                alert.showAndWait();
            }
        }
    }

    @FXML
    private void handleExportPayments() {
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("Exporter les paiements");
        fileChooser.getExtensionFilters().add(new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        java.io.File file = fileChooser.showSaveDialog(paymentTable.getScene().getWindow());
        if (file != null) {
            try {
                String[] headers = {"ID", "Client", "Vehicule", "Montant", "DatePaiement"};
                util.ExportUtil.exportToCSV(paymentList, headers, p -> new String[] {
                    String.valueOf(p.getId()),
                    p.getLocation() != null && p.getLocation().getClient() != null ? p.getLocation().getClient().getNom() + " " + p.getLocation().getClient().getPrenom() : "",
                    p.getLocation() != null && p.getLocation().getVehicule() != null ? p.getLocation().getVehicule().getMarque() + " " + p.getLocation().getVehicule().getModele() : "",
                    String.valueOf(p.getMontant()),
                    p.getDatePaiement() != null ? p.getDatePaiement().toString() : ""
                }, file.getAbsolutePath());
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
                alert.setTitle("Export réussi");
                alert.setHeaderText(null);
                alert.setContentText("Paiements exportés avec succès vers :\n" + file.getAbsolutePath());
                alert.showAndWait();
            } catch (Exception e) {
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export : " + e.getMessage());
                alert.showAndWait();
            }
        }
    }
} 