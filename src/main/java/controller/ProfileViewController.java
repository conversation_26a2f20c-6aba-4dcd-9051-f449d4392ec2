package controller;

import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import model.User;
import service.NotificationService;
import dao.UserDAO;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ResourceBundle;

public class ProfileViewController implements Initializable {

    @FXML private Label lblUsername;
    @FXML private Label lblRole;
    @FXML private Label lblFirstName;
    @FXML private Label lblLastName;
    @FXML private Label lblEmail;
    @FXML private Label lblPhone;
    @FXML private Label lblStatus;
    @FXML private Label lblLastLogin;

    @FXML private Label lblAccountAge;
    @FXML private Label lblTotalLogins;
    @FXML private Label lblNotificationsReceived;
    @FXML private Label lblActionsPerformed;

    @FXML private Label lblTwoFactorStatus;
    @FXML private Button btnToggleTwoFactor;
    @FXML private Button btnChangePassword;

    @FXML private VBox activityContainer;

    @FXML private ComboBox<String> cmbLanguage;
    @FXML private ComboBox<String> cmbTheme;
    @FXML private CheckBox chkAutoSave;
    @FXML private CheckBox chkShowTips;

    @FXML private Button btnEditProfile;
    @FXML private Button btnAccountSettings;
    @FXML private Button btnSavePreferences;
    @FXML private Button btnExportData;
    @FXML private Button btnDeleteAccount;
    @FXML private Button btnViewAllActivity;

    private User currentUser;
    private UserDAO userDAO;
    private NotificationService notificationService;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currentUser = (User) LoginController.loggedInUser;
        userDAO = new UserDAO();
        notificationService = NotificationService.getInstance();

        setupComboBoxes();
        loadUserProfile();
        loadStatistics();
        loadRecentActivity();
    }

    private void setupComboBoxes() {
        cmbLanguage.setItems(FXCollections.observableArrayList(
            "Français", "English", "العربية", "Español"
        ));
        cmbLanguage.setValue("Français");

        cmbTheme.setItems(FXCollections.observableArrayList(
            "Clair", "Sombre", "Automatique"
        ));
        cmbTheme.setValue("Clair");
    }

    private void loadUserProfile() {
        if (currentUser == null) return;

        lblUsername.setText(currentUser.getUsername() != null ? currentUser.getUsername() : "-");
        lblRole.setText(currentUser.getRole() != null ? currentUser.getRole().toUpperCase() : "-");
        lblFirstName.setText(currentUser.getFirstName() != null ? currentUser.getFirstName() : "-");
        lblLastName.setText(currentUser.getLastName() != null ? currentUser.getLastName() : "-");
        lblEmail.setText(currentUser.getEmail() != null ? currentUser.getEmail() : "-");
        lblPhone.setText(currentUser.getPhone() != null ? currentUser.getPhone() : "-");
        lblStatus.setText(currentUser.getStatus() != null ? currentUser.getStatus() : "-");

        if (currentUser.getLastLogin() != null) {
            lblLastLogin.setText(currentUser.getLastLogin().format(
                DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
            ));
        } else {
            lblLastLogin.setText("Jamais connecté");
        }

        // Update role badge color
        if ("admin".equalsIgnoreCase(currentUser.getRole())) {
            lblRole.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;");
        } else {
            lblRole.setStyle("-fx-background-color: #667eea; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;");
        }

        // Update status badge color
        if ("ACTIF".equalsIgnoreCase(currentUser.getStatus())) {
            lblStatus.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;");
        } else {
            lblStatus.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 4px 12px; -fx-background-radius: 15px; -fx-font-size: 12px; -fx-font-weight: bold;");
        }
    }

    private void loadStatistics() {
        if (currentUser == null) return;

        // Calculate account age
        if (currentUser.getCreatedAt() != null) {
            long daysSinceCreation = ChronoUnit.DAYS.between(
                currentUser.getCreatedAt().toLocalDate(),
                LocalDateTime.now().toLocalDate()
            );
            lblAccountAge.setText(String.valueOf(daysSinceCreation));
        } else {
            lblAccountAge.setText("0");
        }

        // Mock data for other statistics (in real app, these would come from database)
        lblTotalLogins.setText("42");
        
        // Get actual notification count
        long notificationCount = notificationService.getNotificationsForUser(currentUser).size();
        lblNotificationsReceived.setText(String.valueOf(notificationCount));
        
        lblActionsPerformed.setText("156");
    }

    private void loadRecentActivity() {
        activityContainer.getChildren().clear();

        // Mock recent activity data
        String[] activities = {
            "Connexion au système - " + LocalDateTime.now().minusHours(2).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
            "Consultation des notifications - " + LocalDateTime.now().minusHours(4).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
            "Modification du profil - " + LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
            "Création d'une nouvelle location - " + LocalDateTime.now().minusDays(2).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
            "Consultation des rapports - " + LocalDateTime.now().minusDays(3).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"))
        };

        for (String activity : activities) {
            HBox activityItem = new HBox();
            activityItem.setStyle("-fx-padding: 10px; -fx-border-color: #e9ecef; -fx-border-width: 0 0 1px 0; -fx-spacing: 10px;");

            Label iconLabel = new Label("📋");
            iconLabel.setStyle("-fx-font-size: 16px;");

            Label activityLabel = new Label(activity);
            activityLabel.setStyle("-fx-text-fill: #495057; -fx-font-size: 13px;");

            activityItem.getChildren().addAll(iconLabel, activityLabel);
            activityContainer.getChildren().add(activityItem);
        }
    }

    @FXML
    private void handleEditProfile() {
        // Create edit profile dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Modifier le Profil");
        dialog.setHeaderText("Modifier vos informations personnelles");

        // Create form
        VBox form = new VBox(10);
        form.setStyle("-fx-padding: 20px;");

        TextField firstNameField = new TextField(currentUser.getFirstName());
        firstNameField.setPromptText("Prénom");
        
        TextField lastNameField = new TextField(currentUser.getLastName());
        lastNameField.setPromptText("Nom");
        
        TextField emailField = new TextField(currentUser.getEmail());
        emailField.setPromptText("Email");
        
        TextField phoneField = new TextField(currentUser.getPhone());
        phoneField.setPromptText("Téléphone");

        form.getChildren().addAll(
            new Label("Prénom:"), firstNameField,
            new Label("Nom:"), lastNameField,
            new Label("Email:"), emailField,
            new Label("Téléphone:"), phoneField
        );

        dialog.getDialogPane().setContent(form);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // Update user information
                currentUser.setFirstName(firstNameField.getText());
                currentUser.setLastName(lastNameField.getText());
                currentUser.setEmail(emailField.getText());
                currentUser.setPhone(phoneField.getText());

                try {
                    userDAO.save(currentUser);
                    loadUserProfile();
                    FXMLUtil.showSuccess("Profil mis à jour avec succès!");
                } catch (Exception e) {
                    FXMLUtil.showError("Erreur lors de la mise à jour du profil: " + e.getMessage());
                }
            }
        });
    }

    @FXML
    private void handleAccountSettings() {
        // Navigate to account settings page
        try {
            FXMLUtil.createMaximizedWindow("/view/account_settings.fxml", 
                                        "Paramètres du Compte", 
                                        getClass());
        } catch (Exception e) {
            FXMLUtil.showError("Erreur lors de l'ouverture des paramètres: " + e.getMessage());
        }
    }

    @FXML
    private void handleChangePassword() {
        // Create change password dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Changer le Mot de Passe");
        dialog.setHeaderText("Modifier votre mot de passe");

        VBox form = new VBox(10);
        form.setStyle("-fx-padding: 20px;");

        PasswordField currentPasswordField = new PasswordField();
        currentPasswordField.setPromptText("Mot de passe actuel");
        
        PasswordField newPasswordField = new PasswordField();
        newPasswordField.setPromptText("Nouveau mot de passe");
        
        PasswordField confirmPasswordField = new PasswordField();
        confirmPasswordField.setPromptText("Confirmer le nouveau mot de passe");

        form.getChildren().addAll(
            new Label("Mot de passe actuel:"), currentPasswordField,
            new Label("Nouveau mot de passe:"), newPasswordField,
            new Label("Confirmer:"), confirmPasswordField
        );

        dialog.getDialogPane().setContent(form);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                String currentPassword = currentPasswordField.getText();
                String newPassword = newPasswordField.getText();
                String confirmPassword = confirmPasswordField.getText();

                if (currentPassword.isEmpty() || newPassword.isEmpty() || confirmPassword.isEmpty()) {
                    FXMLUtil.showWarning("Veuillez remplir tous les champs.");
                    return;
                }

                if (!newPassword.equals(confirmPassword)) {
                    FXMLUtil.showWarning("Les nouveaux mots de passe ne correspondent pas.");
                    return;
                }

                if (newPassword.length() < 6) {
                    FXMLUtil.showWarning("Le nouveau mot de passe doit contenir au moins 6 caractères.");
                    return;
                }

                // Verify current password and update
                try {
                    if (org.mindrot.jbcrypt.BCrypt.checkpw(currentPassword, currentUser.getPasswordHash())) {
                        currentUser.setPasswordHash(org.mindrot.jbcrypt.BCrypt.hashpw(newPassword, org.mindrot.jbcrypt.BCrypt.gensalt()));
                        userDAO.save(currentUser);
                        FXMLUtil.showSuccess("Mot de passe modifié avec succès!");
                    } else {
                        FXMLUtil.showError("Mot de passe actuel incorrect.");
                    }
                } catch (Exception e) {
                    FXMLUtil.showError("Erreur lors de la modification du mot de passe: " + e.getMessage());
                }
            }
        });
    }

    @FXML
    private void handleToggleTwoFactor() {
        // Mock two-factor authentication toggle
        if ("Désactivée".equals(lblTwoFactorStatus.getText())) {
            lblTwoFactorStatus.setText("Activée");
            btnToggleTwoFactor.setText("Désactiver");
            FXMLUtil.showSuccess("Authentification à deux facteurs activée!");
        } else {
            lblTwoFactorStatus.setText("Désactivée");
            btnToggleTwoFactor.setText("Activer");
            FXMLUtil.showSuccess("Authentification à deux facteurs désactivée!");
        }
    }

    @FXML
    private void handleViewAllActivity() {

    }

    @FXML
    private void handleSavePreferences() {
        // Save user preferences
        FXMLUtil.showSuccess("Préférences sauvegardées avec succès!");
    }

    @FXML
    private void handleExportData() {
      ;
    }

    @FXML
    private void handleDeleteAccount() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Supprimer le Compte");
        alert.setHeaderText("Êtes-vous sûr de vouloir supprimer votre compte ?");
        alert.setContentText("Cette action est irréversible. Toutes vos données seront perdues.");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                FXMLUtil.showWarning("Fonctionnalité de suppression de compte désactivée pour des raisons de sécurité.");
            }
        });
    }
}
