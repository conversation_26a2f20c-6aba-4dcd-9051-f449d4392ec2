package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.beans.property.SimpleStringProperty;
import javafx.stage.Stage;
import javafx.stage.FileChooser;
import javafx.application.Platform;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.fxml.FXMLLoader;

import dao.LocationDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.PaiementDAO;
import model.Location;
import model.Client;
import model.Vehicule;
import model.Paiement;

import java.util.List;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.IOException;

public class HistoriqueLocationsController {

    // Filter Controls
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterVehicleCombo;
    @FXML private ComboBox<String> filterClientCombo;
    @FXML private DatePicker filterDateFrom;
    @FXML private DatePicker filterDateTo;
    @FXML private TextField filterPriceMin;
    @FXML private TextField filterPriceMax;
    @FXML private ComboBox<String> filterDurationCombo;
    @FXML private ComboBox<String> filterPaymentCombo;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterProfitableOnly;

    // Statistics Labels
    @FXML private Label lblTotalLocations;
    @FXML private Label lblLocationGrowth;
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblRevenueGrowth;
    @FXML private Label lblActiveLocations;
    @FXML private Label lblActiveRevenue;
    @FXML private Label lblAvgDuration;
    @FXML private Label lblAvgPrice;

    // Charts and Analytics
    @FXML private VBox topVehiclesChart;
    @FXML private VBox topClientsChart;
    @FXML private VBox trendsChart;

    // Table and Search
    @FXML private TextField searchField;
    @FXML private TableView<Location> locationsTable;
    @FXML private TableColumn<Location, String> colSelect;
    @FXML private TableColumn<Location, String> colId;
    @FXML private TableColumn<Location, String> colClient;
    @FXML private TableColumn<Location, String> colVehicle;
    @FXML private TableColumn<Location, String> colStartDate;
    @FXML private TableColumn<Location, String> colEndDate;
    @FXML private TableColumn<Location, String> colActualEnd;
    @FXML private TableColumn<Location, String> colDuration;
    @FXML private TableColumn<Location, String> colStatus;
    @FXML private TableColumn<Location, String> colTotal;
    @FXML private TableColumn<Location, String> colPayment;
    @FXML private TableColumn<Location, String> colProfit;
    @FXML private TableColumn<Location, String> colActions;

    // Sidebar Details
    @FXML private VBox locationDetailCard;
    @FXML private Label lblSelectedLocation;
    @FXML private VBox locationDetails;
    @FXML private VBox locationTimeline;

    // Bottom Statistics
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredRevenue;
    @FXML private Label lblProfitMargin;
    @FXML private Label lblLastUpdate;

    // DAOs
    private LocationDAO locationDAO;
    private ClientDAO clientDAO;
    private VehiculeDAO vehiculeDAO;
    private PaiementDAO paiementDAO;

    // Data
    private ObservableList<Location> allLocations;
    private ObservableList<Location> filteredLocations;
    private Location selectedLocation;

    @FXML
    private void initialize() {
        try {
            // Initialize DAOs
            locationDAO = new LocationDAO();
            clientDAO = new ClientDAO();
            vehiculeDAO = new VehiculeDAO();
            paiementDAO = new PaiementDAO();

            // Initialize data
            allLocations = FXCollections.observableArrayList();
            filteredLocations = FXCollections.observableArrayList();

            // Setup table columns
            setupTableColumns();

            // Setup filters
            setupFilters();

            // Load data
            loadData();

            // Setup search
            setupSearch();

            // Update statistics
            updateStatistics();

            // Update last update time
            updateLastUpdateTime();

        } catch (Exception e) {
            System.err.println("Error initializing HistoriqueLocationsController: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur d'initialisation", "Erreur lors de l'initialisation: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void setupTableColumns() {
        // Select column with checkboxes
        if (colSelect != null) {
            colSelect.setCellValueFactory(cellData -> new SimpleStringProperty(""));
            colSelect.setCellFactory(column -> new TableCell<Location, String>() {
                private final CheckBox checkBox = new CheckBox();

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        setGraphic(checkBox);
                        checkBox.setOnAction(e -> updateSelectedCount());
                    }
                }
            });
        }

        // ID column
        if (colId != null) {
            colId.setCellValueFactory(cellData ->
                new SimpleStringProperty(String.valueOf(cellData.getValue().getId()))
            );
        }

        // Client column
        if (colClient != null) {
            colClient.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getClient() != null ?
                    cellData.getValue().getClient().getNom() + " " + cellData.getValue().getClient().getPrenom() :
                    "N/A"
                )
            );
        }

        // Vehicle column
        if (colVehicle != null) {
            colVehicle.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getVehicule() != null ?
                    cellData.getValue().getVehicule().getMarque() + " " + cellData.getValue().getVehicule().getModele() :
                    "N/A"
                )
            );
        }

        // Start date column
        if (colStartDate != null) {
            colStartDate.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getDateDebut() != null ?
                    cellData.getValue().getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) :
                    "N/A"
                )
            );
        }

        // End date column
        if (colEndDate != null) {
            colEndDate.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getDateFin() != null ?
                    cellData.getValue().getDateFin().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) :
                    "N/A"
                )
            );
        }

        // Actual end column
        if (colActualEnd != null) {
            colActualEnd.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getDateRetourEffective() != null ?
                    cellData.getValue().getDateRetourEffective().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) :
                    "En cours"
                )
            );
        }

        // Duration column
        if (colDuration != null) {
            colDuration.setCellValueFactory(cellData -> {
                Location location = cellData.getValue();
                if (location.getDateDebut() != null && location.getDateFin() != null) {
                    long days = java.time.temporal.ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFin());
                    return new SimpleStringProperty(days + "j");
                }
                return new SimpleStringProperty("N/A");
            });
        }

        // Status column
        if (colStatus != null) {
            colStatus.setCellValueFactory(cellData ->
                new SimpleStringProperty(
                    cellData.getValue().getStatus() != null ?
                    cellData.getValue().getStatus().toString() : "N/A"
                )
            );
        }

        // Total column
        if (colTotal != null) {
            colTotal.setCellValueFactory(cellData ->
                new SimpleStringProperty(String.format("%.2f DH", cellData.getValue().getPrixTotal()))
            );
        }

        // Payment column
        if (colPayment != null) {
            colPayment.setCellValueFactory(cellData -> {
                // Get payment status for this location
                try {
                    List<Paiement> payments = paiementDAO.findByLocationId(cellData.getValue().getId());
                    if (payments.isEmpty()) {
                        return new SimpleStringProperty("Non payé");
                    }
                    double totalPaid = payments.stream().mapToDouble(Paiement::getMontant).sum();
                    double totalDue = cellData.getValue().getPrixTotal();
                    if (totalPaid >= totalDue) {
                        return new SimpleStringProperty("Payé");
                    } else if (totalPaid > 0) {
                        return new SimpleStringProperty("Partiel");
                    } else {
                        return new SimpleStringProperty("Non payé");
                    }
                } catch (Exception e) {
                    return new SimpleStringProperty("Erreur");
                }
            });
        }

        // Profit column
        if (colProfit != null) {
            colProfit.setCellValueFactory(cellData -> {
                // Calculate profit (simplified - revenue minus estimated costs)
                double revenue = cellData.getValue().getPrixTotal();
                double estimatedCosts = revenue * 0.3; // Assume 30% costs
                double profit = revenue - estimatedCosts;
                return new SimpleStringProperty(String.format("%.0f DH", profit));
            });
        }

        // Actions column
        if (colActions != null) {
            colActions.setCellValueFactory(cellData -> new SimpleStringProperty(""));
            colActions.setCellFactory(column -> new TableCell<Location, String>() {
                private final HBox actionBox = new HBox(5);
                private final Button viewBtn = new Button("👁️");
                private final Button editBtn = new Button("✏️");
                private final Button contractBtn = new Button("📋");

                {
                    viewBtn.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");
                    editBtn.setStyle("-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");
                    contractBtn.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 10px;");

                    actionBox.getChildren().addAll(viewBtn, editBtn, contractBtn);
                    actionBox.setAlignment(Pos.CENTER);
                }

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        Location location = getTableView().getItems().get(getIndex());
                        viewBtn.setOnAction(e -> showLocationDetails(location));
                        editBtn.setOnAction(e -> handleEditLocation(location));
                        contractBtn.setOnAction(e -> handleViewContract(location));
                        setGraphic(actionBox);
                    }
                }
            });
        }

        // Set table selection listener
        if (locationsTable != null) {
            locationsTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    showLocationDetails(newSelection);
                }
            });
        }
    }

    private void setupFilters() {
        // Status filter
        if (filterStatusCombo != null) {
            filterStatusCombo.setItems(FXCollections.observableArrayList(
                "Tous", "EN_COURS", "TERMINE", "ANNULE", "EN_RETARD"
            ));
            filterStatusCombo.setValue("Tous");
            filterStatusCombo.setOnAction(e -> applyFilters());
        }

        // Duration filter
        if (filterDurationCombo != null) {
            filterDurationCombo.setItems(FXCollections.observableArrayList(
                "Toutes", "Court terme (1-7j)", "Moyen terme (8-30j)", "Long terme (30j+)"
            ));
            filterDurationCombo.setValue("Toutes");
            filterDurationCombo.setOnAction(e -> applyFilters());
        }

        // Payment filter
        if (filterPaymentCombo != null) {
            filterPaymentCombo.setItems(FXCollections.observableArrayList(
                "Tous", "Payé", "Non payé", "Partiel", "En retard"
            ));
            filterPaymentCombo.setValue("Tous");
            filterPaymentCombo.setOnAction(e -> applyFilters());
        }

        // Populate vehicle and client filters
        populateVehicleFilter();
        populateClientFilter();

        // Add listeners for real-time filtering
        if (filterVehicleCombo != null) filterVehicleCombo.setOnAction(e -> applyFilters());
        if (filterClientCombo != null) filterClientCombo.setOnAction(e -> applyFilters());
        if (filterDateFrom != null) filterDateFrom.setOnAction(e -> applyFilters());
        if (filterDateTo != null) filterDateTo.setOnAction(e -> applyFilters());
        if (filterOverdueOnly != null) filterOverdueOnly.setOnAction(e -> applyFilters());
        if (filterProfitableOnly != null) filterProfitableOnly.setOnAction(e -> applyFilters());

        // Add listeners for price filters
        if (filterPriceMin != null) filterPriceMin.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        if (filterPriceMax != null) filterPriceMax.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void populateVehicleFilter() {
        try {
            if (vehiculeDAO != null && filterVehicleCombo != null) {
                List<Vehicule> vehicles = vehiculeDAO.findAll();
                List<String> vehicleNames = vehicles.stream()
                    .map(v -> v.getMarque() + " " + v.getModele())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                vehicleNames.add(0, "Tous");
                filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicleNames));
                filterVehicleCombo.setValue("Tous");
            }
        } catch (Exception e) {
            System.err.println("Error populating vehicle filter: " + e.getMessage());
        }
    }

    private void populateClientFilter() {
        try {
            if (clientDAO != null && filterClientCombo != null) {
                List<Client> clients = clientDAO.findAll();
                List<String> clientNames = clients.stream()
                    .map(c -> c.getNom() + " " + c.getPrenom())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                clientNames.add(0, "Tous");
                filterClientCombo.setItems(FXCollections.observableArrayList(clientNames));
                filterClientCombo.setValue("Tous");
            }
        } catch (Exception e) {
            System.err.println("Error populating client filter: " + e.getMessage());
        }
    }

    private void setupSearch() {
        if (searchField != null) {
            searchField.textProperty().addListener((observable, oldValue, newValue) -> {
                applyFilters();
            });
        }
    }

    private void loadData() {
        try {
            if (locationDAO != null) {
                List<Location> locations = locationDAO.findAll();
                if (allLocations != null) {
                    allLocations.setAll(locations);
                }
                if (filteredLocations != null) {
                    filteredLocations.setAll(locations);
                }
                if (locationsTable != null) {
                    locationsTable.setItems(filteredLocations);
                }

                updateStatistics();
                updateCharts();
            }

        } catch (Exception e) {
            System.err.println("Error loading locations: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur de chargement", "Erreur lors du chargement des données: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void applyFilters() {
        try {
            if (allLocations != null && filteredLocations != null) {
                List<Location> filtered = allLocations.stream()
                    .filter(this::matchesFilters)
                    .collect(Collectors.toList());

                filteredLocations.setAll(filtered);
                updateStatistics();
                updateFilteredCount();
            }

        } catch (Exception e) {
            System.err.println("Error applying filters: " + e.getMessage());
        }
    }

    private boolean matchesFilters(Location location) {
        // Search filter
        if (searchField != null) {
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                String clientName = location.getClient() != null ?
                    (location.getClient().getNom() + " " + location.getClient().getPrenom()).toLowerCase() : "";
                String vehicleName = location.getVehicule() != null ?
                    (location.getVehicule().getMarque() + " " + location.getVehicule().getModele()).toLowerCase() : "";

                if (!clientName.contains(searchLower) && !vehicleName.contains(searchLower) &&
                    !String.valueOf(location.getId()).contains(searchLower)) {
                    return false;
                }
            }
        }

        // Status filter
        if (filterStatusCombo != null && filterStatusCombo.getValue() != null && !filterStatusCombo.getValue().equals("Tous")) {
            if (location.getStatus() == null || !location.getStatus().toString().equals(filterStatusCombo.getValue())) {
                return false;
            }
        }

        // Vehicle filter
        if (filterVehicleCombo != null && filterVehicleCombo.getValue() != null && !filterVehicleCombo.getValue().equals("Tous")) {
            String vehicleName = location.getVehicule() != null ?
                location.getVehicule().getMarque() + " " + location.getVehicule().getModele() : "";
            if (!vehicleName.equals(filterVehicleCombo.getValue())) {
                return false;
            }
        }

        // Client filter
        if (filterClientCombo != null && filterClientCombo.getValue() != null && !filterClientCombo.getValue().equals("Tous")) {
            String clientName = location.getClient() != null ?
                location.getClient().getNom() + " " + location.getClient().getPrenom() : "";
            if (!clientName.equals(filterClientCombo.getValue())) {
                return false;
            }
        }

        // Date filters
        if (filterDateFrom != null && filterDateFrom.getValue() != null) {
            if (location.getDateDebut() == null || location.getDateDebut().isBefore(filterDateFrom.getValue())) {
                return false;
            }
        }
        if (filterDateTo != null && filterDateTo.getValue() != null) {
            if (location.getDateFin() == null || location.getDateFin().isAfter(filterDateTo.getValue())) {
                return false;
            }
        }

        // Price filters
        try {
            if (filterPriceMin != null && filterPriceMin.getText() != null && !filterPriceMin.getText().trim().isEmpty()) {
                double minPrice = Double.parseDouble(filterPriceMin.getText().trim());
                if (location.getPrixTotal() < minPrice) {
                    return false;
                }
            }
            if (filterPriceMax != null && filterPriceMax.getText() != null && !filterPriceMax.getText().trim().isEmpty()) {
                double maxPrice = Double.parseDouble(filterPriceMax.getText().trim());
                if (location.getPrixTotal() > maxPrice) {
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            // Ignore invalid price inputs
        }

        // Duration filter
        if (filterDurationCombo != null && filterDurationCombo.getValue() != null && !filterDurationCombo.getValue().equals("Toutes")) {
            if (location.getDateDebut() != null && location.getDateFin() != null) {
                long days = java.time.temporal.ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFin());
                String durationFilter = filterDurationCombo.getValue();

                if (durationFilter.equals("Court terme (1-7j)") && (days < 1 || days > 7)) {
                    return false;
                } else if (durationFilter.equals("Moyen terme (8-30j)") && (days < 8 || days > 30)) {
                    return false;
                } else if (durationFilter.equals("Long terme (30j+)") && days <= 30) {
                    return false;
                }
            }
        }

        // Overdue filter
        if (filterOverdueOnly != null && filterOverdueOnly.isSelected()) {
            if (location.getDateFin() == null || !location.getDateFin().isBefore(LocalDate.now()) ||
                location.getStatus() == Location.Status.TERMINE) {
                return false;
            }
        }

        // Profitable filter
        if (filterProfitableOnly != null && filterProfitableOnly.isSelected()) {
            double revenue = location.getPrixTotal();
            double estimatedCosts = revenue * 0.3; // Assume 30% costs
            if (revenue <= estimatedCosts) {
                return false;
            }
        }

        return true;
    }

    private void updateStatistics() {
        try {
            // Total locations
            int totalLocations = filteredLocations.size();
            lblTotalLocations.setText(String.valueOf(totalLocations));

            // Total revenue
            double totalRevenue = filteredLocations.stream()
                .mapToDouble(Location::getPrixTotal)
                .sum();
            lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));

            // Active locations
            long activeLocations = filteredLocations.stream()
                .filter(l -> Location.Status.EN_COURS.equals(l.getStatus()))
                .count();
            lblActiveLocations.setText(String.valueOf(activeLocations));

            // Active revenue
            double activeRevenue = filteredLocations.stream()
                .filter(l -> Location.Status.EN_COURS.equals(l.getStatus()))
                .mapToDouble(Location::getPrixTotal)
                .sum();
            lblActiveRevenue.setText(String.format("%.2f DH en cours", activeRevenue));

            // Average duration
            double avgDuration = filteredLocations.stream()
                .filter(l -> l.getDateDebut() != null && l.getDateFin() != null)
                .mapToLong(l -> java.time.temporal.ChronoUnit.DAYS.between(l.getDateDebut(), l.getDateFin()))
                .average()
                .orElse(0.0);
            lblAvgDuration.setText(String.format("%.0fj", avgDuration));

            // Average price per day
            double avgPricePerDay = avgDuration > 0 ? totalRevenue / (totalLocations * avgDuration) : 0;
            lblAvgPrice.setText(String.format("%.2f DH/jour moy.", avgPricePerDay));

            // Update growth indicators (simplified)
            lblLocationGrowth.setText("↗ +12% ce mois");
            lblRevenueGrowth.setText("↗ +8% ce mois");

            // Update bottom statistics
            updateFilteredCount();

        } catch (Exception e) {
            System.err.println("Error updating statistics: " + e.getMessage());
        }
    }

    private void updateCharts() {
        updateTopVehiclesChart();
        updateTopClientsChart();
        updateTrendsChart();
    }

    private void updateTopVehiclesChart() {
        try {
            topVehiclesChart.getChildren().clear();

            // Group locations by vehicle and count
            var vehicleStats = filteredLocations.stream()
                .filter(l -> l.getVehicule() != null)
                .collect(Collectors.groupingBy(
                    l -> l.getVehicule().getMarque() + " " + l.getVehicule().getModele(),
                    Collectors.counting()
                ));

            // Get top 5 vehicles
            vehicleStats.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .limit(5)
                .forEach(entry -> {
                    HBox row = new HBox(10);
                    row.setAlignment(Pos.CENTER_LEFT);

                    Label nameLabel = new Label(entry.getKey());
                    nameLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                    Label countLabel = new Label(entry.getValue() + " locations");
                    countLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6b7280; -fx-font-weight: bold;");

                    row.getChildren().addAll(nameLabel, countLabel);
                    topVehiclesChart.getChildren().add(row);
                });

        } catch (Exception e) {
            System.err.println("Error updating top vehicles chart: " + e.getMessage());
        }
    }

    private void updateTopClientsChart() {
        try {
            topClientsChart.getChildren().clear();

            // Group locations by client and calculate revenue
            var clientStats = filteredLocations.stream()
                .filter(l -> l.getClient() != null)
                .collect(Collectors.groupingBy(
                    l -> l.getClient().getNom() + " " + l.getClient().getPrenom(),
                    Collectors.summingDouble(Location::getPrixTotal)
                ));

            // Get top 5 clients
            clientStats.entrySet().stream()
                .sorted((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()))
                .limit(5)
                .forEach(entry -> {
                    HBox row = new HBox(10);
                    row.setAlignment(Pos.CENTER_LEFT);

                    Label nameLabel = new Label(entry.getKey());
                    nameLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                    Label revenueLabel = new Label(String.format("%.0f DH", entry.getValue()));
                    revenueLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #059669; -fx-font-weight: bold;");

                    row.getChildren().addAll(nameLabel, revenueLabel);
                    topClientsChart.getChildren().add(row);
                });

        } catch (Exception e) {
            System.err.println("Error updating top clients chart: " + e.getMessage());
        }
    }

    private void updateTrendsChart() {
        try {
            trendsChart.getChildren().clear();

            // Simple trend indicators
            HBox trendRow1 = new HBox(10);
            trendRow1.setAlignment(Pos.CENTER_LEFT);
            Label trend1 = new Label("📈 Locations en hausse");
            trend1.setStyle("-fx-font-size: 12px; -fx-text-fill: #059669;");
            trendRow1.getChildren().add(trend1);

            HBox trendRow2 = new HBox(10);
            trendRow2.setAlignment(Pos.CENTER_LEFT);
            Label trend2 = new Label("💰 Revenus stables");
            trend2.setStyle("-fx-font-size: 12px; -fx-text-fill: #3b82f6;");
            trendRow2.getChildren().add(trend2);

            HBox trendRow3 = new HBox(10);
            trendRow3.setAlignment(Pos.CENTER_LEFT);
            Label trend3 = new Label("⏱️ Durée moyenne: " + lblAvgDuration.getText());
            trend3.setStyle("-fx-font-size: 12px; -fx-text-fill: #6b7280;");
            trendRow3.getChildren().add(trend3);

            trendsChart.getChildren().addAll(trendRow1, trendRow2, trendRow3);

        } catch (Exception e) {
            System.err.println("Error updating trends chart: " + e.getMessage());
        }
    }

    private void updateFilteredCount() {
        lblTotalCount.setText("Total: " + filteredLocations.size() + " locations");

        double filteredRevenue = filteredLocations.stream()
            .mapToDouble(Location::getPrixTotal)
            .sum();
        lblFilteredRevenue.setText(String.format("Revenus filtrés: %.2f DH", filteredRevenue));

        // Calculate profit margin
        double totalRevenue = filteredRevenue;
        double estimatedCosts = totalRevenue * 0.3; // Assume 30% costs
        double profitMargin = totalRevenue > 0 ? ((totalRevenue - estimatedCosts) / totalRevenue) * 100 : 0;
        lblProfitMargin.setText(String.format("Marge: %.1f%%", profitMargin));
    }

    private void updateSelectedCount() {
        // Count selected checkboxes
        long selectedCount = locationsTable.getItems().stream()
            .mapToLong(location -> {
                // This is a simplified count - in real implementation,
                // you'd track checkbox states
                return 0;
            })
            .sum();
        lblSelectedCount.setText("Sélectionnées: " + selectedCount);
    }

    private void updateLastUpdateTime() {
        Platform.runLater(() -> {
            String currentTime = java.time.LocalTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("HH:mm")
            );
            lblLastUpdate.setText("Dernière mise à jour: " + currentTime);
        });
    }

    private void showLocationDetails(Location location) {
        selectedLocation = location;

        if (location == null) {
            lblSelectedLocation.setText("Sélectionnez une location");
            locationDetails.getChildren().clear();
            locationTimeline.getChildren().clear();
            return;
        }

        // Update selected location label
        String clientName = location.getClient() != null ?
            location.getClient().getNom() + " " + location.getClient().getPrenom() : "Client inconnu";
        String vehicleName = location.getVehicule() != null ?
            location.getVehicule().getMarque() + " " + location.getVehicule().getModele() : "Véhicule inconnu";

        lblSelectedLocation.setText("Location #" + location.getId() + " - " + clientName);

        // Update location details
        locationDetails.getChildren().clear();

        addDetailRow("👤 Client:", clientName);
        addDetailRow("🚗 Véhicule:", vehicleName);
        addDetailRow("📅 Date début:", location.getDateDebut() != null ?
            location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        addDetailRow("📅 Date fin prévue:", location.getDateFin() != null ?
            location.getDateFin().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        addDetailRow("🏁 Date retour réelle:", location.getDateRetourEffective() != null ?
            location.getDateRetourEffective().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "En cours");
        addDetailRow("📊 Statut:", location.getStatus() != null ? location.getStatus().toString() : "N/A");
        addDetailRow("💰 Prix total:", String.format("%.2f DH", location.getPrixTotal()));

        // Update timeline
        updateLocationTimeline(location);
    }

    private void addDetailRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);

        Label labelNode = new Label(label);
        labelNode.setStyle("-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-weight: bold; -fx-min-width: 120;");

        Label valueNode = new Label(value);
        valueNode.setStyle("-fx-font-size: 12px; -fx-text-fill: #1f2937;");

        row.getChildren().addAll(labelNode, valueNode);
        locationDetails.getChildren().add(row);
    }

    private void updateLocationTimeline(Location location) {
        locationTimeline.getChildren().clear();

        // Add timeline events
        if (location.getDateDebut() != null) {
            addTimelineEvent("🚀 Location démarrée", location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        }

        if (location.getDateRetourEffective() != null) {
            addTimelineEvent("🏁 Véhicule retourné", location.getDateRetourEffective().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        } else if (location.getDateFin() != null) {
            addTimelineEvent("⏰ Retour prévu", location.getDateFin().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        }

        // Add payment events
        try {
            List<Paiement> payments = paiementDAO.findByLocationId(location.getId());
            for (Paiement payment : payments) {
                if (payment.getDatePaiement() != null) {
                    addTimelineEvent("💳 Paiement reçu",
                        payment.getDatePaiement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) +
                        " - " + String.format("%.2f DH", payment.getMontant()));
                }
            }
        } catch (Exception e) {
            System.err.println("Error loading payments for timeline: " + e.getMessage());
        }
    }

    private void addTimelineEvent(String event, String date) {
        VBox eventBox = new VBox(2);

        Label eventLabel = new Label(event);
        eventLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #1f2937; -fx-font-weight: bold;");

        Label dateLabel = new Label(date);
        dateLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #6b7280;");

        eventBox.getChildren().addAll(eventLabel, dateLabel);
        eventBox.setStyle("-fx-padding: 8; -fx-border-color: #e5e7eb; -fx-border-width: 0 0 1 0;");

        locationTimeline.getChildren().add(eventBox);
    }

    // FXML Action Handlers
    @FXML
    private void handleRefresh() {
        loadData();
        updateLastUpdateTime();
    }

    @FXML
    private void handleClearFilters() {
        filterStatusCombo.setValue("Tous");
        filterVehicleCombo.setValue("Tous");
        filterClientCombo.setValue("Tous");
        filterDateFrom.setValue(null);
        filterDateTo.setValue(null);
        filterPriceMin.clear();
        filterPriceMax.clear();
        filterDurationCombo.setValue("Toutes");
        filterPaymentCombo.setValue("Tous");
        filterOverdueOnly.setSelected(false);
        filterProfitableOnly.setSelected(false);
        searchField.clear();
        applyFilters();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handleBulkActions() {
        // Show bulk actions dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Actions Groupées");
        alert.setHeaderText("Fonctionnalité à venir");
        alert.setContentText("Les actions groupées seront disponibles dans une prochaine version.");
        alert.showAndWait();
    }

    // Export Methods
    @FXML
    private void handleExportCSV() {
        exportToCSV(filteredLocations, "historique_locations.csv");
    }

    @FXML
    private void handleExportExcel() {
        showAlert("Export Excel", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportPDF() {
        showAlert("Export PDF", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportActive() {
        List<Location> activeLocations = filteredLocations.stream()
            .filter(l -> Location.Status.EN_COURS.equals(l.getStatus()))
            .collect(Collectors.toList());
        exportToCSV(activeLocations, "locations_actives.csv");
    }

    @FXML
    private void handleExportCompleted() {
        List<Location> completedLocations = filteredLocations.stream()
            .filter(l -> Location.Status.TERMINE.equals(l.getStatus()))
            .collect(Collectors.toList());
        exportToCSV(completedLocations, "locations_terminees.csv");
    }

    @FXML
    private void handleExportProfitability() {
        showAlert("Export Rentabilité", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    // Quick Action Methods
    @FXML
    private void handleViewContract() {
        if (selectedLocation != null) {
            handleViewContract(selectedLocation);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner une location", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleEditLocation() {
        if (selectedLocation != null) {
            handleEditLocation(selectedLocation);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner une location", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleViewPayments() {
        if (selectedLocation != null) {
            try {
                // Open payment history filtered by this location
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/historique_paiements_enhanced.fxml"));
                Parent root = loader.load();

                Stage stage = new Stage();
                stage.setTitle("Paiements - Location #" + selectedLocation.getId());
                stage.setScene(new Scene(root, 1200, 800));
                stage.initModality(Modality.APPLICATION_MODAL);
                stage.show();

            } catch (Exception e) {
                System.err.println("Error opening payments: " + e.getMessage());
                showAlert("Erreur", "Erreur lors de l'ouverture des paiements: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner une location", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleContactClient() {
        if (selectedLocation != null && selectedLocation.getClient() != null) {
            Client client = selectedLocation.getClient();

            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Contact Client");
            alert.setHeaderText("Informations de contact");
            alert.setContentText(
                "Nom: " + client.getNom() + " " + client.getPrenom() + "\n" +
                "Téléphone: " + (client.getTelephone() != null ? client.getTelephone() : "N/A") + "\n" +
                "Email: " + (client.getEmail() != null ? client.getEmail() : "N/A")
            );
            alert.showAndWait();
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner une location avec un client", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleDuplicateLocation() {
        if (selectedLocation != null) {
            showAlert("Dupliquer Location", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
        } else {
            showAlert("Aucune sélection", "Veuillez sélectionner une location", Alert.AlertType.WARNING);
        }
    }

    @FXML
    private void handleNewLocation() {
        try {
            // Open location creation dialog
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location.fxml"));
            Parent root = loader.load();

            Stage stage = new Stage();
            stage.setTitle("Nouvelle Location");
            stage.setScene(new Scene(root, 1200, 800));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.show();

        } catch (Exception e) {
            System.err.println("Error opening new location: " + e.getMessage());
            showAlert("Erreur", "Erreur lors de l'ouverture de la création de location: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleAnalytics() {
        showAlert("Analyses Avancées", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    // Helper Methods for Actions
    private void handleViewContract(Location location) {
        try {
            // Create a simple contract view dialog
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Contrat de Location");
            alert.setHeaderText("Location #" + location.getId());

            String contractInfo =
                "Client: " + (location.getClient() != null ?
                    location.getClient().getNom() + " " + location.getClient().getPrenom() : "N/A") + "\n" +
                "Véhicule: " + (location.getVehicule() != null ?
                    location.getVehicule().getMarque() + " " + location.getVehicule().getModele() : "N/A") + "\n" +
                "Période: " + (location.getDateDebut() != null ?
                    location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A") +
                " au " + (location.getDateFin() != null ?
                    location.getDateFin().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A") + "\n" +
                "Prix total: " + String.format("%.2f DH", location.getPrixTotal()) + "\n" +
                "Statut: " + (location.getStatus() != null ? location.getStatus().toString() : "N/A");

            alert.setContentText(contractInfo);
            alert.showAndWait();

        } catch (Exception e) {
            System.err.println("Error viewing contract: " + e.getMessage());
            showAlert("Erreur", "Erreur lors de l'affichage du contrat: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void handleEditLocation(Location location) {
        showAlert("Modifier Location", "Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    // Export Utility Methods
    private void exportToCSV(List<Location> locations, String filename) {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les locations");
            fileChooser.setInitialFileName(filename);
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );

            File file = fileChooser.showSaveDialog(locationsTable.getScene().getWindow());
            if (file != null) {
                try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
                    // Write CSV header
                    writer.println("ID,Client,Véhicule,Date Début,Date Fin,Date Retour Réelle,Durée (jours),Statut,Prix Total,Statut Paiement");

                    // Write location data
                    for (Location location : locations) {
                        String clientName = location.getClient() != null ?
                            escapeCSV(location.getClient().getNom() + " " + location.getClient().getPrenom()) : "N/A";
                        String vehicleName = location.getVehicule() != null ?
                            escapeCSV(location.getVehicule().getMarque() + " " + location.getVehicule().getModele()) : "N/A";
                        String startDate = location.getDateDebut() != null ?
                            location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A";
                        String endDate = location.getDateFin() != null ?
                            location.getDateFin().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A";
                        String actualEndDate = location.getDateRetourEffective() != null ?
                            location.getDateRetourEffective().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "En cours";

                        long duration = 0;
                        if (location.getDateDebut() != null && location.getDateFin() != null) {
                            duration = java.time.temporal.ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFin());
                        }

                        String status = location.getStatus() != null ? location.getStatus().toString() : "N/A";

                        // Get payment status
                        String paymentStatus = "Non payé";
                        try {
                            List<Paiement> payments = paiementDAO.findByLocationId(location.getId());
                            if (!payments.isEmpty()) {
                                double totalPaid = payments.stream().mapToDouble(Paiement::getMontant).sum();
                                double totalDue = location.getPrixTotal();
                                if (totalPaid >= totalDue) {
                                    paymentStatus = "Payé";
                                } else if (totalPaid > 0) {
                                    paymentStatus = "Partiel";
                                }
                            }
                        } catch (Exception e) {
                            paymentStatus = "Erreur";
                        }

                        writer.printf("%d,%s,%s,%s,%s,%s,%d,%s,%.2f,%s%n",
                            location.getId(),
                            clientName,
                            vehicleName,
                            startDate,
                            endDate,
                            actualEndDate,
                            duration,
                            escapeCSV(status),
                            location.getPrixTotal(),
                            escapeCSV(paymentStatus)
                        );
                    }
                }

                showAlert("Export réussi", "Les données ont été exportées vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }

        } catch (IOException e) {
            System.err.println("Error exporting to CSV: " + e.getMessage());
            showAlert("Erreur d'export", "Erreur lors de l'export: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @FXML
    private void closeWindow() {
        Stage stage = (Stage) locationsTable.getScene().getWindow();
        stage.close();
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Platform.runLater(() -> {
            Alert alert = new Alert(type);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
}
