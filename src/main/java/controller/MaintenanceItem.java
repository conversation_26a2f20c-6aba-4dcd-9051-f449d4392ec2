package controller;

import model.Vehicule;
import model.VehicleMaintenance;
import model.VehicleFailure;
import dao.VehicleMaintenanceDAO;
import dao.VehicleFailureDAO;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.List;

/**
 * Data model class for vehicle maintenance table items
 */
public class MaintenanceItem {
    private final Vehicule vehicle;
    private final VehicleMaintenanceDAO maintenanceDAO;
    private final VehicleFailureDAO failureDAO;
    
    private final String vehicleInfo;
    private final String vehicleAge;
    private final int vehicleAgeYears;
    private final String maintenanceType;
    private final String lastMaintenanceDate;
    private final String nextMaintenanceDate;
    private final LocalDate nextMaintenanceDateAsLocalDate;
    private final String status;
    private final String daysOverdue;
    private final boolean isOverdue;
    private final boolean isUpcoming;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    
    public MaintenanceItem(Vehicule vehicle, VehicleMaintenanceDAO maintenanceDAO, VehicleFailureDAO failureDAO) {
        this.vehicle = vehicle;
        this.maintenanceDAO = maintenanceDAO;
        this.failureDAO = failureDAO;
        
        // Vehicle information
        this.vehicleInfo = buildVehicleInfo(vehicle);
        this.vehicleAgeYears = calculateVehicleAge(vehicle);
        this.vehicleAge = vehicleAgeYears + " ans" + (vehicleAgeYears >= 5 ? " ⚠️" : "");
        
        // Maintenance information
        VehicleMaintenance lastMaintenance = null;
        VehicleMaintenance nextMaintenance = null;

        try {
            lastMaintenance = maintenanceDAO.findLastMaintenanceForVehicle(vehicle);
            nextMaintenance = maintenanceDAO.findNextMaintenanceForVehicle(vehicle);
        } catch (Exception e) {
            System.err.println("Error loading maintenance data for vehicle " + vehicle.getId() + ": " + e.getMessage());
            // Continue with null values - will be handled below
        }
        
        if (lastMaintenance != null) {
            this.lastMaintenanceDate = lastMaintenance.getMaintenanceDate().format(DATE_FORMATTER);
            this.maintenanceType = lastMaintenance.getType().name();
        } else {
            this.lastMaintenanceDate = "Jamais";
            this.maintenanceType = "PERIODIQUE"; // Default for vehicles without maintenance
        }
        
        // Calculate next maintenance based on vehicle age and last maintenance
        if (nextMaintenance != null) {
            this.nextMaintenanceDate = nextMaintenance.getNextMaintenanceDate().format(DATE_FORMATTER);
            this.nextMaintenanceDateAsLocalDate = nextMaintenance.getNextMaintenanceDate();
        } else {
            // Calculate next maintenance date based on vehicle age
            LocalDate calculatedNext = calculateNextMaintenanceDate(vehicle, lastMaintenance);
            this.nextMaintenanceDate = calculatedNext.format(DATE_FORMATTER);
            this.nextMaintenanceDateAsLocalDate = calculatedNext;
        }
        
        // Status and overdue calculation
        LocalDate today = LocalDate.now();
        this.isOverdue = nextMaintenanceDateAsLocalDate.isBefore(today);
        this.isUpcoming = !isOverdue && nextMaintenanceDateAsLocalDate.isBefore(today.plusDays(30));
        
        if (isOverdue) {
            long daysOverdueCount = ChronoUnit.DAYS.between(nextMaintenanceDateAsLocalDate, today);
            this.status = "EN_RETARD";
            this.daysOverdue = String.valueOf(daysOverdueCount);
        } else if (isUpcoming) {
            this.status = "A_VENIR";
            this.daysOverdue = "0";
        } else {
            this.status = "PLANIFIE";
            this.daysOverdue = "0";
        }
    }
    
    private String buildVehicleInfo(Vehicule vehicle) {
        StringBuilder info = new StringBuilder();
        info.append(vehicle.getMarque() != null ? vehicle.getMarque() : "").append(" ");
        info.append(vehicle.getModele() != null ? vehicle.getModele() : "");
        info.append(" (").append(vehicle.getImmatriculation() != null ? vehicle.getImmatriculation() : "").append(")");
        return info.toString();
    }
    
    private int calculateVehicleAge(Vehicule vehicle) {
        if (vehicle.getDateAcquisition() == null) return 0;
        return LocalDate.now().getYear() - vehicle.getDateAcquisition().getYear();
    }
    
    private LocalDate calculateNextMaintenanceDate(Vehicule vehicle, VehicleMaintenance lastMaintenance) {
        LocalDate baseDate = lastMaintenance != null ? 
            lastMaintenance.getMaintenanceDate() : 
            (vehicle.getDateAcquisition() != null ? vehicle.getDateAcquisition() : LocalDate.now());
        
        int vehicleAge = calculateVehicleAge(vehicle);
        
        if (vehicleAge >= 5) {
            // Vehicles 5+ years old need maintenance every 6 months
            return baseDate.plusMonths(6);
        } else {
            // Newer vehicles need maintenance every 12 months
            return baseDate.plusMonths(12);
        }
    }
    
    public String getMaintenanceStatus() {
        return status;
    }
    
    public boolean hasRecentFailures() {
        try {
            List<VehicleFailure> recentFailures = failureDAO.findRecentFailuresForVehicle(vehicle, 30);
            return recentFailures != null && !recentFailures.isEmpty();
        } catch (Exception e) {
            System.err.println("Error checking recent failures: " + e.getMessage());
            return false;
        }
    }

    public boolean hasCriticalFailures() {
        try {
            List<VehicleFailure> failures = failureDAO.findByVehicle(vehicle);
            if (failures == null) return false;
            return failures.stream()
                .anyMatch(f -> f.getSeverity() == VehicleFailure.FailureSeverity.CRITIQUE && !f.isResolved());
        } catch (Exception e) {
            System.err.println("Error checking critical failures: " + e.getMessage());
            return false;
        }
    }

    public long getFailureCount() {
        try {
            return failureDAO.getFailureCountByVehicle(vehicle);
        } catch (Exception e) {
            System.err.println("Error getting failure count: " + e.getMessage());
            return 0;
        }
    }

    public double getTotalMaintenanceCost() {
        try {
            Double cost = maintenanceDAO.getTotalMaintenanceCostForVehicle(vehicle);
            return cost != null ? cost : 0.0;
        } catch (Exception e) {
            System.err.println("Error getting maintenance cost: " + e.getMessage());
            return 0.0;
        }
    }

    public double getTotalRepairCost() {
        try {
            Double cost = failureDAO.getTotalRepairCostForVehicle(vehicle);
            return cost != null ? cost : 0.0;
        } catch (Exception e) {
            System.err.println("Error getting repair cost: " + e.getMessage());
            return 0.0;
        }
    }
    
    public String getMaintenanceRecommendation() {
        StringBuilder recommendation = new StringBuilder();
        
        if (vehicleAgeYears >= 5) {
            recommendation.append("Suivi renforcé requis (véhicule +5 ans). ");
        }
        
        if (isOverdue) {
            recommendation.append("Maintenance urgente requise. ");
        } else if (isUpcoming) {
            recommendation.append("Maintenance à programmer prochainement. ");
        }
        
        if (hasCriticalFailures()) {
            recommendation.append("Pannes critiques non résolues. ");
        }
        
        if (hasRecentFailures()) {
            recommendation.append("Pannes récentes détectées. ");
        }
        
        if (recommendation.length() == 0) {
            recommendation.append("Véhicule en bon état.");
        }
        
        return recommendation.toString().trim();
    }
    
    // Getters for table columns
    public Vehicule getVehicle() { return vehicle; }
    public String getVehicleInfo() { return vehicleInfo; }
    public String getVehicleAge() { return vehicleAge; }
    public int getVehicleAgeYears() { return vehicleAgeYears; }
    public String getMaintenanceType() { return maintenanceType; }
    public String getLastMaintenanceDate() { return lastMaintenanceDate; }
    public String getNextMaintenanceDate() { return nextMaintenanceDate; }
    public LocalDate getNextMaintenanceDateAsLocalDate() { return nextMaintenanceDateAsLocalDate; }
    public String getStatus() { return status; }
    public String getDaysOverdue() { return daysOverdue; }
    public boolean isOverdue() { return isOverdue; }
    public boolean isUpcoming() { return isUpcoming; }
    
    @Override
    public String toString() {
        return vehicleInfo + " - " + status + " (Prochaine: " + nextMaintenanceDate + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MaintenanceItem that = (MaintenanceItem) obj;
        return vehicle != null ? vehicle.getId().equals(that.vehicle.getId()) : that.vehicle == null;
    }
    
    @Override
    public int hashCode() {
        return vehicle != null ? vehicle.getId().hashCode() : 0;
    }
}
