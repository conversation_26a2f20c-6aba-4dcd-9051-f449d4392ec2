package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.VehiculeDAO;
import model.Vehicule;
import model.User;
import java.util.List;
import java.util.stream.Collectors;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.stage.FileChooser;
import java.time.LocalDate;
import java.util.Optional;
import javafx.application.Platform;
import util.ExportOptions;
import util.ExportUtil;
import java.io.File;
import javafx.beans.property.SimpleStringProperty;
import javafx.geometry.Pos;
import javafx.geometry.Rectangle2D;
import javafx.stage.Screen;
import javafx.stage.Modality;

public class VehiculeController {
    // Table view
    @FXML private TableView<Vehicule> vehiculeTable;
    @FXML private TableColumn<Vehicule, Long> idColumn;
    @FXML private TableColumn<Vehicule, String> marqueColumn;
    @FXML private TableColumn<Vehicule, String> modeleColumn;
    @FXML private TableColumn<Vehicule, String> immatriculationColumn;
    @FXML private TableColumn<Vehicule, String> etatColumn;
    @FXML private TableColumn<Vehicule, Double> prixParJourColumn;
    @FXML private TextField searchField;
    @FXML private ComboBox<String> etatCombo;
    @FXML private Label lblTotalCount;

    // Form fields
    @FXML private TextField txtMarqueForm;
    @FXML private TextField txtModeleForm;
    @FXML private TextField txtImmatriculationForm;
    @FXML private ComboBox<String> txtEtatForm;
    @FXML private TextField txtPrixParJourForm;
    @FXML private ComboBox<String> txtCarburantForm;
    @FXML private TextField txtMetrageForm;
    @FXML private DatePicker txtDateAcquisitionForm;
    @FXML private DatePicker txtLastUsedForm;
    @FXML private TextField txtNbreChevauxForm;
    @FXML private TextField txtAssuranceCompagnieForm;
    @FXML private DatePicker txtAssuranceExpirationForm;
    @FXML private TextField txtAssuranceNumeroForm;
    @FXML private ImageView imgPreview;
    @FXML private Button btnChooseImage;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;

    private String selectedImagePath = null;
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private ObservableList<Vehicule> vehiculeList;
    private Vehicule selectedVehicule = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        marqueColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getMarque()));
        modeleColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getModele()));
        immatriculationColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getImmatriculation()));
        etatColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getEtat()));
        prixParJourColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleDoubleProperty(data.getValue().getPrixParJour()).asObject());

        ObservableList<String> etats = FXCollections.observableArrayList("Disponible", "Loué", "En panne", "En maintenance");
        etatCombo.setItems(etats);
        txtEtatForm.setItems(etats);
        ObservableList<String> carburants = FXCollections.observableArrayList("Essence", "Diesel", "Hybride", "Electrique");
        txtCarburantForm.setItems(carburants);

        vehiculeTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedVehicule = newSelection;
                    loadVehiculeToForm(newSelection);
                    isEditMode = true;
                    btnSave.setText("Modifier");
                } else {
                    clearForm();
                }
            } catch (Exception e) {
                System.err.println("Error in vehicule selection: " + e.getMessage());
                clearForm();
            }
        });

        loadVehicules();
        clearForm();
    }

    private void loadVehicules() {
        List<Vehicule> list = vehiculeDAO.findAll();
        vehiculeList = FXCollections.observableArrayList(list);
        vehiculeTable.setItems(vehiculeList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + list.size());
        }
    }

    private void loadVehiculeToForm(Vehicule vehicule) {
        if (vehicule == null) {
            clearForm();
            return;
        }

        // Use null-safe operations for all fields
        txtMarqueForm.setText(vehicule.getMarque() != null ? vehicule.getMarque() : "");
        txtModeleForm.setText(vehicule.getModele() != null ? vehicule.getModele() : "");
        txtImmatriculationForm.setText(vehicule.getImmatriculation() != null ? vehicule.getImmatriculation() : "");
        txtEtatForm.setValue(vehicule.getEtat() != null ? vehicule.getEtat() : "Disponible");
        txtPrixParJourForm.setText(vehicule.getPrixParJour() != null ? String.valueOf(vehicule.getPrixParJour()) : "0.0");
        txtCarburantForm.setValue(vehicule.getCarburant() != null ? vehicule.getCarburant() : "Essence");
        txtMetrageForm.setText(vehicule.getMetrage() != null ? vehicule.getMetrage().toString() : "");
        txtDateAcquisitionForm.setValue(vehicule.getDateAcquisition());
        txtLastUsedForm.setValue(vehicule.getLastUsed());
        txtNbreChevauxForm.setText(vehicule.getNbreChevaux() != null ? vehicule.getNbreChevaux().toString() : "");
        txtAssuranceCompagnieForm.setText(vehicule.getAssuranceCompagnie() != null ? vehicule.getAssuranceCompagnie() : "");
        txtAssuranceExpirationForm.setValue(vehicule.getAssuranceExpiration());
        txtAssuranceNumeroForm.setText(vehicule.getAssuranceNumero() != null ? vehicule.getAssuranceNumero() : "");

        // Handle image loading safely
        selectedImagePath = vehicule.getPhotoUrl();
        if (selectedImagePath != null && !selectedImagePath.isEmpty()) {
            try {
                imgPreview.setImage(new javafx.scene.image.Image(selectedImagePath));
            } catch (Exception e) {
                imgPreview.setImage(null);
                selectedImagePath = null;
            }
        } else {
            imgPreview.setImage(null);
        }
    }

    private void clearForm() {
        txtMarqueForm.clear();
        txtModeleForm.clear();
        txtImmatriculationForm.clear();
        txtEtatForm.setValue(null);
        txtPrixParJourForm.clear();
        txtCarburantForm.setValue(null);
        txtMetrageForm.clear();
        txtDateAcquisitionForm.setValue(null);
        txtLastUsedForm.setValue(null);
        txtNbreChevauxForm.clear();
        txtAssuranceCompagnieForm.clear();
        txtAssuranceExpirationForm.setValue(null);
        txtAssuranceNumeroForm.clear();
        imgPreview.setImage(null);
        selectedImagePath = null;
        selectedVehicule = null;
        isEditMode = false;
        btnSave.setText("Enregistrer");
    }

    @FXML
    private void handleAjouter() {
        clearForm();
    }

    @FXML
    private void handleModifier() {
        if (selectedVehicule != null) {
            isEditMode = true;
            btnSave.setText("Modifier");
        } else {
            showAlert("Veuillez sélectionner un véhicule à modifier", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSupprimer() {
        // Check if user is admin - only admins can delete vehicles
        if (!isAdmin()) {
            showAlert("Seuls les administrateurs peuvent supprimer des véhicules.", AlertType.WARNING);
            return;
        }

        Vehicule selected = vehiculeTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Supprimer le véhicule");
            alert.setContentText("Êtes-vous sûr de vouloir supprimer " + selected.getMarque() + " " + selected.getModele() + " ?");
            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        vehiculeDAO.delete(selected);
                        loadVehicules();
                        clearForm();
                        showAlert("Véhicule supprimé avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de la suppression: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner un véhicule à supprimer", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSave() {
        String marque = txtMarqueForm.getText().trim();
        String modele = txtModeleForm.getText().trim();
        String immatriculation = txtImmatriculationForm.getText().trim();
        String etat = txtEtatForm.getValue();
        String prixText = txtPrixParJourForm.getText().trim();
        String carburant = txtCarburantForm.getValue();
        String metrageText = txtMetrageForm.getText().trim();
        LocalDate dateAcquisition = txtDateAcquisitionForm.getValue();
        LocalDate lastUsed = txtLastUsedForm.getValue();
        String nbreChevauxText = txtNbreChevauxForm.getText().trim();
        String assuranceCompagnie = txtAssuranceCompagnieForm.getText().trim();
        LocalDate assuranceExpiration = txtAssuranceExpirationForm.getValue();
        String assuranceNumero = txtAssuranceNumeroForm.getText().trim();

        if (marque.isEmpty() || modele.isEmpty() || immatriculation.isEmpty() || etat == null || carburant == null || metrageText.isEmpty() || dateAcquisition == null) {
            showAlert("Veuillez remplir tous les champs obligatoires", AlertType.WARNING);
            return;
        }
        double prix = 0.0;
        int metrage = 0;
        Integer nbreChevaux = null;
        try {
            prix = Double.parseDouble(prixText);
            metrage = Integer.parseInt(metrageText);
            if (!nbreChevauxText.isEmpty()) nbreChevaux = Integer.parseInt(nbreChevauxText);
        } catch (NumberFormatException e) {
            showAlert("Le prix, le métrage et le nombre de chevaux doivent être des nombres valides", AlertType.WARNING);
            return;
        }
        try {
            if (isEditMode && selectedVehicule != null) {
                selectedVehicule.setMarque(marque);
                selectedVehicule.setModele(modele);
                selectedVehicule.setImmatriculation(immatriculation);
                selectedVehicule.setEtat(etat);
                selectedVehicule.setPrixParJour(prix);
                selectedVehicule.setPhotoUrl(selectedImagePath);
                selectedVehicule.setCarburant(carburant);
                selectedVehicule.setMetrage(metrage);
                selectedVehicule.setDateAcquisition(dateAcquisition);
                selectedVehicule.setLastUsed(lastUsed);
                selectedVehicule.setNbreChevaux(nbreChevaux);
                selectedVehicule.setAssuranceCompagnie(assuranceCompagnie);
                selectedVehicule.setAssuranceExpiration(assuranceExpiration);
                selectedVehicule.setAssuranceNumero(assuranceNumero);
                vehiculeDAO.save(selectedVehicule);
                showAlert("Véhicule modifié avec succès", AlertType.INFORMATION);
            } else {
                Vehicule newVehicule = new Vehicule();
                newVehicule.setMarque(marque);
                newVehicule.setModele(modele);
                newVehicule.setImmatriculation(immatriculation);
                newVehicule.setEtat(etat);
                newVehicule.setPrixParJour(prix);
                newVehicule.setPhotoUrl(selectedImagePath);
                newVehicule.setCarburant(carburant);
                newVehicule.setMetrage(metrage);
                newVehicule.setDateAcquisition(dateAcquisition);
                newVehicule.setLastUsed(lastUsed);
                newVehicule.setNbreChevaux(nbreChevaux);
                newVehicule.setAssuranceCompagnie(assuranceCompagnie);
                newVehicule.setAssuranceExpiration(assuranceExpiration);
                newVehicule.setAssuranceNumero(assuranceNumero);
                vehiculeDAO.save(newVehicule);
                showAlert("Véhicule ajouté avec succès", AlertType.INFORMATION);
            }
            loadVehicules();
            clearForm();
        } catch (Exception e) {
            showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), AlertType.ERROR);
        }
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    @FXML
    private void handleRefresh() {
        searchField.clear();
        loadVehicules();
        clearForm();
    }

    @FXML
    private void handleExport() {
        Dialog<ExportOptions> dialog = new Dialog<>();
        dialog.setTitle("Exporter les véhicules");
        dialog.setHeaderText("Choisissez les options d'export");

        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(exportButtonType, ButtonType.CANCEL);

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> formatCombo = new ComboBox<>();
        formatCombo.getItems().addAll("CSV", "PDF");
        formatCombo.setValue("CSV");

        ComboBox<String> periodCombo = new ComboBox<>();
        periodCombo.getItems().addAll("Tous les véhicules", "Période personnalisée");
        periodCombo.setValue("Tous les véhicules");

        DatePicker startDate = new DatePicker();
        DatePicker endDate = new DatePicker();
        startDate.setDisable(true);
        endDate.setDisable(true);

        periodCombo.valueProperty().addListener((obs, oldVal, newVal) -> {
            boolean custom = "Période personnalisée".equals(newVal);
            startDate.setDisable(!custom);
            endDate.setDisable(!custom);
        });

        grid.add(new Label("Format:"), 0, 0);
        grid.add(formatCombo, 1, 0);
        grid.add(new Label("Période:"), 0, 1);
        grid.add(periodCombo, 1, 1);
        grid.add(new Label("Date début:"), 0, 2);
        grid.add(startDate, 1, 2);
        grid.add(new Label("Date fin:"), 0, 3);
        grid.add(endDate, 1, 3);

        dialog.getDialogPane().setContent(grid);

        Platform.runLater(() -> formatCombo.requestFocus());

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == exportButtonType) {
                return new ExportOptions(
                    formatCombo.getValue(),
                    periodCombo.getValue(),
                    startDate.getValue(),
                    endDate.getValue()
                );
            }
            return null;
        });

        Optional<ExportOptions> result = dialog.showAndWait();

        result.ifPresent(options -> {
            try {
                FileChooser fileChooser = new FileChooser();
                fileChooser.setTitle("Sauvegarder l'export des véhicules");
                String extension = "CSV".equals(options.getFormat()) ? "*.csv" : "*.pdf";
                String formatName = "CSV".equals(options.getFormat()) ? "CSV Files" : "PDF Files";
                fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter(formatName, extension));
                File file = fileChooser.showSaveDialog(vehiculeTable.getScene().getWindow());
                if (file != null) {
                    List<Vehicule> dataToExport = vehiculeList;
                    if ("Période personnalisée".equals(options.getPeriod()) && options.getStartDate() != null && options.getEndDate() != null) {
                        dataToExport = vehiculeList.stream()
                            .filter(v -> {
                                if (v.getDateAcquisition() == null) return false;
                                return !v.getDateAcquisition().isBefore(options.getStartDate()) &&
                                       !v.getDateAcquisition().isAfter(options.getEndDate());
                            })
                            .collect(Collectors.toList());
                    }
                    String[] headers = {"ID", "Marque", "Modèle", "Immatriculation", "État", "Prix/Jour (DH)", "Carburant", "Métrage", "Date d'acquisition"};
                    if ("CSV".equals(options.getFormat())) {
                        ExportUtil.exportToCSV(dataToExport, headers, vehicule -> new String[]{
                            String.valueOf(vehicule.getId()),
                            vehicule.getMarque(),
                            vehicule.getModele(),
                            vehicule.getImmatriculation(),
                            vehicule.getEtat(),
                            String.valueOf(vehicule.getPrixParJour()),
                            vehicule.getCarburant() != null ? vehicule.getCarburant() : "",
                            vehicule.getMetrage() != null ? vehicule.getMetrage().toString() : "",
                            vehicule.getDateAcquisition() != null ? vehicule.getDateAcquisition().toString() : ""
                        }, file.getAbsolutePath());
                    } else {
                        ExportUtil.exportToPDF(dataToExport, headers, vehicule -> new String[]{
                            String.valueOf(vehicule.getId()),
                            vehicule.getMarque(),
                            vehicule.getModele(),
                            vehicule.getImmatriculation(),
                            vehicule.getEtat(),
                            String.valueOf(vehicule.getPrixParJour()),
                            vehicule.getCarburant() != null ? vehicule.getCarburant() : "",
                            vehicule.getMetrage() != null ? vehicule.getMetrage().toString() : "",
                            vehicule.getDateAcquisition() != null ? vehicule.getDateAcquisition().toString() : ""
                        }, file.getAbsolutePath());
                    }
                    showAlert("Export réussi!\nFichier sauvegardé: " + file.getAbsolutePath(), AlertType.INFORMATION);
                }
            } catch (Exception e) {
                showAlert("Erreur lors de l'export: " + e.getMessage(), AlertType.ERROR);
            }
        });
    }



    @FXML
    private void handleRechercher() {
        String keyword = searchField.getText().toLowerCase();
        if (keyword.isEmpty()) {
            loadVehicules();
            return;
        }
        List<Vehicule> filtered = vehiculeDAO.findAll().stream()
                .filter(v -> v.getMarque().toLowerCase().contains(keyword)
                        || v.getModele().toLowerCase().contains(keyword)
                        || v.getImmatriculation().toLowerCase().contains(keyword)
                        || v.getEtat().toLowerCase().contains(keyword))
                .collect(Collectors.toList());
        vehiculeList.setAll(filtered);
        vehiculeTable.setItems(vehiculeList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filtered.size());
        }
    }

    @FXML
    private void handleClearFilters() {
        searchField.clear();
        loadVehicules();
    }

    @FXML
    private void handleChangerEtat() {
        Vehicule selected = vehiculeTable.getSelectionModel().getSelectedItem();
        String newEtat = etatCombo.getValue();
        if (selected != null && newEtat != null) {
            try {
                selected.setEtat(newEtat);
                vehiculeDAO.save(selected);
                loadVehicules();
                showAlert("État du véhicule modifié avec succès", AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Erreur lors de la modification: " + e.getMessage(), AlertType.ERROR);
            }
        } else {
            showAlert("Veuillez sélectionner un véhicule et un nouvel état", AlertType.WARNING);
        }
    }

    @FXML
    private void handleChooseImage() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choisir une image de véhicule");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Images", "*.png", "*.jpg", "*.jpeg", "*.gif")
        );
        File file = fileChooser.showOpenDialog(btnChooseImage.getScene().getWindow());
        if (file != null) {
            selectedImagePath = file.toURI().toString();
            imgPreview.setImage(new Image(selectedImagePath));
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private boolean isAdmin() {
        // Check if logged in user is admin
        if (LoginController.loggedInUser != null) {
            try {
                User user = (User) LoginController.loggedInUser;
                return "admin".equals(user.getRole());
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    @FXML
    private void handleReturnPreview() {
        try {
            System.out.println("Opening return preview...");

            // Create return preview dialog
            Dialog<Void> dialog = new Dialog<>();
            dialog.setTitle("Prévisions de Retour des Véhicules");
            dialog.setHeaderText("Véhicules attendus en retour dans les prochains jours");

            // Create content
            javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(15);
            content.setPadding(new Insets(20));
            content.setStyle("-fx-background-color: #f8fafc;");

            // Get vehicles currently rented
            List<Vehicule> allVehicles = vehiculeDAO.findAll();
            List<Vehicule> rentedVehicles = allVehicles.stream()
                .filter(v -> "loué".equalsIgnoreCase(v.getEtat()) || "loue".equalsIgnoreCase(v.getEtat()))
                .collect(Collectors.toList());

            // Statistics section
            javafx.scene.layout.HBox statsBox = new javafx.scene.layout.HBox(20);
            statsBox.setAlignment(javafx.geometry.Pos.CENTER);

            javafx.scene.layout.VBox totalCard = createStatCard("🚗 Véhicules Loués", String.valueOf(rentedVehicles.size()), "#ef4444");
            javafx.scene.layout.VBox todayCard = createStatCard("📅 Retours Aujourd'hui", "0", "#f59e0b");
            javafx.scene.layout.VBox weekCard = createStatCard("📊 Retours Cette Semaine", "0", "#3b82f6");
            javafx.scene.layout.VBox overdueCard = createStatCard("⚠️ Retards", "0", "#dc2626");

            statsBox.getChildren().addAll(totalCard, todayCard, weekCard, overdueCard);

            // Rented vehicles table
            Label tableLabel = new Label("Véhicules Actuellement Loués");
            tableLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;");

            TableView<Vehicule> previewTable = new TableView<>();
            previewTable.setPrefHeight(300);

            // Setup columns
            TableColumn<Vehicule, String> marqueCol = new TableColumn<>("Marque");
            marqueCol.setPrefWidth(120);
            marqueCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMarque())
            );

            TableColumn<Vehicule, String> modeleCol = new TableColumn<>("Modèle");
            modeleCol.setPrefWidth(120);
            modeleCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(cellData.getValue().getModele())
            );

            TableColumn<Vehicule, String> immatCol = new TableColumn<>("Immatriculation");
            immatCol.setPrefWidth(130);
            immatCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(cellData.getValue().getImmatriculation())
            );

            TableColumn<Vehicule, String> etatCol = new TableColumn<>("État");
            etatCol.setPrefWidth(100);
            etatCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(cellData.getValue().getEtat())
            );

            TableColumn<Vehicule, String> lastUsedCol = new TableColumn<>("Dernière Utilisation");
            lastUsedCol.setPrefWidth(150);
            lastUsedCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getLastUsed() != null ?
                    cellData.getValue().getLastUsed().toString() : "N/A"
                )
            );

            previewTable.getColumns().addAll(marqueCol, modeleCol, immatCol, etatCol, lastUsedCol);
            previewTable.setItems(FXCollections.observableArrayList(rentedVehicles));

            content.getChildren().addAll(statsBox, tableLabel, previewTable);

            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

            // Set dialog size
            dialog.getDialogPane().setPrefSize(800, 600);

            dialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error opening return preview: " + e.getMessage());
            e.printStackTrace();

            Alert alert = new Alert(AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture des prévisions");
            alert.setContentText("Une erreur s'est produite: " + e.getMessage());
            alert.showAndWait();
        }
    }

    @FXML
    private void handleMaintenance() {
        try {
            System.out.println("Opening maintenance management...");

            // Load the vehicle maintenance FXML
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/vehicle_maintenance.fxml"));
            javafx.scene.Parent root = loader.load();

            // Create new stage for maintenance
            Stage maintenanceStage = new Stage();
            maintenanceStage.setTitle("Gestion de la Maintenance");
            maintenanceStage.setScene(new javafx.scene.Scene(root, 1200, 800));
            maintenanceStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);

            // Maximize window
            javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
            javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
            maintenanceStage.setX(visualBounds.getMinX());
            maintenanceStage.setY(visualBounds.getMinY());
            maintenanceStage.setWidth(visualBounds.getWidth());
            maintenanceStage.setHeight(visualBounds.getHeight() * 0.95);

            maintenanceStage.show();

        } catch (Exception e) {
            System.err.println("Error opening maintenance: " + e.getMessage());
            e.printStackTrace();

            Alert alert = new Alert(AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture de la maintenance");
            alert.setContentText("Une erreur s'est produite: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private javafx.scene.layout.VBox createStatCard(String title, String value, String color) {
        javafx.scene.layout.VBox card = new javafx.scene.layout.VBox(8);
        card.setAlignment(javafx.geometry.Pos.CENTER);
        card.setStyle("-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; " +
                     "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }
}
