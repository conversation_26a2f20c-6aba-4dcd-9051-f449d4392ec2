package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import java.util.Optional;

import dao.VehicleMaintenanceDAO;
import dao.VehicleFailureDAO;
import dao.VehiculeDAO;
import model.VehicleMaintenance;
import model.VehicleFailure;
import model.Vehicule;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

public class VehicleMaintenanceController {
    
    // FXML Components
    @FXML private Label lblOverdueMaintenance;
    @FXML private Label lblOldVehicles;
    @FXML private Label lblUpcomingMaintenance;
    @FXML private Label lblTotalMaintenances;
    @FXML private Label lblTotalCount;
    
    @FXML private ComboBox<Vehicule> filterVehicleCombo;
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterTypeCombo;
    @FXML private DatePicker filterDatePicker;
    @FXML private TextField searchField;
    
    @FXML private TableView<MaintenanceItem> maintenanceTable;
    @FXML private TableColumn<MaintenanceItem, String> colVehicle;
    @FXML private TableColumn<MaintenanceItem, String> colAge;
    @FXML private TableColumn<MaintenanceItem, String> colType;
    @FXML private TableColumn<MaintenanceItem, String> colLastMaintenance;
    @FXML private TableColumn<MaintenanceItem, String> colNextMaintenance;
    @FXML private TableColumn<MaintenanceItem, String> colStatus;
    @FXML private TableColumn<MaintenanceItem, String> colDaysOverdue;
    @FXML private TableColumn<MaintenanceItem, Void> colActions;
    
    // Sidebar components
    @FXML private Label lblSelectedVehicle;
    @FXML private Label lblVehicleAge;
    @FXML private Label lblVehicleKm;
    @FXML private Label lblLastMaintenanceInfo;
    @FXML private ListView<String> maintenanceHistoryList;
    @FXML private ListView<String> failureHistoryList;
    
    // Data and Services
    private final VehicleMaintenanceDAO maintenanceDAO = new VehicleMaintenanceDAO();
    private final VehicleFailureDAO failureDAO = new VehicleFailureDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    
    private ObservableList<MaintenanceItem> maintenanceItems;
    private ObservableList<MaintenanceItem> filteredItems;
    private Vehicule selectedVehicle = null;
    
    @FXML
    private void initialize() {
        try {
            setupTable();
            setupFilters();
            loadMaintenanceData();
            updateStatistics();
            System.out.println("Vehicle maintenance controller initialized successfully");
        } catch (Exception e) {
            System.err.println("Error initializing maintenance controller: " + e.getMessage());
            e.printStackTrace();
            // Initialize with empty data to prevent crashes
            maintenanceItems = FXCollections.observableArrayList();
            filteredItems = FXCollections.observableArrayList();
            if (maintenanceTable != null) {
                maintenanceTable.setItems(filteredItems);
            }
            updateStatistics();
        }
    }
    
    private void setupTable() {
        // Configure table columns
        colVehicle.setCellValueFactory(new PropertyValueFactory<>("vehicleInfo"));
        colAge.setCellValueFactory(new PropertyValueFactory<>("vehicleAge"));
        colType.setCellValueFactory(new PropertyValueFactory<>("maintenanceType"));
        colLastMaintenance.setCellValueFactory(new PropertyValueFactory<>("lastMaintenanceDate"));
        colNextMaintenance.setCellValueFactory(new PropertyValueFactory<>("nextMaintenanceDate"));
        colStatus.setCellValueFactory(new PropertyValueFactory<>("status"));
        colDaysOverdue.setCellValueFactory(new PropertyValueFactory<>("daysOverdue"));
        
        // Setup actions column with buttons
        colActions.setCellFactory(param -> new TableCell<MaintenanceItem, Void>() {
            private final Button viewButton = new Button("👁️");
            private final Button maintainButton = new Button("🔧");
            private final Button failureButton = new Button("⚠️");
            
            {
                viewButton.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8;");
                maintainButton.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8;");
                failureButton.setStyle("-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 4 8;");
                
                viewButton.setOnAction(e -> {
                    try {
                        MaintenanceItem item = getTableView().getItems().get(getIndex());
                        if (item != null && item.getVehicle() != null) {
                            showVehicleDetails(item.getVehicle());
                        }
                    } catch (Exception ex) {
                        System.err.println("Error in view button: " + ex.getMessage());
                    }
                });

                maintainButton.setOnAction(e -> {
                    try {
                        MaintenanceItem item = getTableView().getItems().get(getIndex());
                        if (item != null && item.getVehicle() != null) {
                            scheduleMaintenance(item.getVehicle());
                        }
                    } catch (Exception ex) {
                        System.err.println("Error in maintain button: " + ex.getMessage());
                    }
                });

                failureButton.setOnAction(e -> {
                    try {
                        MaintenanceItem item = getTableView().getItems().get(getIndex());
                        if (item != null && item.getVehicle() != null) {
                            showFailureHistory(item.getVehicle());
                        }
                    } catch (Exception ex) {
                        System.err.println("Error in failure button: " + ex.getMessage());
                    }
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    javafx.scene.layout.HBox buttons = new javafx.scene.layout.HBox(3);
                    buttons.getChildren().addAll(viewButton, maintainButton, failureButton);
                    setGraphic(buttons);
                }
            }
        });
        
        // Add selection listener
        maintenanceTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedVehicle = newSelection.getVehicle();
                    updateVehicleDetails(selectedVehicle);
                } else {
                    selectedVehicle = null;
                    updateVehicleDetails(null);
                }
            } catch (Exception e) {
                System.err.println("Error in maintenance table selection: " + e.getMessage());
                selectedVehicle = null;
                updateVehicleDetails(null);
            }
        });
    }
    
    private void setupFilters() {
        // Status filter
        filterStatusCombo.setItems(FXCollections.observableArrayList(
            "Tous", "EN_RETARD", "A_VENIR", "PLANIFIE", "EN_COURS", "TERMINE"
        ));
        filterStatusCombo.setValue("Tous");
        filterStatusCombo.setOnAction(e -> applyFilters());
        
        // Type filter
        filterTypeCombo.setItems(FXCollections.observableArrayList(
            "Tous", "PERIODIQUE", "PREVENTIVE", "CORRECTIVE", "VISITE_TECHNIQUE", "VIDANGE", "AUTRE"
        ));
        filterTypeCombo.setValue("Tous");
        filterTypeCombo.setOnAction(e -> applyFilters());
        
        // Vehicle filter
        try {
            List<Vehicule> vehicles = vehiculeDAO.findAll();
            filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicles));
            filterVehicleCombo.setOnAction(e -> applyFilters());
        } catch (Exception e) {
            System.err.println("Error loading vehicles for filter: " + e.getMessage());
        }
        
        // Date filter
        filterDatePicker.setOnAction(e -> applyFilters());
    }
    
    private void loadMaintenanceData() {
        try {
            List<Vehicule> allVehicles = vehiculeDAO.findAll();
            maintenanceItems = FXCollections.observableArrayList();

            if (allVehicles != null && !allVehicles.isEmpty()) {
                for (Vehicule vehicle : allVehicles) {
                    try {
                        MaintenanceItem item = new MaintenanceItem(vehicle, maintenanceDAO, failureDAO);
                        maintenanceItems.add(item);
                    } catch (Exception e) {
                        System.err.println("Error creating maintenance item for vehicle " + vehicle.getId() + ": " + e.getMessage());
                        // Continue with other vehicles
                    }
                }
            }

            filteredItems = FXCollections.observableArrayList(maintenanceItems);
            maintenanceTable.setItems(filteredItems);

        } catch (Exception e) {
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
            // Initialize empty lists to prevent null pointer exceptions
            maintenanceItems = FXCollections.observableArrayList();
            filteredItems = FXCollections.observableArrayList();
            maintenanceTable.setItems(filteredItems);
        }
    }
    
    private void updateStatistics() {
        if (maintenanceItems == null) {
            // Set default values when no data
            if (lblOverdueMaintenance != null) lblOverdueMaintenance.setText("0");
            if (lblOldVehicles != null) lblOldVehicles.setText("0");
            if (lblUpcomingMaintenance != null) lblUpcomingMaintenance.setText("0");
            if (lblTotalMaintenances != null) lblTotalMaintenances.setText("0");
            if (lblTotalCount != null) lblTotalCount.setText("Total: 0 véhicules");
            return;
        }

        try {
            int overdueMaintenance = (int) maintenanceItems.stream()
                .filter(item -> item != null && item.isOverdue())
                .count();

            int oldVehicles = (int) maintenanceItems.stream()
                .filter(item -> item != null && item.getVehicleAgeYears() >= 5)
                .count();

            int upcomingMaintenance = (int) maintenanceItems.stream()
                .filter(item -> item != null && item.isUpcoming())
                .count();

            int totalMaintenances = maintenanceItems.size();

            if (lblOverdueMaintenance != null) lblOverdueMaintenance.setText(String.valueOf(overdueMaintenance));
            if (lblOldVehicles != null) lblOldVehicles.setText(String.valueOf(oldVehicles));
            if (lblUpcomingMaintenance != null) lblUpcomingMaintenance.setText(String.valueOf(upcomingMaintenance));
            if (lblTotalMaintenances != null) lblTotalMaintenances.setText(String.valueOf(totalMaintenances));
            if (lblTotalCount != null) {
                int filteredSize = filteredItems != null ? filteredItems.size() : 0;
                lblTotalCount.setText("Total: " + filteredSize + " véhicules");
            }
        } catch (Exception e) {
            System.err.println("Error updating statistics: " + e.getMessage());
            // Set error values
            if (lblOverdueMaintenance != null) lblOverdueMaintenance.setText("?");
            if (lblOldVehicles != null) lblOldVehicles.setText("?");
            if (lblUpcomingMaintenance != null) lblUpcomingMaintenance.setText("?");
            if (lblTotalMaintenances != null) lblTotalMaintenances.setText("?");
            if (lblTotalCount != null) lblTotalCount.setText("Erreur de calcul");
        }
    }

    private void updateVehicleDetails(Vehicule vehicle) {
        if (vehicle == null) {
            lblSelectedVehicle.setText("Aucun véhicule sélectionné");
            lblVehicleAge.setText("");
            lblVehicleKm.setText("");
            lblLastMaintenanceInfo.setText("");
            maintenanceHistoryList.getItems().clear();
            failureHistoryList.getItems().clear();
            return;
        }

        // Vehicle basic info
        lblSelectedVehicle.setText(vehicle.getMarque() + " " + vehicle.getModele() +
                                  " (" + vehicle.getImmatriculation() + ")");

        int age = calculateVehicleAge(vehicle);
        lblVehicleAge.setText("Âge: " + age + " ans" + (age >= 5 ? " ⚠️" : ""));

        lblVehicleKm.setText("Kilométrage: " + (vehicle.getMetrage() != null ?
                            vehicle.getMetrage() + " km" : "Non renseigné"));

        // Last maintenance info
        try {
            VehicleMaintenance lastMaintenance = maintenanceDAO.findLastMaintenanceForVehicle(vehicle);
            if (lastMaintenance != null) {
                lblLastMaintenanceInfo.setText("Dernière maintenance: " +
                    lastMaintenance.getMaintenanceDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) +
                    " (" + lastMaintenance.getType().name() + ")");
            } else {
                lblLastMaintenanceInfo.setText("Aucune maintenance enregistrée");
            }
        } catch (Exception e) {
            System.err.println("Error loading last maintenance: " + e.getMessage());
            lblLastMaintenanceInfo.setText("Erreur de chargement");
        }

        // Load maintenance history
        loadMaintenanceHistory(vehicle);

        // Load failure history
        loadFailureHistory(vehicle);
    }

    private void loadMaintenanceHistory(Vehicule vehicle) {
        try {
            List<VehicleMaintenance> history = maintenanceDAO.findByVehicle(vehicle);
            ObservableList<String> historyItems = FXCollections.observableArrayList();

            if (history != null && !history.isEmpty()) {
                for (VehicleMaintenance maintenance : history) {
                    try {
                        String item = String.format("%s - %s (%s) - %s",
                            maintenance.getMaintenanceDate() != null ?
                                maintenance.getMaintenanceDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "Date inconnue",
                            maintenance.getType() != null ? maintenance.getType().name() : "Type inconnu",
                            maintenance.getStatus() != null ? maintenance.getStatus().name() : "Statut inconnu",
                            maintenance.getCost() != null ? maintenance.getCost() + " DH" : "Coût non renseigné"
                        );
                        historyItems.add(item);
                    } catch (Exception e) {
                        System.err.println("Error formatting maintenance item: " + e.getMessage());
                        historyItems.add("Erreur de formatage des données");
                    }
                }
            } else {
                historyItems.add("Aucun historique de maintenance");
            }

            maintenanceHistoryList.setItems(historyItems);
        } catch (Exception e) {
            System.err.println("Error loading maintenance history: " + e.getMessage());
            ObservableList<String> errorItems = FXCollections.observableArrayList();
            errorItems.add("Erreur de chargement de l'historique");
            maintenanceHistoryList.setItems(errorItems);
        }
    }

    private void loadFailureHistory(Vehicule vehicle) {
        try {
            List<VehicleFailure> failures = failureDAO.findByVehicle(vehicle);
            ObservableList<String> failureItems = FXCollections.observableArrayList();

            if (failures != null && !failures.isEmpty()) {
                for (VehicleFailure failure : failures) {
                    try {
                        String status = failure.isResolved() ? "✅ Réparé" : "❌ Non réparé";
                        String item = String.format("%s - %s (%s) - %s",
                            failure.getFailureDate() != null ?
                                failure.getFailureDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "Date inconnue",
                            failure.getType() != null ? failure.getType().name() : "Type inconnu",
                            failure.getSeverity() != null ? failure.getSeverity().name() : "N/A",
                            status
                        );
                        failureItems.add(item);
                    } catch (Exception e) {
                        System.err.println("Error formatting failure item: " + e.getMessage());
                        failureItems.add("Erreur de formatage des données");
                    }
                }
            } else {
                failureItems.add("Aucun historique de panne");
            }

            failureHistoryList.setItems(failureItems);
        } catch (Exception e) {
            System.err.println("Error loading failure history: " + e.getMessage());
            ObservableList<String> errorItems = FXCollections.observableArrayList();
            errorItems.add("Erreur de chargement de l'historique");
            failureHistoryList.setItems(errorItems);
        }
    }

    private void applyFilters() {
        if (maintenanceItems == null) {
            filteredItems = FXCollections.observableArrayList();
            maintenanceTable.setItems(filteredItems);
            updateStatistics();
            return;
        }

        try {
            String statusFilter = filterStatusCombo != null ? filterStatusCombo.getValue() : "Tous";
            String typeFilter = filterTypeCombo != null ? filterTypeCombo.getValue() : "Tous";
            Vehicule vehicleFilter = filterVehicleCombo != null ? filterVehicleCombo.getValue() : null;
            LocalDate dateFilter = filterDatePicker != null ? filterDatePicker.getValue() : null;
            String searchText = searchField != null && searchField.getText() != null ?
                searchField.getText().toLowerCase() : "";

            filteredItems = maintenanceItems.stream()
                .filter(item -> {
                    if (item == null) return false;

                    try {
                        // Status filter
                        if (statusFilter != null && !"Tous".equals(statusFilter)) {
                            String itemStatus = item.getMaintenanceStatus();
                            if (itemStatus == null || !statusFilter.equals(itemStatus)) {
                                return false;
                            }
                        }

                        // Type filter
                        if (typeFilter != null && !"Tous".equals(typeFilter)) {
                            String itemType = item.getMaintenanceType();
                            if (itemType == null || !typeFilter.equals(itemType)) {
                                return false;
                            }
                        }

                        // Vehicle filter
                        if (vehicleFilter != null) {
                            if (!vehicleFilter.equals(item.getVehicle())) {
                                return false;
                            }
                        }

                        // Date filter
                        if (dateFilter != null) {
                            LocalDate nextMaintenance = item.getNextMaintenanceDateAsLocalDate();
                            if (nextMaintenance == null || !dateFilter.equals(nextMaintenance)) {
                                return false;
                            }
                        }

                        // Search filter
                        if (searchText != null && !searchText.isEmpty()) {
                            String vehicleInfo = item.getVehicleInfo();
                            String maintenanceType = item.getMaintenanceType();
                            return (vehicleInfo != null && vehicleInfo.toLowerCase().contains(searchText)) ||
                                   (maintenanceType != null && maintenanceType.toLowerCase().contains(searchText));
                        }

                        return true;
                    } catch (Exception e) {
                        System.err.println("Error filtering item: " + e.getMessage());
                        return false;
                    }
                })
                .collect(Collectors.toCollection(FXCollections::observableArrayList));

            maintenanceTable.setItems(filteredItems);
            updateStatistics();
        } catch (Exception e) {
            System.err.println("Error applying filters: " + e.getMessage());
            // Fallback to showing all items
            filteredItems = FXCollections.observableArrayList(maintenanceItems);
            maintenanceTable.setItems(filteredItems);
            updateStatistics();
        }
    }

    private int calculateVehicleAge(Vehicule vehicle) {
        if (vehicle.getDateAcquisition() == null) return 0;
        return LocalDate.now().getYear() - vehicle.getDateAcquisition().getYear();
    }

    // FXML Event Handlers
    @FXML
    private void handleRefresh() {
        loadMaintenanceData();
        updateStatistics();
        showAlert("Données actualisées avec succès", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleAddMaintenance() {
        if (selectedVehicle == null) {
            showAlert("Veuillez sélectionner un véhicule", Alert.AlertType.WARNING);
            return;
        }
        scheduleMaintenance(selectedVehicle);
    }

    @FXML
    private void handleExport() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les données de maintenance");
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("Maintenance_" + LocalDate.now().toString() + ".csv");

            File file = fileChooser.showSaveDialog(maintenanceTable.getScene().getWindow());
            if (file != null) {
                exportToCSV(file);
                showAlert("Export réussi vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleClearFilters() {
        filterStatusCombo.setValue("Tous");
        filterTypeCombo.setValue("Tous");
        filterVehicleCombo.setValue(null);
        filterDatePicker.setValue(null);
        searchField.clear();
        applyFilters();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handleAddFailure() {
        if (selectedVehicle == null) {
            showAlert("Veuillez sélectionner un véhicule", Alert.AlertType.WARNING);
            return;
        }
        openFailureDialog(selectedVehicle, null);
    }

    @FXML
    private void handleScheduleMaintenance() {
        if (selectedVehicle == null) {
            showAlert("Veuillez sélectionner un véhicule", Alert.AlertType.WARNING);
            return;
        }
        scheduleMaintenance(selectedVehicle);
    }

    @FXML
    private void handleViewMaintenanceDetails() {
        if (selectedVehicle == null) {
            showAlert("Veuillez sélectionner un véhicule", Alert.AlertType.WARNING);
            return;
        }
        showVehicleDetails(selectedVehicle);
    }

    @FXML
    private void handleGenerateReport() {
        if (selectedVehicle == null) {
            showAlert("Veuillez sélectionner un véhicule", Alert.AlertType.WARNING);
            return;
        }
        generateMaintenanceReport(selectedVehicle);
    }

    @FXML
    private void closeWindow() {
        Stage stage = (Stage) maintenanceTable.getScene().getWindow();
        stage.close();
    }

    // Utility Methods
    private void showVehicleDetails(Vehicule vehicle) {
        updateVehicleDetails(vehicle);
        // Could open a detailed window here if needed
    }

    private void scheduleMaintenance(Vehicule vehicle) {
        // Create a simple dialog for scheduling maintenance
        Dialog<VehicleMaintenance> dialog = new Dialog<>();
        dialog.setTitle("Programmer Maintenance");
        dialog.setHeaderText("Programmer une maintenance pour " + vehicle.getMarque() + " " + vehicle.getModele());

        // Set the button types
        ButtonType scheduleButtonType = new ButtonType("Programmer", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(scheduleButtonType, ButtonType.CANCEL);

        // Create the form
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<VehicleMaintenance.MaintenanceType> typeCombo = new ComboBox<>();
        typeCombo.getItems().addAll(VehicleMaintenance.MaintenanceType.values());
        typeCombo.setValue(VehicleMaintenance.MaintenanceType.PERIODIQUE);

        DatePicker datePicker = new DatePicker();
        datePicker.setValue(LocalDate.now().plusDays(7));

        TextField descriptionField = new TextField();
        descriptionField.setPromptText("Description de la maintenance");

        TextField costField = new TextField();
        costField.setPromptText("Coût estimé (DH)");

        grid.add(new Label("Type:"), 0, 0);
        grid.add(typeCombo, 1, 0);
        grid.add(new Label("Date:"), 0, 1);
        grid.add(datePicker, 1, 1);
        grid.add(new Label("Description:"), 0, 2);
        grid.add(descriptionField, 1, 2);
        grid.add(new Label("Coût:"), 0, 3);
        grid.add(costField, 1, 3);

        dialog.getDialogPane().setContent(grid);

        // Convert the result when the schedule button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == scheduleButtonType) {
                try {
                    VehicleMaintenance maintenance = new VehicleMaintenance(vehicle, typeCombo.getValue(), datePicker.getValue());
                    maintenance.setDescription(descriptionField.getText());
                    if (!costField.getText().isEmpty()) {
                        maintenance.setCost(Double.parseDouble(costField.getText()));
                    }
                    return maintenance;
                } catch (NumberFormatException e) {
                    showAlert("Coût invalide", Alert.AlertType.ERROR);
                    return null;
                }
            }
            return null;
        });

        Optional<VehicleMaintenance> result = dialog.showAndWait();
        result.ifPresent(maintenance -> {
            try {
                maintenanceDAO.save(maintenance);
                showAlert("Maintenance programmée avec succès", Alert.AlertType.INFORMATION);
                loadMaintenanceData(); // Refresh the table
                updateStatistics();
            } catch (Exception e) {
                showAlert("Erreur lors de la sauvegarde: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        });
    }

    private void showFailureHistory(Vehicule vehicle) {
        try {
            List<VehicleFailure> failures = failureDAO.findByVehicle(vehicle);

            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Historique des Pannes");
            alert.setHeaderText(vehicle.getMarque() + " " + vehicle.getModele() + " - Pannes");

            StringBuilder content = new StringBuilder();
            if (failures.isEmpty()) {
                content.append("Aucune panne enregistrée pour ce véhicule.");
            } else {
                for (VehicleFailure failure : failures) {
                    content.append("📅 ").append(failure.getFailureDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
                           .append(" - ").append(failure.getType().name())
                           .append(" (").append(failure.getSeverity() != null ? failure.getSeverity().name() : "N/A").append(")\n");
                    content.append("📝 ").append(failure.getDescription()).append("\n");
                    if (failure.isResolved()) {
                        content.append("✅ Réparé le ").append(failure.getRepairDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
                        if (failure.getRepairCost() != null) {
                            content.append(" - Coût: ").append(failure.getRepairCost()).append(" DH");
                        }
                    } else {
                        content.append("❌ Non réparé - Statut: ").append(failure.getStatus().name());
                    }
                    content.append("\n\n");
                }
            }

            alert.setContentText(content.toString());
            alert.showAndWait();
        } catch (Exception e) {
            showAlert("Erreur lors du chargement de l'historique: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void openFailureDialog(Vehicule vehicle, VehicleFailure failure) {
        // This would open a failure form dialog
        // For now, show a simple input dialog
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Nouvelle Panne");
        dialog.setHeaderText("Signaler une panne pour " + vehicle.getMarque() + " " + vehicle.getModele());
        dialog.setContentText("Description de la panne:");

        dialog.showAndWait().ifPresent(description -> {
            try {
                VehicleFailure newFailure = new VehicleFailure(vehicle, VehicleFailure.FailureType.AUTRE, description);
                newFailure.setSeverity(VehicleFailure.FailureSeverity.MINEURE);
                failureDAO.save(newFailure);

                loadFailureHistory(vehicle);
                showAlert("Panne enregistrée avec succès", Alert.AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        });
    }

    private void generateMaintenanceReport(Vehicule vehicle) {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Sauvegarder le rapport de maintenance");
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
            fileChooser.setInitialFileName("Rapport_Maintenance_" + vehicle.getImmatriculation() + ".pdf");

            File file = fileChooser.showSaveDialog(maintenanceTable.getScene().getWindow());
            if (file != null) {
                // Generate PDF report (implementation would go here)
                showAlert("Rapport généré avec succès: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur lors de la génération du rapport: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void exportToCSV(File file) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            // CSV Header
            writer.append("Véhicule,Âge,Type Maintenance,Dernière Maintenance,Prochaine Maintenance,Statut,Jours de Retard\n");

            // CSV Data
            for (MaintenanceItem item : filteredItems) {
                writer.append(item.getVehicleInfo()).append(",");
                writer.append(item.getVehicleAge()).append(",");
                writer.append(item.getMaintenanceType()).append(",");
                writer.append(item.getLastMaintenanceDate()).append(",");
                writer.append(item.getNextMaintenanceDate()).append(",");
                writer.append(item.getStatus()).append(",");
                writer.append(item.getDaysOverdue()).append("\n");
            }
        }
    }

    private void showAlert(String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("Maintenance des Véhicules");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
