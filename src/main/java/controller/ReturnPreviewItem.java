package controller;

import model.Location;
import model.Vehicule;
import model.Client;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * Data model class for vehicle return preview table items
 */
public class ReturnPreviewItem {
    private final Location location;
    private final String vehicleInfo;
    private final String clientName;
    private final String startDate;
    private final String returnDate;
    private final String daysLeft;
    private final String status;
    private final String total;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    
    public ReturnPreviewItem(Location location) {
        this.location = location;
        
        // Vehicle information
        Vehicule vehicule = location.getVehicule();
        if (vehicule != null) {
            this.vehicleInfo = (vehicule.getMarque() != null ? vehicule.getMarque() : "") + " " + 
                             (vehicule.getModele() != null ? vehicule.getModele() : "") + 
                             " (" + (vehicule.getImmatriculation() != null ? vehicule.getImmatriculation() : "") + ")";
        } else {
            this.vehicleInfo = "Véhicule non défini";
        }
        
        // Client information
        Client client = location.getClient();
        if (client != null) {
            this.clientName = (client.getNom() != null ? client.getNom() : "") + " " + 
                             (client.getPrenom() != null ? client.getPrenom() : "");
        } else {
            this.clientName = "Client non défini";
        }
        
        // Dates
        this.startDate = location.getDateDebut() != null ? 
            location.getDateDebut().format(DATE_FORMATTER) : "Non définie";
        this.returnDate = location.getDateFinPrevue() != null ? 
            location.getDateFinPrevue().format(DATE_FORMATTER) : "Non définie";
        
        // Days left calculation
        if (location.getDateFinPrevue() != null) {
            LocalDate today = LocalDate.now();
            LocalDate returnDate = location.getDateFinPrevue();
            long days = ChronoUnit.DAYS.between(today, returnDate);
            
            if (days < 0) {
                this.daysLeft = Math.abs(days) + " jour(s) de retard";
            } else if (days == 0) {
                this.daysLeft = "Aujourd'hui";
            } else {
                this.daysLeft = days + " jour(s)";
            }
        } else {
            this.daysLeft = "Non calculé";
        }
        
        // Status with enhanced logic
        if (location.getStatus() != null) {
            this.status = location.getStatus().name();
        } else {
            // Determine status based on dates
            if (location.getDateFinReelle() != null) {
                this.status = "TERMINE";
            } else if (location.getDateFinPrevue() != null && location.getDateFinPrevue().isBefore(LocalDate.now())) {
                this.status = "TERMINE"; // Use TERMINE instead of EN_RETARD since it's not in the enum
            } else {
                this.status = "EN_COURS";
            }
        }
        
        // Total amount
        double totalAmount = location.getPrixTotal();
        if (location.getPenalite() > 0) {
            totalAmount += location.getPenalite();
        }
        this.total = String.format("%.2f", totalAmount);
    }
    
    // Getters
    public Location getLocation() {
        return location;
    }
    
    public String getVehicleInfo() {
        return vehicleInfo;
    }
    
    public String getClientName() {
        return clientName;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public String getReturnDate() {
        return returnDate;
    }
    
    public String getDaysLeft() {
        return daysLeft;
    }

    // Additional method to get numeric days left for calculations
    public int getDaysLeftNumeric() {
        if (location.getDateFinPrevue() == null) return Integer.MAX_VALUE;
        return (int) ChronoUnit.DAYS.between(LocalDate.now(), location.getDateFinPrevue());
    }
    
    public String getStatus() {
        return status;
    }
    
    public String getTotal() {
        return total;
    }
    
    // Utility methods for filtering and sorting
    public boolean isOverdue() {
        return location.getDateFinPrevue() != null && 
               location.getDateFinPrevue().isBefore(LocalDate.now()) &&
               location.getDateFinReelle() == null;
    }
    
    public boolean isReturnToday() {
        return location.getDateFinPrevue() != null && 
               location.getDateFinPrevue().equals(LocalDate.now());
    }
    
    public boolean isReturnThisWeek() {
        if (location.getDateFinPrevue() == null) return false;
        
        LocalDate today = LocalDate.now();
        LocalDate weekEnd = today.plusDays(7);
        LocalDate returnDate = location.getDateFinPrevue();
        
        return !returnDate.isBefore(today) && !returnDate.isAfter(weekEnd);
    }
    
    public int getDaysUntilReturn() {
        if (location.getDateFinPrevue() == null) return Integer.MAX_VALUE;
        
        return (int) ChronoUnit.DAYS.between(LocalDate.now(), location.getDateFinPrevue());
    }
    
    @Override
    public String toString() {
        return vehicleInfo + " - " + clientName + " (Retour: " + returnDate + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ReturnPreviewItem that = (ReturnPreviewItem) obj;
        return location != null ? location.getId().equals(that.location.getId()) : that.location == null;
    }
    
    @Override
    public int hashCode() {
        return location != null ? location.getId().hashCode() : 0;
    }
}
