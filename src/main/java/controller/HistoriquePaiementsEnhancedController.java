package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.geometry.Pos;
import javafx.stage.Stage;
import model.*;
import dao.*;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static model.Location.Status.ANNULE;

public class HistoriquePaiementsEnhancedController implements Initializable {

    // Filter Controls
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterMethodCombo;
    @FXML private ComboBox<Client> filterClientCombo;
    @FXML private ComboBox<Vehicule> filterVehicleCombo;
    @FXML private DatePicker filterDateFrom;
    @FXML private DatePicker filterDateTo;
    @FXML private TextField filterAmountMin;
    @FXML private TextField filterAmountMax;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterPartialOnly;
    @FXML private TextField searchField;

    // Statistics Labels
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblRevenueChange;
    @FXML private Label lblPaidCount;
    @FXML private Label lblPaidPercentage;
    @FXML private Label lblPendingCount;
    @FXML private Label lblPendingAmount;
    @FXML private Label lblOverdueCount;
    @FXML private Label lblOverdueAmount;

    // Charts and Analytics
    @FXML private VBox paymentMethodsChart;
    @FXML private VBox monthlyTrendsChart;

    // Table and Controls
    @FXML private TableView<PaymentRow> paymentsTable;
    @FXML private TableColumn<PaymentRow, Boolean> colSelect;
    @FXML private TableColumn<PaymentRow, Long> colId;
    @FXML private TableColumn<PaymentRow, String> colClient;
    @FXML private TableColumn<PaymentRow, String> colVehicle;
    @FXML private TableColumn<PaymentRow, Double> colAmount;
    @FXML private TableColumn<PaymentRow, String> colMethod;
    @FXML private TableColumn<PaymentRow, String> colDate;
    @FXML private TableColumn<PaymentRow, String> colStatus;
    @FXML private TableColumn<PaymentRow, String> colDueDate;
    @FXML private TableColumn<PaymentRow, String> colReference;
    @FXML private TableColumn<PaymentRow, Void> colActions;

    // Sidebar
    @FXML private VBox paymentDetailCard;
    @FXML private Label lblSelectedPayment;
    @FXML private VBox paymentDetails;
    @FXML private VBox paymentTimeline;

    // Bottom Statistics
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredRevenue;
    @FXML private Label lblLastUpdate;

    // Action Buttons
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnBulkActions;
    @FXML private Button btnMarkPaid;
    @FXML private Button btnSendReminder;
    @FXML private Button btnViewContract;
    @FXML private Button btnGenerateInvoice;
    @FXML private Button btnReconcile;
    @FXML private Button btnAnalytics;

    // Menu Items
    @FXML private MenuItem exportPaymentsCSV;
    @FXML private MenuItem exportPaymentsExcel;
    @FXML private MenuItem exportPaymentsPDF;
    @FXML private MenuItem exportUnpaid;
    @FXML private MenuItem exportOverdue;

    private PaiementDAO paiementDAO;
    private LocationDAO locationDAO;
    private ClientDAO clientDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<PaymentRow> allPayments;
    private ObservableList<PaymentRow> filteredPayments;
    private PaymentRow selectedPayment;

    // Inner class for table rows with selection
    public static class PaymentRow {
        private final BooleanProperty selected = new SimpleBooleanProperty(false);
        private final Paiement payment;
        private final Location location;
        private final Client client;
        private final Vehicule vehicule;

        public PaymentRow(Paiement payment) {
            this.payment = payment;
            this.location = payment.getLocation();
            this.client = location != null ? location.getClient() : null;
            this.vehicule = location != null ? location.getVehicule() : null;
        }

        // Getters
        public BooleanProperty selectedProperty() { return selected; }
        public boolean isSelected() { return selected.get(); }
        public void setSelected(boolean selected) { this.selected.set(selected); }
        public Paiement getPayment() { return payment; }
        public Location getLocation() { return location; }
        public Client getClient() { return client; }
        public Vehicule getVehicule() { return vehicule; }

        // Table column getters
        public Long getId() { return payment.getId(); }
        public String getClientName() { 
            return client != null ? client.getPrenom() + " " + client.getNom() : "N/A"; 
        }
        public String getVehicleName() { 
            return vehicule != null ? vehicule.getMarque() + " " + vehicule.getModele() : "N/A"; 
        }
        public Double getAmount() { return payment.getMontant(); }
        public String getMethod() { 
            return payment.getMethodePaiement() != null ? 
                getMethodDisplayName(payment.getMethodePaiement()) : "N/A"; 
        }
        public String getDate() { 
            return payment.getDatePaiement() != null ? 
                payment.getDatePaiement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A"; 
        }
        public String getStatus() { 
            return payment.getStatus() != null ? 
                getStatusDisplayName(payment.getStatus()) : "N/A"; 
        }
        public String getDueDate() {
            // Since Paiement might not have dateEcheance, we'll use datePaiement + 30 days as default
            LocalDate dueDate = payment.getDatePaiement() != null ?
                payment.getDatePaiement().plusDays(30) : LocalDate.now().plusDays(30);
            return dueDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        }
        public String getReference() {
            return "REF-" + payment.getId(); // Simple reference generation
        }

        private String getStatusDisplayName(Paiement.Status status) {
            return switch (status) {
                case PAYE -> "Payé";
                case EN_ATTENTE -> "En attente";
                case EN_RETARD -> "En retard";
                case PARTIEL -> "Partiel";
                case ANNULE -> "Annulé";
            };
        }

        private String getMethodDisplayName(Paiement.MethodePaiement method) {
            return switch (method) {
                case ESPECES -> "Espèces";
                case CARTE_BANCAIRE -> "Carte";
                case CHEQUE -> "Chèque";
                case VIREMENT -> "Virement";
                case PAYPAL -> "PayPal";
            };
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        paiementDAO = new PaiementDAO();
        locationDAO = new LocationDAO();
        clientDAO = new ClientDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilterControls();
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    private void setupTableColumns() {
        // Selection column
        colSelect.setCellValueFactory(cellData -> cellData.getValue().selectedProperty());
        colSelect.setCellFactory(CheckBoxTableCell.forTableColumn(colSelect));
        colSelect.setEditable(true);

        // Data columns
        colId.setCellValueFactory(cellData -> new SimpleObjectProperty<>(cellData.getValue().getId()));
        colClient.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getClientName()));
        colVehicle.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getVehicleName()));
        colAmount.setCellValueFactory(cellData -> new SimpleObjectProperty<>(cellData.getValue().getAmount()));
        colMethod.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getMethod()));
        colDate.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getDate()));
        colStatus.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getStatus()));
        colDueDate.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getDueDate()));
        colReference.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getReference()));

        // Format amount column
        colAmount.setCellFactory(column -> new TableCell<PaymentRow, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });

        // Actions column
        colActions.setCellFactory(column -> new TableCell<PaymentRow, Void>() {
            private final Button viewBtn = new Button("👁");
            private final Button editBtn = new Button("✏");
            private final HBox buttons = new HBox(5, viewBtn, editBtn);

            {
                buttons.setAlignment(Pos.CENTER);
                viewBtn.setOnAction(e -> {
                    PaymentRow row = getTableView().getItems().get(getIndex());
                    showPaymentDetails(row);
                });
                editBtn.setOnAction(e -> {
                    PaymentRow row = getTableView().getItems().get(getIndex());
                    editPayment(row);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : buttons);
            }
        });

        // Table selection listener
        paymentsTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    selectedPayment = newSelection;
                    showPaymentDetails(newSelection);
                }
            }
        );

        paymentsTable.setEditable(true);
    }

    private void setupFilterControls() {
        // Status filter
        filterStatusCombo.setItems(FXCollections.observableArrayList(
            "Tous", "Payé", "En attente", "En retard", "Partiel", "Annulé"
        ));
        filterStatusCombo.setValue("Tous");

        // Method filter
        filterMethodCombo.setItems(FXCollections.observableArrayList(
            "Toutes", "Espèces", "Carte bancaire", "Chèque", "Virement", "PayPal"
        ));
        filterMethodCombo.setValue("Toutes");

        // Load clients and vehicles
        List<Client> clients = clientDAO.findAll();
        filterClientCombo.setItems(FXCollections.observableArrayList(clients));

        List<Vehicule> vehicules = vehiculeDAO.findAll();
        filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicules));

        // Add listeners for real-time filtering
        filterStatusCombo.setOnAction(e -> applyFilters());
        filterMethodCombo.setOnAction(e -> applyFilters());
        filterClientCombo.setOnAction(e -> applyFilters());
        filterVehicleCombo.setOnAction(e -> applyFilters());
        filterDateFrom.setOnAction(e -> applyFilters());
        filterDateTo.setOnAction(e -> applyFilters());
        filterOverdueOnly.setOnAction(e -> applyFilters());
        filterPartialOnly.setOnAction(e -> applyFilters());

        // Text field listeners
        filterAmountMin.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        filterAmountMax.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void loadData() {
        List<Paiement> payments = paiementDAO.findAll();
        allPayments = FXCollections.observableArrayList(
            payments.stream().map(PaymentRow::new).collect(Collectors.toList())
        );
        filteredPayments = FXCollections.observableArrayList(allPayments);
        paymentsTable.setItems(filteredPayments);
    }

    private void applyFilters() {
        if (allPayments == null) return;

        List<PaymentRow> filtered = allPayments.stream()
            .filter(this::matchesFilters)
            .collect(Collectors.toList());

        filteredPayments.setAll(filtered);
        updateStatistics();
        updateSelectionCount();
    }

    private boolean matchesFilters(PaymentRow row) {
        Paiement payment = row.getPayment();

        // Status filter
        String selectedStatus = filterStatusCombo.getValue();
        if (!"Tous".equals(selectedStatus)) {
            String statusName = row.getStatus();
            if (!selectedStatus.equals(statusName)) return false;
        }

        // Method filter
        String selectedMethod = filterMethodCombo.getValue();
        if (!"Toutes".equals(selectedMethod)) {
            String methodName = row.getMethod();
            if (!selectedMethod.equals(methodName)) return false;
        }

        // Client filter
        Client selectedClient = filterClientCombo.getValue();
        if (selectedClient != null) {
            if (row.getClient() == null || !row.getClient().getId().equals(selectedClient.getId())) {
                return false;
            }
        }

        // Vehicle filter
        Vehicule selectedVehicle = filterVehicleCombo.getValue();
        if (selectedVehicle != null) {
            if (row.getVehicule() == null || !row.getVehicule().getId().equals(selectedVehicle.getId())) {
                return false;
            }
        }

        // Date range filter
        LocalDate fromDate = filterDateFrom.getValue();
        LocalDate toDate = filterDateTo.getValue();
        LocalDate paymentDate = payment.getDatePaiement();
        if (paymentDate != null) {
            if (fromDate != null && paymentDate.isBefore(fromDate)) return false;
            if (toDate != null && paymentDate.isAfter(toDate)) return false;
        }

        // Amount range filter
        try {
            String minText = filterAmountMin.getText();
            if (!minText.isEmpty()) {
                double min = Double.parseDouble(minText);
                if (payment.getMontant() < min) return false;
            }

            String maxText = filterAmountMax.getText();
            if (!maxText.isEmpty()) {
                double max = Double.parseDouble(maxText);
                if (payment.getMontant() > max) return false;
            }
        } catch (NumberFormatException e) {
            // Invalid number format, ignore filter
        }

        // Overdue filter
        if (filterOverdueOnly.isSelected()) {
            LocalDate dueDate = payment.getDatePaiement() != null ?
                payment.getDatePaiement().plusDays(30) : LocalDate.now().plusDays(30);
            if (payment.getStatus() == Paiement.Status.PAYE ||
                !LocalDate.now().isAfter(dueDate)) {
                return false;
            }
        }

        // Partial filter
        if (filterPartialOnly.isSelected()) {
            if (payment.getStatus() != Paiement.Status.PARTIEL) return false;
        }

        // Search filter
        String searchText = searchField.getText();
        if (searchText != null && !searchText.trim().isEmpty()) {
            String searchLower = searchText.toLowerCase();
            return row.getClientName().toLowerCase().contains(searchLower) ||
                   row.getVehicleName().toLowerCase().contains(searchLower) ||
                   row.getReference().toLowerCase().contains(searchLower) ||
                   String.valueOf(payment.getMontant()).contains(searchLower);
        }

        return true;
    }

    private void updateStatistics() {
        List<PaymentRow> payments = filteredPayments;

        // Total revenue
        double totalRevenue = payments.stream()
            .mapToDouble(p -> p.getPayment().getMontant())
            .sum();
        lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));

        // Paid payments
        long paidCount = payments.stream()
            .filter(p -> p.getPayment().getStatus() == Paiement.Status.PAYE)
            .count();
        lblPaidCount.setText(String.valueOf(paidCount));

        double paidPercentage = payments.isEmpty() ? 0 : (double) paidCount / payments.size() * 100;
        lblPaidPercentage.setText(String.format("%.1f%% du total", paidPercentage));

        // Pending payments
        long pendingCount = payments.stream()
            .filter(p -> p.getPayment().getStatus() == Paiement.Status.EN_ATTENTE)
            .count();
        lblPendingCount.setText(String.valueOf(pendingCount));

        double pendingAmount = payments.stream()
            .filter(p -> p.getPayment().getStatus() == Paiement.Status.EN_ATTENTE)
            .mapToDouble(p -> p.getPayment().getMontant())
            .sum();
        lblPendingAmount.setText(String.format("%.2f DH à recevoir", pendingAmount));

        // Overdue payments (using 30 days after payment date as due date)
        long overdueCount = payments.stream()
            .filter(p -> {
                LocalDate dueDate = p.getPayment().getDatePaiement() != null ?
                    p.getPayment().getDatePaiement().plusDays(30) : LocalDate.now().plusDays(30);
                return p.getPayment().getStatus() != Paiement.Status.PAYE &&
                       LocalDate.now().isAfter(dueDate);
            })
            .count();
        lblOverdueCount.setText(String.valueOf(overdueCount));

        double overdueAmount = payments.stream()
            .filter(p -> {
                LocalDate dueDate = p.getPayment().getDatePaiement() != null ?
                    p.getPayment().getDatePaiement().plusDays(30) : LocalDate.now().plusDays(30);
                return p.getPayment().getStatus() != Paiement.Status.PAYE &&
                       LocalDate.now().isAfter(dueDate);
            })
            .mapToDouble(p -> p.getPayment().getMontant())
            .sum();
        lblOverdueAmount.setText(String.format("%.2f DH en retard", overdueAmount));

        // Update charts
        updatePaymentMethodsChart(payments);
        updateMonthlyTrendsChart(payments);

        // Bottom statistics
        lblTotalCount.setText(String.format("Total: %d paiements", payments.size()));
        lblFilteredRevenue.setText(String.format("Revenus filtrés: %.2f DH", totalRevenue));
    }

    private void updatePaymentMethodsChart(List<PaymentRow> payments) {
        paymentMethodsChart.getChildren().clear();

        Map<String, Long> methodCounts = payments.stream()
            .collect(Collectors.groupingBy(PaymentRow::getMethod, Collectors.counting()));

        methodCounts.forEach((method, count) -> {
            HBox methodRow = new HBox(10);
            methodRow.setAlignment(Pos.CENTER_LEFT);

            Label methodLabel = new Label(method);
            methodLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

            Label countLabel = new Label(count.toString());
            countLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

            methodRow.getChildren().addAll(methodLabel, countLabel);
            paymentMethodsChart.getChildren().add(methodRow);
        });
    }

    private void updateMonthlyTrendsChart(List<PaymentRow> payments) {
        monthlyTrendsChart.getChildren().clear();

        // Simple monthly trend display
        Map<String, Double> monthlyRevenue = payments.stream()
            .filter(p -> p.getPayment().getDatePaiement() != null)
            .collect(Collectors.groupingBy(
                p -> p.getPayment().getDatePaiement().format(DateTimeFormatter.ofPattern("MM/yyyy")),
                Collectors.summingDouble(p -> p.getPayment().getMontant())
            ));

        monthlyRevenue.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByKey().reversed())
            .limit(6)
            .forEach(entry -> {
                HBox monthRow = new HBox(10);
                monthRow.setAlignment(Pos.CENTER_LEFT);

                Label monthLabel = new Label(entry.getKey());
                monthLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                Label revenueLabel = new Label(String.format("%.2f DH", entry.getValue()));
                revenueLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

                monthRow.getChildren().addAll(monthLabel, revenueLabel);
                monthlyTrendsChart.getChildren().add(monthRow);
            });
    }

    private void showPaymentDetails(PaymentRow row) {
        if (row == null) return;

        selectedPayment = row;
        Paiement payment = row.getPayment();

        lblSelectedPayment.setText("Paiement #" + payment.getId());

        paymentDetails.getChildren().clear();

        // Add payment details
        addDetailRow("Client:", row.getClientName());
        addDetailRow("Véhicule:", row.getVehicleName());
        addDetailRow("Montant:", String.format("%.2f DH", payment.getMontant()));
        addDetailRow("Méthode:", row.getMethod());
        addDetailRow("Statut:", row.getStatus());
        addDetailRow("Date:", row.getDate());
        addDetailRow("Échéance:", row.getDueDate());
        addDetailRow("Référence:", row.getReference());

        // Update timeline
        updatePaymentTimeline(payment);
    }

    private void addDetailRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);

        Label labelNode = new Label(label);
        labelNode.setStyle("-fx-font-weight: bold; -fx-text-fill: #374151; -fx-min-width: 80px;");

        Label valueNode = new Label(value);
        valueNode.setStyle("-fx-text-fill: #1f2937;");

        row.getChildren().addAll(labelNode, valueNode);
        paymentDetails.getChildren().add(row);
    }

    private void updatePaymentTimeline(Paiement payment) {
        paymentTimeline.getChildren().clear();

        // Add timeline events
        addTimelineEvent("Création", payment.getDatePaiement(), "Paiement créé");
        
        if (payment.getStatus() == Paiement.Status.PAYE) {
            addTimelineEvent("Paiement", payment.getDatePaiement(), "Paiement effectué");
        }
    }

    private void addTimelineEvent(String title, LocalDate date, String description) {
        VBox event = new VBox(5);
        event.setStyle("-fx-padding: 8px; -fx-border-color: #e5e7eb; -fx-border-width: 0 0 1px 0;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #1f2937;");

        Label dateLabel = new Label(date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        dateLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6b7280;");

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #374151;");

        event.getChildren().addAll(titleLabel, dateLabel, descLabel);
        paymentTimeline.getChildren().add(event);
    }

    private void updateSelectionCount() {
        long selectedCount = filteredPayments.stream()
            .filter(PaymentRow::isSelected)
            .count();
        lblSelectedCount.setText(String.format("Sélectionnés: %d", selectedCount));
    }

    private void updateLastUpdateTime() {
        lblLastUpdate.setText("Dernière mise à jour: " + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private void editPayment(PaymentRow row) {
        FXMLUtil.showInfo("Fonctionnalité d'édition en cours de développement.");
    }

    // Event Handlers
    @FXML private void handleRefresh() {
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    @FXML private void handleClearFilters() {
        filterStatusCombo.setValue("Tous");
        filterMethodCombo.setValue("Toutes");
        filterClientCombo.setValue(null);
        filterVehicleCombo.setValue(null);
        filterDateFrom.setValue(null);
        filterDateTo.setValue(null);
        filterAmountMin.clear();
        filterAmountMax.clear();
        filterOverdueOnly.setSelected(false);
        filterPartialOnly.setSelected(false);
        searchField.clear();
        applyFilters();
    }

    @FXML private void handleExportCSV() {
        FXMLUtil.showSuccess("Export CSV en cours de développement.");
    }

    @FXML private void handleExportExcel() {
        FXMLUtil.showInfo("Export Excel en cours de développement.");
    }

    @FXML private void handleExportPDF() {
        FXMLUtil.showInfo("Export PDF en cours de développement.");
    }

    @FXML private void handleExportUnpaid() {
        FXMLUtil.showInfo("Export des impayés en cours de développement.");
    }

    @FXML private void handleExportOverdue() {
        FXMLUtil.showInfo("Export des retards en cours de développement.");
    }

    @FXML private void handleBulkActions() {
        List<PaymentRow> selected = filteredPayments.stream()
            .filter(PaymentRow::isSelected)
            .collect(Collectors.toList());
        
        if (selected.isEmpty()) {
            FXMLUtil.showWarning("Veuillez sélectionner au moins un paiement.");
            return;
        }
        
        FXMLUtil.showInfo(String.format("Actions groupées pour %d paiements sélectionnés.", selected.size()));
    }

    @FXML private void handleMarkPaid() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Marquer comme payé en cours de développement.");
    }

    @FXML private void handleSendReminder() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Envoi de rappel en cours de développement.");
    }

    @FXML private void handleViewContract() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Visualisation du contrat en cours de développement.");
    }

    @FXML private void handleGenerateInvoice() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Génération de facture en cours de développement.");
    }

    @FXML private void handleReconcile() {
        FXMLUtil.showInfo("Rapprochement bancaire en cours de développement.");
    }

    @FXML private void handleAnalytics() {
        FXMLUtil.showInfo("Analyses avancées en cours de développement.");
    }

    @FXML private void handleSearch() {
        applyFilters();
    }

    @FXML private void closeWindow() {
        paymentsTable.getScene().getWindow().hide();
    }
}
