package controller;

import javafx.animation.FadeTransition;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.image.PixelWriter;
import javafx.scene.image.WritableImage;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import javafx.stage.FileChooser;
import javafx.collections.FXCollections;
import javafx.event.ActionEvent;
import javafx.geometry.Pos;
import javafx.geometry.Insets;
import javafx.application.Platform;

import javafx.util.Duration;
import model.Vehicule;
import dao.VehiculeDAO;
import controller.DisponibiliteController;
import controller.HistoriqueController;

import java.util.List;
import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.IOException;

public class CatalogueController {
    // Main containers
    @FXML private FlowPane cataloguePane;
    @FXML private VBox listViewPane;
    @FXML private ScrollPane cardViewContainer;
    @FXML private ScrollPane listViewContainer;
    @FXML private VBox emptyState;

    // Filter controls
    @FXML private ComboBox<String> marqueFilter;
    @FXML private ComboBox<String> etatFilter;
    @FXML private ComboBox<String> carburantFilter;
    @FXML private TextField prixMaxFilter;
    @FXML private TextField searchField;

    // Statistics labels
    @FXML private Label lblTotalCount;
    @FXML private Label lblAvailableCount;
    @FXML private Label lblRentedCount;
    @FXML private Label lblMaintenanceCount;
    @FXML private Label lblTotalVehicles;

    // Action buttons
    @FXML private Button btnViewMode;
    @FXML private Button btnRefresh;
    @FXML private Button btnExport;
    @FXML private Button btnAddVehicle;
    @FXML private Button btnClearFilters;

    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private List<Vehicule> allVehicles; // Store all vehicles for filtering
    private List<Vehicule> filteredVehicles; // Store filtered results
    private boolean isListView = false; // Track current view mode

    @FXML
    private void handleSearch() {
        System.out.println("Search triggered");
        applyFilters();
    }

    @FXML
    private void toggleViewMode() {
        System.out.println("Toggle view mode triggered");
        isListView = !isListView;

        if (isListView) {
            // Switch to list view
            cardViewContainer.setVisible(false);
            listViewContainer.setVisible(true);
            btnViewMode.setText("🔲 Vue Cartes");
            displayVehiclesInList();
        } else {
            // Switch to card view
            listViewContainer.setVisible(false);
            cardViewContainer.setVisible(true);
            btnViewMode.setText("📋 Vue Liste");
            displayVehiclesInCards();
        }
    }

    @FXML
    private void handleRefresh() {
        loadCatalogue();
        System.out.println("Refresh triggered");
    }

    @FXML
    private void handleApplyFilters() {
        System.out.println("Apply filters triggered");
        applyFilters();
    }

    private void applyFilters() {
        if (allVehicles == null) {
            System.out.println("No vehicles loaded yet");
            return;
        }

        System.out.println("Applying filters to " + allVehicles.size() + " vehicles");

        filteredVehicles = allVehicles.stream()
            .filter(this::matchesFilters)
            .collect(Collectors.toList());

        System.out.println("Filtered results: " + filteredVehicles.size() + " vehicles");

        // Update display
        if (isListView) {
            displayVehiclesInList();
        } else {
            displayVehiclesInCards();
        }

        // Update statistics
        updateStatistics();
    }

    private boolean matchesFilters(Vehicule vehicule) {
        // Search field filter
        if (searchField != null && !searchField.getText().trim().isEmpty()) {
            String searchText = searchField.getText().toLowerCase().trim();
            String vehicleText = (vehicule.getMarque() + " " + vehicule.getModele() + " " + vehicule.getImmatriculation()).toLowerCase();
            if (!vehicleText.contains(searchText)) {
                return false;
            }
        }

        // Marque filter
        if (marqueFilter != null && marqueFilter.getValue() != null && !marqueFilter.getValue().isEmpty()) {
            if (!vehicule.getMarque().equalsIgnoreCase(marqueFilter.getValue())) {
                return false;
            }
        }

        // Etat filter
        if (etatFilter != null && etatFilter.getValue() != null && !etatFilter.getValue().isEmpty()) {
            String vehicleEtat = vehicule.getEtat();
            if (vehicleEtat == null || !vehicleEtat.equalsIgnoreCase(etatFilter.getValue())) {
                return false;
            }
        }

        // Carburant filter
        if (carburantFilter != null && carburantFilter.getValue() != null && !carburantFilter.getValue().isEmpty()) {
            if (!vehicule.getCarburant().equalsIgnoreCase(carburantFilter.getValue())) {
                return false;
            }
        }

        // Prix max filter
        if (prixMaxFilter != null && !prixMaxFilter.getText().trim().isEmpty()) {
            try {
                double maxPrice = Double.parseDouble(prixMaxFilter.getText().trim());
                Double vehiclePrice = vehicule.getPrixParJour();
                if (vehiclePrice != null && vehiclePrice > maxPrice) {
                    return false;
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid price format: " + prixMaxFilter.getText());
            }
        }

        return true;
    }

    @FXML
    private void handleClearFilters() {
        System.out.println("Clear filters triggered");

        // Clear all filter controls
        if (marqueFilter != null) {
            marqueFilter.getSelectionModel().clearSelection();
            marqueFilter.setValue(null);
        }
        if (etatFilter != null) {
            etatFilter.getSelectionModel().clearSelection();
            etatFilter.setValue(null);
        }
        if (carburantFilter != null) {
            carburantFilter.getSelectionModel().clearSelection();
            carburantFilter.setValue(null);
        }
        if (prixMaxFilter != null) {
            prixMaxFilter.clear();
        }
        if (searchField != null) {
            searchField.clear();
        }

        // Reset filtered vehicles to all vehicles
        if (allVehicles != null) {
            filteredVehicles = new java.util.ArrayList<>(allVehicles);
        }

        // Refresh display
        if (isListView) {
            displayVehiclesInList();
        } else {
            displayVehiclesInCards();
        }

        // Update statistics
        updateStatistics();

        System.out.println("Filters cleared, showing all " + (filteredVehicles != null ? filteredVehicles.size() : 0) + " vehicles");
    }

    @FXML
    private void handleExport() {
        System.out.println("Export triggered");

        if (filteredVehicles == null || filteredVehicles.isEmpty()) {
            showAlert("Export", "Aucun véhicule à exporter", Alert.AlertType.WARNING);
            return;
        }

        try {
            // Create file chooser
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter la liste des véhicules");
            fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Fichiers CSV", "*.csv"),
                new FileChooser.ExtensionFilter("Fichiers Excel", "*.xlsx"),
                new FileChooser.ExtensionFilter("Tous les fichiers", "*.*")
            );

            // Show save dialog
            File file = fileChooser.showSaveDialog(btnExport.getScene().getWindow());

            if (file != null) {
                exportToCSV(file);
                showAlert("Export Réussi",
                    "Liste des véhicules exportée avec succès!\n" +
                    "Fichier: " + file.getName() + "\n" +
                    "Véhicules exportés: " + filteredVehicles.size(),
                    Alert.AlertType.INFORMATION);
            }

        } catch (Exception e) {
            System.err.println("Error during export: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur d'Export",
                "Une erreur s'est produite lors de l'export:\n" + e.getMessage(),
                Alert.AlertType.ERROR);
        }
    }

    private void exportToCSV(File file) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            // Write CSV header
            writer.println("ID,Marque,Modèle,Immatriculation,Carburant,État,Prix/Jour,Kilométrage");

            // Write vehicle data
            for (Vehicule vehicule : filteredVehicles) {
                writer.printf("%d,%s,%s,%s,%s,%s,%.2f,%d%n",
                    vehicule.getId(),
                    escapeCSV(vehicule.getMarque()),
                    escapeCSV(vehicule.getModele()),
                    escapeCSV(vehicule.getImmatriculation()),
                    escapeCSV(vehicule.getCarburant()),
                    escapeCSV(vehicule.getEtat()),
                    vehicule.getPrixParJour() != null ? vehicule.getPrixParJour() : 0.0,
                    vehicule.getMetrage() != null ? vehicule.getMetrage() : 0
                );
            }
        }
    }

    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @FXML
    private void handleAddVehicle() {
        System.out.println("Add vehicle triggered");

        try {
            // Create add vehicle dialog
            Dialog<Vehicule> dialog = new Dialog<>();
            dialog.setTitle("Ajouter un Nouveau Véhicule");
            dialog.setHeaderText("Saisissez les informations du véhicule");

            // Create form fields
            GridPane grid = new GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new Insets(20, 150, 10, 10));

            TextField marqueField = new TextField();
            marqueField.setPromptText("Marque");
            TextField modeleField = new TextField();
            modeleField.setPromptText("Modèle");
            TextField immatriculationField = new TextField();
            immatriculationField.setPromptText("Immatriculation");
            TextField carburantField = new TextField();
            carburantField.setPromptText("Carburant");
            TextField prixField = new TextField();
            prixField.setPromptText("Prix par jour");
            TextField kilometrageField = new TextField();
            kilometrageField.setPromptText("Kilométrage");

            ComboBox<String> etatCombo = new ComboBox<>();
            etatCombo.getItems().addAll("disponible", "loué", "en panne", "maintenance");
            etatCombo.setValue("disponible");

            // Add fields to grid
            grid.add(new Label("Marque:"), 0, 0);
            grid.add(marqueField, 1, 0);
            grid.add(new Label("Modèle:"), 0, 1);
            grid.add(modeleField, 1, 1);
            grid.add(new Label("Immatriculation:"), 0, 2);
            grid.add(immatriculationField, 1, 2);
            grid.add(new Label("Carburant:"), 0, 3);
            grid.add(carburantField, 1, 3);
            grid.add(new Label("Prix/jour:"), 0, 4);
            grid.add(prixField, 1, 4);
            grid.add(new Label("Kilométrage:"), 0, 5);
            grid.add(kilometrageField, 1, 5);
            grid.add(new Label("État:"), 0, 6);
            grid.add(etatCombo, 1, 6);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            ButtonType saveButtonType = new ButtonType("Ajouter", ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

            // Enable/disable save button based on input
            Node saveButton = dialog.getDialogPane().lookupButton(saveButtonType);
            saveButton.setDisable(true);

            // Validation
            marqueField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || modeleField.getText().trim().isEmpty() ||
                                    immatriculationField.getText().trim().isEmpty());
            });
            modeleField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || marqueField.getText().trim().isEmpty() ||
                                    immatriculationField.getText().trim().isEmpty());
            });
            immatriculationField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || marqueField.getText().trim().isEmpty() ||
                                    modeleField.getText().trim().isEmpty());
            });

            // Convert result
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == saveButtonType) {
                    try {
                        Vehicule vehicule = new Vehicule();
                        vehicule.setMarque(marqueField.getText().trim());
                        vehicule.setModele(modeleField.getText().trim());
                        vehicule.setImmatriculation(immatriculationField.getText().trim());
                        vehicule.setCarburant(carburantField.getText().trim());

                        // Handle price
                        String prixText = prixField.getText().trim();
                        if (!prixText.isEmpty()) {
                            vehicule.setPrixParJour(Double.parseDouble(prixText));
                        }

                        // Handle kilométrage
                        String kmText = kilometrageField.getText().trim();
                        if (!kmText.isEmpty()) {
                            vehicule.setMetrage(Integer.parseInt(kmText));
                        }

                        vehicule.setEtat(etatCombo.getValue());
                        return vehicule;
                    } catch (Exception e) {
                        showAlert("Erreur de Saisie", "Veuillez vérifier les données saisies:\n" + e.getMessage(), Alert.AlertType.ERROR);
                        return null;
                    }
                }
                return null;
            });

            // Show dialog and process result
            Optional<Vehicule> result = dialog.showAndWait();
            result.ifPresent(vehicule -> {
                try {
                    // Save to database
                    vehiculeDAO.save(vehicule);

                    // Refresh catalogue
                    loadCatalogue();

                    showAlert("Succès", "Véhicule ajouté avec succès!", Alert.AlertType.INFORMATION);

                } catch (Exception e) {
                    System.err.println("Error saving vehicle: " + e.getMessage());
                    e.printStackTrace();
                    showAlert("Erreur", "Erreur lors de l'ajout du véhicule:\n" + e.getMessage(), Alert.AlertType.ERROR);
                }
            });

        } catch (Exception e) {
            System.err.println("Error in add vehicle dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur", "Erreur lors de l'ouverture du formulaire:\n" + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        javafx.application.Platform.runLater(() -> {
            Alert alert = new Alert(type);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    @FXML
    public void initialize() {
        System.out.println("Initializing CatalogueController...");

        // Initialize view mode
        isListView = false;

        // Initialize filter options
        initializeFilterOptions();

        // Load catalogue data
        loadCatalogue();
    }

    private void initializeFilterOptions() {
        try {
            List<Vehicule> vehicules = vehiculeDAO.findAll();

            // Setup marque filter
            if (marqueFilter != null) {
                List<String> marques = vehicules.stream()
                    .map(Vehicule::getMarque)
                    .filter(marque -> marque != null && !marque.isEmpty())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                marqueFilter.setItems(FXCollections.observableArrayList(marques));
            }

            // Setup etat filter
            if (etatFilter != null) {
                List<String> etats = vehicules.stream()
                    .map(Vehicule::getEtat)
                    .filter(etat -> etat != null && !etat.isEmpty())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                etatFilter.setItems(FXCollections.observableArrayList(etats));
            }

            // Setup carburant filter
            if (carburantFilter != null) {
                List<String> carburants = vehicules.stream()
                    .map(Vehicule::getCarburant)
                    .filter(carburant -> carburant != null && !carburant.isEmpty())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
                carburantFilter.setItems(FXCollections.observableArrayList(carburants));
            }

            System.out.println("Filter options initialized successfully");

        } catch (Exception e) {
            System.err.println("Error initializing filter options: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateStatistics(List<Vehicule> vehicules) {
        if (vehicules == null) return;

        long availableCount = vehicules.stream().filter(v -> "disponible".equalsIgnoreCase(v.getEtat())).count();
        long rentedCount = vehicules.stream().filter(v -> "loué".equalsIgnoreCase(v.getEtat()) || "loue".equalsIgnoreCase(v.getEtat())).count();
        long maintenanceCount = vehicules.stream().filter(v -> "en panne".equalsIgnoreCase(v.getEtat()) || "maintenance".equalsIgnoreCase(v.getEtat())).count();
        long totalCount = vehicules.size();

        if (lblAvailableCount != null) lblAvailableCount.setText(String.valueOf(availableCount));
        if (lblRentedCount != null) lblRentedCount.setText(String.valueOf(rentedCount));
        if (lblMaintenanceCount != null) lblMaintenanceCount.setText(String.valueOf(maintenanceCount));
        if (lblTotalVehicles != null) lblTotalVehicles.setText(String.valueOf(totalCount));
    }

    private void updateStatistics() {
        if (filteredVehicles != null) {
            updateStatistics(filteredVehicles);
        }
    }

    private void loadCatalogue() {
        try {
            // Load all vehicles from database
            allVehicles = vehiculeDAO.findAll();
            filteredVehicles = new java.util.ArrayList<>(allVehicles);

            System.out.println("Loading catalogue with " + allVehicles.size() + " vehicles");

            // Display vehicles based on current view mode
            if (isListView) {
                displayVehiclesInList();
            } else {
                displayVehiclesInCards();
            }

            // Update statistics
            updateStatistics();

            System.out.println("Catalogue loaded successfully");

        } catch (Exception e) {
            System.err.println("Error loading catalogue: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur", "Erreur lors du chargement du catalogue:\n" + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void displayVehiclesInCards() {
        if (cataloguePane == null || filteredVehicles == null) return;

        cataloguePane.getChildren().clear();

        for (Vehicule v : filteredVehicles) {
            VBox card = createModernVehicleCard(v);
            cataloguePane.getChildren().add(card);
        }

        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filteredVehicles.size() + " véhicules");
        }
    }

    private void displayVehiclesInList() {
        if (listViewPane == null || filteredVehicles == null) {
            System.out.println("List view components not available, falling back to card view");
            displayVehiclesInCards();
            return;
        }

        listViewPane.getChildren().clear();

        // Create header
        HBox header = new HBox(10);
        header.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");
        header.getChildren().addAll(
            createListHeader("Marque", 120),
            createListHeader("Modèle", 120),
            createListHeader("Immatriculation", 120),
            createListHeader("État", 100),
            createListHeader("Prix/jour", 80),
            createListHeader("Kilométrage", 100),
            createListHeader("Actions", 150)
        );
        listViewPane.getChildren().add(header);

        // Add vehicle rows
        for (Vehicule v : filteredVehicles) {
            HBox row = createVehicleListRow(v);
            listViewPane.getChildren().add(row);
        }

        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filteredVehicles.size() + " véhicules");
        }

        System.out.println("List view displayed with " + filteredVehicles.size() + " vehicles");
    }

    private Label createListHeader(String text, double width) {
        Label header = new Label(text);
        header.setMinWidth(width);
        header.setMaxWidth(width);
        header.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");
        return header;
    }

    private HBox createVehicleListRow(Vehicule v) {
        HBox row = new HBox(10);
        row.setStyle("-fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0; -fx-background-color: white;");
        row.setAlignment(Pos.CENTER_LEFT);

        // Add hover effect
        row.setOnMouseEntered(e -> row.setStyle("-fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0; -fx-background-color: #f8f9fa;"));
        row.setOnMouseExited(e -> row.setStyle("-fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0; -fx-background-color: white;"));

        // Create cells
        Label marqueLabel = createListCell(v.getMarque(), 120);
        Label modeleLabel = createListCell(v.getModele(), 120);
        Label immatriculationLabel = createListCell(v.getImmatriculation(), 120);
        Label etatLabel = createListCell(v.getEtat(), 100);
        Label prixLabel = createListCell(v.getPrixParJour() != null ? String.format("%.2f €", v.getPrixParJour()) : "N/A", 80);
        Label kilometrageLabel = createListCell(v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A", 100);

        // Action buttons
        HBox actions = new HBox(5);
        actions.setMinWidth(150);
        actions.setMaxWidth(150);
        actions.setAlignment(Pos.CENTER_LEFT);

        Button detailBtn = new Button("👁️");
        detailBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 5 8;");
        detailBtn.setOnAction(e -> showVehiculeDetail(v));

        Button editBtn = new Button("✏️");
        editBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 5 8;");
        editBtn.setOnAction(e -> editVehicle(v));

        actions.getChildren().addAll(detailBtn, editBtn);

        row.getChildren().addAll(marqueLabel, modeleLabel, immatriculationLabel, etatLabel, prixLabel, kilometrageLabel, actions);

        return row;
    }

    private Label createListCell(String text, double width) {
        Label cell = new Label(text != null ? text : "");
        cell.setMinWidth(width);
        cell.setMaxWidth(width);
        cell.setStyle("-fx-text-fill: #212529;");
        return cell;
    }

    private void editVehicle(Vehicule v) {
        System.out.println("Edit vehicle: " + v.getMarque() + " " + v.getModele());
        // TODO: Implement edit vehicle functionality
        showAlert("Modifier Véhicule", "Fonctionnalité de modification à implémenter", Alert.AlertType.INFORMATION);
    }

    private VBox createModernVehicleCard(Vehicule v) {
        VBox card = new VBox();
        card.setStyle("-fx-background-color: white; " +
                "-fx-background-radius: 16; " +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 10, 0, 0, 2); " +
                "-fx-border-color: #e2e8f0; " +
                "-fx-border-radius: 16; " +
                "-fx-border-width: 1; " +
                "-fx-padding: 0; " +
                "-fx-cursor: hand;");
        card.setPrefWidth(320);
        card.setMaxWidth(320);

        // Vehicle image container
        StackPane imageContainer = new StackPane();
        imageContainer.setStyle("-fx-background-color: #f8fafc; " +
                "-fx-background-radius: 16 16 0 0; " +
                "-fx-padding: 20;");
        imageContainer.setAlignment(Pos.CENTER);

        ImageView carImage = new ImageView();
        carImage.setFitHeight(160);
        carImage.setFitWidth(280);
        carImage.setPreserveRatio(true);
        carImage.setStyle("-fx-background-radius: 12;");

        // Load image with fallback
        try {
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                carImage.setImage(new Image(v.getPhotoUrl()));
            } else {
                carImage.setImage(createPlaceholderImage());
            }
        } catch (Exception e) {
            carImage.setImage(createPlaceholderImage());
        }

        // Add status badge on top of image
        Label statusBadge = new Label(v.getEtat() != null ? v.getEtat() : "N/A");
        statusBadge.setStyle("-fx-background-color: " + getStatusBackgroundColor(v.getEtat()) + "; " +
                "-fx-text-fill: " + getStatusTextColor(v.getEtat()) + "; " +
                "-fx-background-radius: 12; " +
                "-fx-padding: 4 12; " +
                "-fx-font-size: 12px; " +
                "-fx-font-weight: bold;");
        StackPane.setAlignment(statusBadge, Pos.TOP_RIGHT);
        StackPane.setMargin(statusBadge, new Insets(12));

        imageContainer.getChildren().addAll(carImage, statusBadge);

        // Vehicle info container
        VBox infoContainer = new VBox(16);
        infoContainer.setStyle("-fx-padding: 20;");

        // Title and price
        HBox titleRow = new HBox();
        titleRow.setAlignment(Pos.CENTER_LEFT);

        Label titleLabel = new Label((v.getMarque() != null ? v.getMarque() : "") + " " +
                (v.getModele() != null ? v.getModele() : ""));
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1e293b;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        Label priceLabel = new Label((v.getPrixParJour() != null ? v.getPrixParJour() : 0) + " DH/jour");
        priceLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #10b981;");

        titleRow.getChildren().addAll(titleLabel, spacer, priceLabel);

        // Details grid
        GridPane detailsGrid = new GridPane();
        detailsGrid.setHgap(12);
        detailsGrid.setVgap(8);
        detailsGrid.setStyle("-fx-padding: 8 0;");

        // Registration
        Label immatIcon = new Label("🔖");
        Label immatLabel = new Label(v.getImmatriculation() != null ? v.getImmatriculation() : "N/A");
        immatLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(0, immatIcon, immatLabel);

        // Fuel
        Label carburantIcon = new Label("⛽");
        Label carburantLabel = new Label(v.getCarburant() != null ? v.getCarburant() : "N/A");
        carburantLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(1, carburantIcon, carburantLabel);

        // Mileage
        Label kmIcon = new Label("📏");
        Label kmLabel = new Label(v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A");
        kmLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(2, kmIcon, kmLabel);

        // Action buttons
        HBox actionButtons = new HBox(12);
        actionButtons.setAlignment(Pos.CENTER);

        Button btnLouer = new Button("Louer");
        btnLouer.setStyle("-fx-background-color: #3b82f6; " +
                "-fx-text-fill: white; " +
                "-fx-background-radius: 8; " +
                "-fx-padding: 10 24; " +
                "-fx-font-weight: bold; " +
                "-fx-cursor: hand;");
        btnLouer.setOnAction(e -> openLocationCreateWithVehicle(v));

        Button btnDetails = new Button("Détails");
        btnDetails.setStyle("-fx-background-color: white; " +
                "-fx-text-fill: #3b82f6; " +
                "-fx-background-radius: 8; " +
                "-fx-border-color: #3b82f6; " +
                "-fx-border-radius: 8; " +
                "-fx-border-width: 1; " +
                "-fx-padding: 10 24; " +
                "-fx-font-weight: bold; " +
                "-fx-cursor: hand;");
        btnDetails.setOnAction(e -> showVehiculeDetail(v));

        actionButtons.getChildren().addAll(btnDetails, btnLouer);

        infoContainer.getChildren().addAll(titleRow, detailsGrid, actionButtons);
        card.getChildren().addAll(imageContainer, infoContainer);

        // Add hover effect
        card.setOnMouseEntered(e -> {
            card.setStyle(card.getStyle() +
                    "-fx-effect: dropshadow(gaussian, rgba(59,130,246,0.15), 15, 0, 0, 5);");
            card.setTranslateY(-2);
        });
        card.setOnMouseExited(e -> {
            card.setStyle(card.getStyle()
                    .replace("-fx-effect: dropshadow(gaussian, rgba(59,130,246,0.15), 15, 0, 0, 5);", ""));
            card.setTranslateY(0);
        });

        return card;
    }

    private Image createPlaceholderImage() {
        try {
            return new Image("/default-car.png");
        } catch (Exception e) {
            // Create a simple colored rectangle as fallback
            WritableImage wi = new WritableImage(280, 160);
            PixelWriter pw = wi.getPixelWriter();
            for (int x = 0; x < 280; x++) {
                for (int y = 0; y < 160; y++) {
                    pw.setColor(x, y, Color.LIGHTGRAY);
                }
            }
            return wi;
        }
    }

    private String getStatusBackgroundColor(String etat) {
        if (etat == null) return "#f3f4f6";
        switch (etat.toLowerCase()) {
            case "disponible": return "#dcfce7";
            case "loué": return "#fef3c7";
            case "en panne": return "#fee2e2";
            default: return "#f3f4f6";
        }
    }

    private String getStatusTextColor(String etat) {
        if (etat == null) return "#6b7280";
        switch (etat.toLowerCase()) {
            case "disponible": return "#166534";
            case "loué": return "#92400e";
            case "en panne": return "#991b1b";
            default: return "#6b7280";
        }
    }

    // IMPROVED: Better redirect to location create with selected vehicle
    private void openLocationCreateWithVehicle(Vehicule v) {
        try {
            System.out.println("Opening location create with vehicle: " + v.getMarque() + " " + v.getModele());

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            javafx.scene.Parent root = loader.load();

            // Get the controller and set the selected vehicle
            controller.LocationCreateController controller = loader.getController();
            if (controller != null) {
                controller.setVehicule(v);
                System.out.println("Vehicle set in LocationCreateController: " + v.getId());
            } else {
                System.err.println("LocationCreateController is null!");
            }

            // Navigate to the location create view
            javafx.scene.Scene scene = cataloguePane.getScene();
            if (scene != null) {
                // Try to find the main content pane
                javafx.scene.Node contentPane = scene.lookup("#contentPane");
                if (contentPane instanceof javafx.scene.layout.StackPane) {
                    ((javafx.scene.layout.StackPane) contentPane).getChildren().setAll(root);
                    System.out.println("Successfully navigated to location create with selected vehicle");
                } else {
                    // Fallback: open in new window
                    javafx.stage.Stage stage = new javafx.stage.Stage();
                    stage.setTitle("Nouvelle Location - " + v.getMarque() + " " + v.getModele());
                    stage.setScene(new javafx.scene.Scene(root));
                    stage.setMaximized(true);
                    stage.show();
                    System.out.println("Opened location create in new window");
                }
            } else {
                System.err.println("Scene is null!");
            }
        } catch (Exception e) {
            System.err.println("Error opening location create: " + e.getMessage());
            e.printStackTrace();

            // Show error alert
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture du formulaire de location");
            alert.setContentText("Impossible d'ouvrir le formulaire de location pour ce véhicule: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private HBox createVehicleCard(Vehicule v) {
        HBox card = new HBox(20);
        card.setStyle("-fx-background-color: #fff; -fx-background-radius: 12; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-border-width: 1; -fx-cursor: hand;");
        card.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        card.setOnMouseClicked(e -> showVehiculeDetail(v));

        // Vehicle image
        ImageView img = new ImageView();
        try {
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                img.setImage(new Image(v.getPhotoUrl(), 120, 80, true, true));
            }
        } catch (Exception e) { /* ignore */ }
        img.setFitWidth(120);
        img.setFitHeight(80);
        img.setStyle("-fx-background-color: #e2e8f0; -fx-background-radius: 8;");

        // Vehicle details
        VBox details = new VBox(8);
        details.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label marque = new Label(v.getMarque() + " " + v.getModele());
        marque.setStyle("-fx-font-weight: bold; -fx-font-size: 18px; -fx-text-fill: #1a3c40;");

        Label immatriculation = new Label("Immatriculation: " + (v.getImmatriculation() != null ? v.getImmatriculation() : ""));
        immatriculation.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        Label carburant = new Label("Carburant: " + (v.getCarburant() != null ? v.getCarburant() : ""));
        carburant.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        details.getChildren().addAll(marque, immatriculation, carburant);

        // Price and status section
        VBox priceStatus = new VBox(8);
        priceStatus.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        Label prix = new Label(v.getPrixParJour() != null ? String.format("%.2f DH/jour", v.getPrixParJour()) : "Prix non défini");
        prix.setStyle("-fx-text-fill: #3b82f6; -fx-font-size: 16px; -fx-font-weight: bold;");

        String etatColor = "#10b981";
        if (v.getEtat() != null && v.getEtat().toLowerCase().contains("lou")) etatColor = "#f59e42";
        if (v.getEtat() != null && v.getEtat().toLowerCase().contains("panne")) etatColor = "#ef4444";
        Label etat = new Label(v.getEtat());
        etat.setStyle("-fx-font-size: 13px; -fx-font-weight: bold; -fx-text-fill: " + etatColor + "; -fx-background-color: rgba(16,185,129,0.08); -fx-background-radius: 6; -fx-padding: 4 8 4 8; -fx-alignment: center;");

        priceStatus.getChildren().addAll(prix, etat);

        // Action buttons
        VBox actions = new VBox(8);
        actions.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        Button btnDetails = new Button("Voir détails");
        btnDetails.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16; -fx-font-size: 13px; -fx-cursor: hand;");
        btnDetails.setOnAction(e -> showVehiculeDetail(v));

        Button btnLouer = new Button("Louer");
        btnLouer.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16; -fx-font-size: 13px; -fx-cursor: hand;");
        btnLouer.setOnAction(e -> openLocationCreateWithVehicle(v));

        actions.getChildren().addAll(btnDetails, btnLouer);

        // Add all sections to the card
        card.getChildren().addAll(img, details, new Region(), priceStatus, actions);
        HBox.setHgrow(details, javafx.scene.layout.Priority.ALWAYS);

        return card;
    }



    // Add the missing methods from your original code
    private void showVehiculeDetail(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Détails du Véhicule");
        dialog.getDialogPane().setStyle("-fx-background-color: rgba(241, 245, 249, 0.9);");

        // Main content container
        VBox content = new VBox(20);
        content.setStyle("-fx-padding: 32; -fx-background-color: white; -fx-background-radius: 16; " +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 3);");

        // Header with vehicle name and status
        HBox headerBox = new HBox(12);
        headerBox.setAlignment(Pos.CENTER_LEFT);

        Label header = new Label(v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e293b;");

        Label statusBadge = new Label(v.getEtat() != null ? v.getEtat() : "N/A");
        statusBadge.setStyle("-fx-background-color: " + getStatusBackgroundColor(v.getEtat()) + "; " +
                "-fx-text-fill: " + getStatusTextColor(v.getEtat()) + "; " +
                "-fx-background-radius: 12; " +
                "-fx-padding: 4 12; " +
                "-fx-font-size: 12px; " +
                "-fx-font-weight: bold;");

        headerBox.getChildren().addAll(header, statusBadge);

        // Vehicle image with subtle frame
        StackPane imageContainer = new StackPane();
        imageContainer.setStyle("-fx-background-color: #f8fafc; -fx-background-radius: 12;");

        ImageView img = new ImageView();
        try {
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                img.setImage(new Image(v.getPhotoUrl(), 400, 225, true, true));
            } else {
                img.setImage(createPlaceholderImage());
            }
        } catch (Exception e) {
            img.setImage(createPlaceholderImage());
        }
        img.setFitWidth(400);
        img.setFitHeight(225);
        img.setStyle("-fx-background-radius: 8;");
        imageContainer.getChildren().add(img);

        // Details grid - two columns
        GridPane detailsGrid = new GridPane();
        detailsGrid.setHgap(24);
        detailsGrid.setVgap(12);
        detailsGrid.setStyle("-fx-padding: 16 0;");

        // Column 1
        addDetailRow(detailsGrid, 0, "🔖 Immatriculation:", v.getImmatriculation());
        addDetailRow(detailsGrid, 1, "⛽ Carburant:", v.getCarburant());
        addDetailRow(detailsGrid, 2, "📏 Métrage:", v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A");
        addDetailRow(detailsGrid, 3, "📅 Date acquisition:", v.getDateAcquisition() != null ? v.getDateAcquisition().toString() : "N/A");

        // Column 2
        addDetailRow(detailsGrid, 0, "💰 Prix/jour:", v.getPrixParJour() != null ? String.format("%.2f DH", v.getPrixParJour()) : "N/A");
        addDetailRow(detailsGrid, 1, "🏎️ Chevaux:", v.getNbreChevaux() != null ? v.getNbreChevaux().toString() : "N/A");
        addDetailRow(detailsGrid, 2, "🔄 Dernière utilisation:", v.getLastUsed() != null ? v.getLastUsed().toString() : "N/A");
        addDetailRow(detailsGrid, 3, "🛡️ Assurance:",
                (v.getAssuranceCompagnie() != null ? v.getAssuranceCompagnie() : "") +
                        (v.getAssuranceExpiration() != null ? " (exp: " + v.getAssuranceExpiration() + ")" : "") +
                        (v.getAssuranceNumero() != null ? " #" + v.getAssuranceNumero() : ""));

        // Action buttons
        HBox actions = new HBox(16);
        actions.setAlignment(Pos.CENTER);
        actions.setStyle("-fx-padding: 16 0 0 0;");

        Button btnReserver = createActionButton("Louer", "#3b82f6", e -> {
            dialog.close();
            showReservationDialog(v);
        });

        Button btnHistorique = createActionButton("Historique", "#64748b", e -> {
            dialog.close();
            showHistoriqueElaborate(v);
        });

        Button btnDisponibilite = createActionButton("Disponibilité", "#10b981", e -> {
            dialog.close();
            showDisponibiliteCalendar(v);
        });

        actions.getChildren().addAll(btnReserver, btnHistorique, btnDisponibilite);
        content.getChildren().addAll(headerBox, imageContainer, detailsGrid, actions);

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

        // Make close button invisible but still functional
        Node closeButton = dialog.getDialogPane().lookupButton(ButtonType.CLOSE);
        closeButton.setVisible(false);

        // Add subtle animation on show
        FadeTransition ft = new FadeTransition(Duration.millis(200), content);
        ft.setFromValue(0);
        ft.setToValue(1);
        ft.play();

        dialog.showAndWait();
    }

    private void addDetailRow(GridPane grid, int row, String label, String value) {
        Label detailLabel = new Label(label);
        detailLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;");

        Label detailValue = new Label(value != null ? value : "N/A");
        detailValue.setStyle("-fx-font-size: 14px; -fx-text-fill: #334155;");
        detailValue.setWrapText(true);

        grid.addRow(row, detailLabel, detailValue);
    }

    private Button createActionButton(String text, String color, EventHandler<ActionEvent> handler) {
        Button btn = new Button(text);
        btn.setStyle("-fx-background-color: " + color + "; " +
                "-fx-text-fill: white; " +
                "-fx-font-size: 14px; " +
                "-fx-font-weight: bold; " +
                "-fx-background-radius: 8; " +
                "-fx-padding: 12 24; " +
                "-fx-cursor: hand;");
        btn.setOnAction(handler);

        // Hover effect
        btn.setOnMouseEntered(e -> btn.setStyle(btn.getStyle() + "-fx-effect: dropshadow(gaussian, " + color + ", 10, 0.3, 0, 1);"));
        btn.setOnMouseExited(e -> btn.setStyle(btn.getStyle().replace("-fx-effect: dropshadow(gaussian, " + color + ", 10, 0.3, 0, 1);", "")));

        return btn;
    }

    private void showReservationDialog(Vehicule v) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            javafx.scene.Parent root = loader.load();
            controller.LocationCreateController controller = loader.getController();
            controller.setVehicule(v);
            // Use full-page navigation
            javafx.scene.Scene scene = cataloguePane.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                contentPane.getChildren().setAll(root);
            } else {
                javafx.stage.Stage stage = (javafx.stage.Stage) cataloguePane.getScene().getWindow();
                stage.setScene(new javafx.scene.Scene(root));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showHistoriqueDialog(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Historique des Locations");
        VBox content = new VBox(16);
        content.setStyle("-fx-padding: 24; -fx-background-color: #fff; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.10), 10, 0, 0, 2);");
        content.setMinWidth(440);
        content.setMinHeight(340);
        Label header = new Label("\uD83D\uDCC3  Historique pour " + v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-padding: 0 0 8 0;");
        java.util.List<model.Location> data = new dao.LocationDAO().findByVehiculeId(v.getId());
        if (data.isEmpty()) {
            VBox emptyBox = new VBox(10);
            emptyBox.setStyle("-fx-alignment: center; -fx-padding: 40;");
            Label icon = new Label("\uD83D\uDCC3");
            icon.setStyle("-fx-font-size: 54px; -fx-text-fill: #bdbdbd;");
            Label msg = new Label("Aucune location trouvée pour ce véhicule.");
            msg.setStyle("-fx-font-size: 16px; -fx-text-fill: #64748b; -fx-font-weight: bold;");
            emptyBox.getChildren().addAll(icon, msg);
            content.getChildren().addAll(header, emptyBox);
        } else {
            TableView<model.Location> table = new TableView<>();
            TableColumn<model.Location, String> clientCol = new TableColumn<>("Client");
            clientCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getClient() != null ? cellData.getValue().getClient().getNom() + " " + cellData.getValue().getClient().getPrenom() : ""
            ));
            TableColumn<model.Location, String> dateDebutCol = new TableColumn<>("Début");
            dateDebutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateDebut() != null ? cellData.getValue().getDateDebut().toString() : ""
            ));
            TableColumn<model.Location, String> dateFinCol = new TableColumn<>("Fin prévue");
            dateFinCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinPrevue() != null ? cellData.getValue().getDateFinPrevue().toString() : ""
            ));
            TableColumn<model.Location, String> statutCol = new TableColumn<>("Statut");
            statutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinReelle() == null ? "En cours" : "Terminée"
            ));
            // Color status
            statutCol.setCellFactory(col -> new javafx.scene.control.TableCell<>() {
                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setStyle("");
                    } else {
                        setText(item);
                        if (item.equals("En cours")) setStyle("-fx-text-fill: #f59e42; -fx-font-weight: bold;");
                        else if (item.equals("Terminée")) setStyle("-fx-text-fill: #64748b;");
                        else setStyle("-fx-text-fill: #10b981; -fx-font-weight: bold;");
                    }
                }
            });
            table.getColumns().addAll(clientCol, dateDebutCol, dateFinCol, statutCol);
            table.setItems(javafx.collections.FXCollections.observableArrayList(data));
            table.setMinHeight(220);
            table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
            content.getChildren().addAll(header, table);
        }
        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
        dialog.showAndWait();
    }

    private void showDisponibiliteDialog(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Disponibilité du Véhicule");
        VBox content = new VBox(16);
        content.setStyle("-fx-padding: 24; -fx-background-color: #fff; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.10), 10, 0, 0, 2);");
        content.setMinWidth(440);
        content.setMinHeight(340);
        Label header = new Label("\uD83D\uDE97  Disponibilité pour " + v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-padding: 0 0 8 0;");
        java.util.List<model.Location> data = new dao.LocationDAO().findActiveByVehiculeId(v.getId());
        if (data.isEmpty()) {
            VBox emptyBox = new VBox(10);
            emptyBox.setStyle("-fx-alignment: center; -fx-padding: 40;");
            Label icon = new Label("\uD83D\uDE97");
            icon.setStyle("-fx-font-size: 54px; -fx-text-fill: #bdbdbd;");
            Label msg = new Label("Aucune réservation ou location active pour ce véhicule.");
            msg.setStyle("-fx-font-size: 16px; -fx-text-fill: #64748b; -fx-font-weight: bold;");
            emptyBox.getChildren().addAll(icon, msg);
            content.getChildren().addAll(header, emptyBox);
        } else {
            TableView<model.Location> table = new TableView<>();
            TableColumn<model.Location, String> dateDebutCol = new TableColumn<>("Début");
            dateDebutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateDebut() != null ? cellData.getValue().getDateDebut().toString() : ""
            ));
            table.getColumns().add(dateDebutCol);
            table.setItems(javafx.collections.FXCollections.observableArrayList(data));
            table.setMinHeight(220);
            table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
            content.getChildren().addAll(header, table);
        }
        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
        dialog.showAndWait();
    }

    private void showDisponibiliteCalendar(Vehicule v) {
        System.out.println("=== ATTEMPTING TO LOAD DISPONIBILITE CALENDAR ===");

        // Use Platform.runLater to ensure proper JavaFX thread handling
        javafx.application.Platform.runLater(() -> {
            try {
                // Use enhanced main version
                String fxmlPath = "/view/disponibilite.fxml";
                System.out.println("Loading reliable FXML: " + fxmlPath);

                java.net.URL fxmlUrl = getClass().getResource(fxmlPath);
                if (fxmlUrl == null) {
                    throw new RuntimeException("FXML file not found: " + fxmlPath);
                }

                FXMLLoader loader = new FXMLLoader(fxmlUrl);
                System.out.println("FXMLLoader created successfully");

                System.out.println("Loading root...");
                javafx.scene.Parent root = loader.load();
                System.out.println("Root loaded successfully: " + (root != null));

                if (root == null) {
                    throw new RuntimeException("Root is null after loading");
                }

                System.out.println("Getting controller...");
                DisponibiliteController ctrl = loader.getController();
                System.out.println("Controller obtained: " + (ctrl != null ? "SUCCESS" : "NULL"));

                System.out.println("Creating stage...");
                javafx.stage.Stage dialog = new javafx.stage.Stage();
                dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);

                // Create scene with minimum size to ensure visibility
                javafx.scene.Scene scene = new javafx.scene.Scene(root, 1000, 700);
                dialog.setScene(scene);
                dialog.setTitle("Calendrier de Disponibilité - " + v.getMarque() + " " + v.getModele());

                // Set vehicle after scene is created
                if (ctrl != null) {
                    System.out.println("Setting vehicle: " + v.getMarque() + " " + v.getModele());
                    ctrl.setVehicule(v);
                }

                // Maximize window
                javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
                javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
                dialog.setX(visualBounds.getMinX());
                dialog.setY(visualBounds.getMinY());
                dialog.setWidth(visualBounds.getWidth());
                dialog.setHeight(visualBounds.getHeight() * 0.95);

                // Ensure dialog is visible
                dialog.setResizable(true);
                dialog.centerOnScreen();

                System.out.println("Showing dialog...");
                dialog.show(); // Use show() instead of showAndWait() to prevent blocking

                // Bring to front
                dialog.toFront();
                dialog.requestFocus();

                System.out.println("Dialog shown successfully");

            } catch (Exception e) {
                System.err.println("ERROR loading disponibilite calendar: " + e.getMessage());
                e.printStackTrace();

                // Show error dialog to user
                javafx.application.Platform.runLater(() -> {
                    javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                    alert.setTitle("Erreur");
                    alert.setHeaderText("Erreur de chargement");
                    alert.setContentText("Impossible de charger le calendrier de disponibilité: " + e.getMessage());
                    alert.showAndWait();
                });
            }
        });
    }

    private void showHistoriqueElaborate(Vehicule v) {
        System.out.println("=== ATTEMPTING TO LOAD HISTORIQUE ELABORATE ===");

        // Use Platform.runLater to ensure proper JavaFX thread handling
        javafx.application.Platform.runLater(() -> {
            try {
                // Use enhanced main version
                String fxmlPath = "/view/historique.fxml";
                System.out.println("Loading reliable FXML: " + fxmlPath);

                java.net.URL fxmlUrl = getClass().getResource(fxmlPath);
                if (fxmlUrl == null) {
                    throw new RuntimeException("FXML file not found: " + fxmlPath);
                }

                FXMLLoader loader = new FXMLLoader(fxmlUrl);
                System.out.println("FXMLLoader created successfully");

                System.out.println("Loading root...");
                javafx.scene.Parent root = loader.load();
                System.out.println("Root loaded successfully: " + (root != null));

                if (root == null) {
                    throw new RuntimeException("Root is null after loading");
                }

                System.out.println("Getting controller...");
                HistoriqueController ctrl = loader.getController();
                System.out.println("Controller obtained: " + (ctrl != null ? "SUCCESS" : "NULL"));

                System.out.println("Creating stage...");
                javafx.stage.Stage dialog = new javafx.stage.Stage();
                dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);

                // Create scene with minimum size to ensure visibility
                javafx.scene.Scene scene = new javafx.scene.Scene(root, 1000, 700);
                dialog.setScene(scene);
                dialog.setTitle("Historique Détaillé - " + v.getMarque() + " " + v.getModele());

                // Set vehicle after scene is created
                if (ctrl != null) {
                    System.out.println("Setting vehicle: " + v.getMarque() + " " + v.getModele());
                    ctrl.setVehicule(v);
                }

                // Maximize window
                javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
                javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
                dialog.setX(visualBounds.getMinX());
                dialog.setY(visualBounds.getMinY());
                dialog.setWidth(visualBounds.getWidth());
                dialog.setHeight(visualBounds.getHeight() * 0.95);

                // Ensure dialog is visible
                dialog.setResizable(true);
                dialog.centerOnScreen();

                System.out.println("Showing dialog...");
                dialog.show(); // Use show() instead of showAndWait() to prevent blocking

                // Bring to front
                dialog.toFront();
                dialog.requestFocus();

                System.out.println("Dialog shown successfully");

            } catch (Exception e) {
                System.err.println("ERROR loading historique elaborate: " + e.getMessage());
                e.printStackTrace();

                // Show error dialog to user
                javafx.application.Platform.runLater(() -> {
                    javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                    alert.setTitle("Erreur");
                    alert.setHeaderText("Erreur de chargement");
                    alert.setContentText("Impossible de charger l'historique détaillé: " + e.getMessage());
                    alert.showAndWait();
                });
            }
        });
    }
}
