package controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.chart.LineChart;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.XYChart;
import javafx.stage.Stage;
import javafx.stage.FileChooser;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.application.Platform;
import javafx.geometry.Insets;
import model.Vehicule;
import model.Location;
import dao.LocationDAO;
import util.ExportUtil;
import util.ExportOptions;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Collectors;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class HistoriqueController {
    // Header components
    @FXML private Label vehicleInfoLabel;
    @FXML private Button btnExport;
    @FXML private Button btnRefresh;

    // Statistics components
    @FXML private Label totalLocationsLabel;
    @FXML private Label totalLocationsSubLabel;
    @FXML private Label totalRevenueLabel;
    @FXML private Label avgRevenueLabel;
    @FXML private Label totalDaysLabel;
    @FXML private Label avgDaysLabel;
    @FXML private Label utilizationRateLabel;
    @FXML private Label statusLabel;

    // Performance indicators
    @FXML private Label bestMonthLabel;
    @FXML private Label avgDurationLabel;
    @FXML private Label returnRateLabel;
    @FXML private Label topClientLabel;

    // Table components
    @FXML private Label tableSubtitleLabel;
    @FXML private Label tableCountLabel;
    @FXML private Label tableSummaryLabel;

    // Charts
    @FXML private LineChart<String, Number> revenueChart;
    @FXML private PieChart statusChart;

    // Table components
    @FXML private ComboBox<String> filterComboBox;
    @FXML private TableView<Location> historyTable;
    @FXML private TableColumn<Location, Long> idColumn;
    @FXML private TableColumn<Location, String> clientColumn;
    @FXML private TableColumn<Location, LocalDate> dateDebutColumn;
    @FXML private TableColumn<Location, LocalDate> dateFinColumn;
    @FXML private TableColumn<Location, String> durationColumn;
    @FXML private TableColumn<Location, String> statusColumn;
    @FXML private TableColumn<Location, Double> priceColumn;
    @FXML private TableColumn<Location, Void> actionsColumn;

    // Detail panel components
    @FXML private VBox detailPanel;
    @FXML private Label detailClientLabel;
    @FXML private Label detailPhoneLabel;
    @FXML private Label detailStartDateLabel;
    @FXML private Label detailEndDateLabel;
    @FXML private Label detailActualEndLabel;
    @FXML private Label detailDurationLabel;
    @FXML private Label detailPriceLabel;
    @FXML private Label detailStatusLabel;

    // Data
    private Vehicule vehicule;
    private final LocationDAO locationDAO = new LocationDAO();
    private ObservableList<Location> allLocations;
    private ObservableList<Location> filteredLocations;

    @FXML
    private void initialize() {
        System.out.println("=== INITIALIZING HISTORIQUE CONTROLLER ===");

        try {
            // Initialize empty data collections
            allLocations = FXCollections.observableArrayList();
            filteredLocations = FXCollections.observableArrayList();

            // Initialize immediately, then use Platform.runLater for UI updates
            initializeComponents();

            // Schedule UI updates for next JavaFX pulse
            Platform.runLater(() -> {
                try {
                    updateUIComponents();
                    System.out.println("=== HISTORIQUE CONTROLLER UI UPDATED SUCCESSFULLY ===");
                } catch (Exception e) {
                    System.err.println("ERROR updating UI components: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            System.out.println("=== HISTORIQUE CONTROLLER INITIALIZED SUCCESSFULLY ===");
        } catch (Exception e) {
            System.err.println("ERROR initializing HistoriqueController: " + e.getMessage());
            e.printStackTrace();
            // Initialize with safe defaults
            initializeSafeDefaults();
        }
    }

    private void initializeComponents() {
        // Set default vehicle info
        if (vehicleInfoLabel != null) {
            vehicleInfoLabel.setText("📊 Historique Détaillé - Prêt à utiliser");
            System.out.println("Vehicle info label set successfully");
        } else {
            System.out.println("WARNING: vehicleInfoLabel is null!");
        }

        // Initialize statistics with empty values immediately
        updateEmptyStatistics();

        // Initialize filter combo box
        if (filterComboBox != null) {
            filterComboBox.setItems(FXCollections.observableArrayList(
                "Tous", "Réservé", "En cours", "Terminé", "Annulé"
            ));
            filterComboBox.setValue("Tous");
            System.out.println("Filter combo box initialized");
        } else {
            System.out.println("WARNING: filterComboBox is null!");
        }

        // Hide detail panel initially
        if (detailPanel != null) {
            detailPanel.setVisible(false);
        } else {
            System.out.println("WARNING: detailPanel is null!");
        }
    }

    private void updateUIComponents() {
        // Initialize table columns
        setupTableColumns();

        // Initialize charts (removed for simple version)
        // setupCharts();

        // Ensure all components are visible
        if (vehicleInfoLabel != null && vehicleInfoLabel.getParent() != null) {
            vehicleInfoLabel.getParent().setVisible(true);
        }

        // Make sure statistics are visible
        if (totalLocationsLabel != null && totalLocationsLabel.getParent() != null) {
            totalLocationsLabel.getParent().setVisible(true);
        }

        System.out.println("UI components updated and made visible");
    }

    private void initializeEmptyState() {
        // Set default vehicle info
        if (vehicleInfoLabel != null) {
            vehicleInfoLabel.setText("Sélectionnez un véhicule pour voir son historique");
        }

        // Initialize filter combo box
        if (filterComboBox != null) {
            filterComboBox.setItems(FXCollections.observableArrayList(
                "Tous", "Réservé", "En cours", "Terminé", "Annulé"
            ));
            filterComboBox.setValue("Tous");
        }

        // Initialize statistics with empty values
        updateEmptyStatistics();

        // Initialize table columns
        setupTableColumns();

        // Initialize charts
        setupCharts();

        // Hide detail panel initially
        if (detailPanel != null) {
            detailPanel.setVisible(false);
        }
    }

    private void initializeSafeDefaults() {
        allLocations = FXCollections.observableArrayList();
        filteredLocations = FXCollections.observableArrayList();
        updateEmptyStatistics();
    }

    private void updateEmptyStatistics() {
        // Set default statistics
        if (totalLocationsLabel != null) totalLocationsLabel.setText("0");
        if (totalLocationsSubLabel != null) totalLocationsSubLabel.setText("Aucune location enregistrée");
        if (totalRevenueLabel != null) totalRevenueLabel.setText("0.00 €");
        if (avgRevenueLabel != null) avgRevenueLabel.setText("Moyenne: 0.00 €");
        if (totalDaysLabel != null) totalDaysLabel.setText("0");
        if (avgDaysLabel != null) avgDaysLabel.setText("Moyenne: 0 jours");
        if (utilizationRateLabel != null) utilizationRateLabel.setText("0%");
        if (statusLabel != null) statusLabel.setText("Statut: Disponible");
    }

    public void setVehicule(Vehicule v) {
        this.vehicule = v;
        System.out.println("Setting vehicle for historique: " + v.getMarque() + " " + v.getModele());

        Platform.runLater(() -> {
            try {
                // Update vehicle info
                if (vehicleInfoLabel != null) {
                    vehicleInfoLabel.setText("🚗 " + v.getMarque() + " " + v.getModele() + " (" + v.getImmatriculation() + ")");
                }

                if (tableSubtitleLabel != null) {
                    tableSubtitleLabel.setText("Historique complet pour " + v.getMarque() + " " + v.getModele());
                }

                // Load real data
                loadData();

                System.out.println("Vehicle data loaded successfully for historique");
            } catch (Exception e) {
                System.err.println("Error setting vehicle in historique: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }


    private void setupTableColumns() {
        if (historyTable == null) return;

        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));

        clientColumn.setCellValueFactory(cellData -> {
            Location location = cellData.getValue();
            String clientName = location.getClient() != null ?
                location.getClient().getNom() + " " + location.getClient().getPrenom() : "N/A";
            return new javafx.beans.property.SimpleStringProperty(clientName);
        });

        dateDebutColumn.setCellValueFactory(new PropertyValueFactory<>("dateDebut"));
        dateFinColumn.setCellValueFactory(new PropertyValueFactory<>("dateFinPrevue"));

        durationColumn.setCellValueFactory(cellData -> {
            Location location = cellData.getValue();
            if (location.getDateDebut() != null && location.getDateFinPrevue() != null) {
                long days = ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFinPrevue());
                return new javafx.beans.property.SimpleStringProperty(days + " jours");
            }
            return new javafx.beans.property.SimpleStringProperty("N/A");
        });

        statusColumn.setCellValueFactory(cellData -> {
            Location location = cellData.getValue();
            location.updateStatus(); // Update status based on current date
            return new javafx.beans.property.SimpleStringProperty(getStatusText(location.getStatus()));
        });

        priceColumn.setCellValueFactory(new PropertyValueFactory<>("prixTotal"));

        // Actions column with buttons
        actionsColumn.setCellFactory(col -> {
            TableCell<Location, Void> cell = new TableCell<Location, Void>() {
                private final Button detailBtn = new Button("👁️");

                {
                    detailBtn.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 5 8;");
                    detailBtn.setOnAction(e -> {
                        Location location = getTableView().getItems().get(getIndex());
                        showLocationDetail(location);
                    });
                }

                @Override
                protected void updateItem(Void item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        setGraphic(detailBtn);
                    }
                }
            };
            return cell;
        });
    }

    private void setupCharts() {
        // Initialize charts with empty data
        if (revenueChart != null) {
            revenueChart.setTitle("Revenus Mensuels");
            revenueChart.setCreateSymbols(false);

            // Add empty series to show chart structure
            XYChart.Series<String, Number> emptySeries = new XYChart.Series<>();
            emptySeries.setName("Revenus");
            emptySeries.getData().add(new XYChart.Data<>("Aucune donnée", 0));
            revenueChart.getData().clear();
            revenueChart.getData().add(emptySeries);
        }

        if (statusChart != null) {
            statusChart.setTitle("Répartition des Statuts");

            // Add empty data to show chart structure
            statusChart.getData().clear();
            statusChart.getData().add(new PieChart.Data("Aucune donnée", 1));
        }
    }

    private void loadData() {
        if (vehicule == null) {
            allLocations = FXCollections.observableArrayList();
            filteredLocations = FXCollections.observableArrayList();
            updateEmptyStatistics();
            return;
        }

        try {
            System.out.println("Loading location data for vehicle ID: " + vehicule.getId());

            // Load all locations for this vehicle
            List<Location> locations = locationDAO.findByVehiculeId(vehicule.getId());
            allLocations = FXCollections.observableArrayList(locations);
            filteredLocations = FXCollections.observableArrayList(locations);

            System.out.println("Loaded " + locations.size() + " locations for historique");

            // Update all components with real data
            updateStatistics();
            updatePerformanceIndicators();
            updateTable();
            updateTableSummary();

        } catch (Exception e) {
            System.err.println("Error loading data for historique: " + e.getMessage());
            e.printStackTrace();

            // Fallback to empty data
            allLocations = FXCollections.observableArrayList();
            filteredLocations = FXCollections.observableArrayList();
            updateEmptyStatistics();
        }
    }

    private void updatePerformanceIndicators() {
        if (allLocations == null || allLocations.isEmpty()) {
            if (bestMonthLabel != null) bestMonthLabel.setText("N/A");
            if (avgDurationLabel != null) avgDurationLabel.setText("0 jours");
            if (returnRateLabel != null) returnRateLabel.setText("0%");
            if (topClientLabel != null) topClientLabel.setText("N/A");
            return;
        }

        try {
            // Find best month by revenue
            Map<String, Double> monthlyRevenue = new HashMap<>();
            Map<String, Integer> clientCounts = new HashMap<>();
            long totalDuration = 0;
            int completedLocations = 0;

            DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMM yyyy", java.util.Locale.FRENCH);

            for (Location location : allLocations) {
                // Monthly revenue
                if (location.getDateDebut() != null) {
                    String month = location.getDateDebut().format(monthFormatter);
                    monthlyRevenue.merge(month, location.getPrixTotal(), Double::sum);
                }

                // Client counts
                if (location.getClient() != null) {
                    String clientName = location.getClient().getNom() + " " + location.getClient().getPrenom();
                    clientCounts.merge(clientName, 1, Integer::sum);
                }

                // Duration calculation
                if (location.getDateDebut() != null && location.getDateFinPrevue() != null) {
                    long duration = ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFinPrevue());
                    totalDuration += duration;
                    completedLocations++;
                }
            }

            // Best month
            String bestMonth = monthlyRevenue.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("N/A");
            if (bestMonthLabel != null) {
                bestMonthLabel.setText(bestMonth);
            }

            // Average duration
            double avgDuration = completedLocations > 0 ? (double) totalDuration / completedLocations : 0;
            if (avgDurationLabel != null) {
                avgDurationLabel.setText(String.format("%.1f jours", avgDuration));
            }

            // Return rate (clients with multiple rentals)
            long returningClients = clientCounts.values().stream().filter(count -> count > 1).count();
            double returnRate = clientCounts.size() > 0 ? (double) returningClients / clientCounts.size() * 100 : 0;
            if (returnRateLabel != null) {
                returnRateLabel.setText(String.format("%.1f%%", returnRate));
            }

            // Top client
            String topClient = clientCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("N/A");
            if (topClientLabel != null) {
                topClientLabel.setText(topClient);
            }

        } catch (Exception e) {
            System.err.println("Error updating performance indicators: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateTableSummary() {
        if (filteredLocations == null || tableSummaryLabel == null) return;

        try {
            int count = filteredLocations.size();
            double totalRevenue = filteredLocations.stream().mapToDouble(Location::getPrixTotal).sum();
            long totalDays = filteredLocations.stream()
                .filter(l -> l.getDateDebut() != null && l.getDateFinPrevue() != null)
                .mapToLong(l -> ChronoUnit.DAYS.between(l.getDateDebut(), l.getDateFinPrevue()))
                .sum();

            tableSummaryLabel.setText(String.format("Résumé: %d locations • %.2f € total • %d jours",
                count, totalRevenue, totalDays));

            if (tableCountLabel != null) {
                tableCountLabel.setText(count + " location" + (count != 1 ? "s" : "") + " trouvée" + (count != 1 ? "s" : ""));
            }

        } catch (Exception e) {
            System.err.println("Error updating table summary: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateStatistics() {
        if (allLocations == null || allLocations.isEmpty()) {
            updateEmptyStatistics();
            return;
        }

        // Calculate statistics
        int totalLocations = allLocations.size();
        double totalRevenue = allLocations.stream().mapToDouble(Location::getPrixTotal).sum();
        double avgRevenue = totalRevenue / totalLocations;

        long totalDays = allLocations.stream()
            .filter(l -> l.getDateDebut() != null && l.getDateFinPrevue() != null)
            .mapToLong(l -> ChronoUnit.DAYS.between(l.getDateDebut(), l.getDateFinPrevue()))
            .sum();
        double avgDays = totalLocations > 0 ? (double) totalDays / totalLocations : 0;

        // Calculate utilization rate (simplified)
        long daysInYear = 365;
        double utilizationRate = (double) totalDays / daysInYear * 100;

        // Update labels
        totalLocationsLabel.setText(String.valueOf(totalLocations));
        totalLocationsSubLabel.setText("locations enregistrées");
        totalRevenueLabel.setText(String.format("%.2f €", totalRevenue));
        avgRevenueLabel.setText(String.format("Moyenne: %.2f €", avgRevenue));
        totalDaysLabel.setText(String.valueOf(totalDays));
        avgDaysLabel.setText(String.format("Moyenne: %.1f jours", avgDays));
        utilizationRateLabel.setText(String.format("%.1f%%", Math.min(utilizationRate, 100)));

        // Update status
        boolean isCurrentlyRented = allLocations.stream()
            .anyMatch(l -> l.getStatus() == Location.Status.EN_COURS);
        statusLabel.setText("Statut: " + (isCurrentlyRented ? "En location" : "Disponible"));
    }

    private void updateCharts() {
        updateRevenueChart();
        updateStatusChart();
    }

    private void updateRevenueChart() {
        if (revenueChart == null) return;

        if (allLocations == null || allLocations.isEmpty()) {
            // Show empty chart
            XYChart.Series<String, Number> emptySeries = new XYChart.Series<>();
            emptySeries.setName("Revenus");
            emptySeries.getData().add(new XYChart.Data<>("Aucune donnée", 0));
            revenueChart.getData().clear();
            revenueChart.getData().add(emptySeries);
            return;
        }

        try {
            // Group locations by month
            Map<String, Double> monthlyRevenue = new HashMap<>();
            DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMM yyyy", java.util.Locale.FRENCH);

            for (Location location : allLocations) {
                if (location.getDateDebut() != null) {
                    String month = location.getDateDebut().format(monthFormatter);
                    monthlyRevenue.merge(month, location.getPrixTotal(), Double::sum);
                }
            }

            XYChart.Series<String, Number> series = new XYChart.Series<>();
            series.setName("Revenus");

            if (monthlyRevenue.isEmpty()) {
                series.getData().add(new XYChart.Data<>("Aucune donnée", 0));
            } else {
                monthlyRevenue.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> series.getData().add(new XYChart.Data<>(entry.getKey(), entry.getValue())));
            }

            revenueChart.getData().clear();
            revenueChart.getData().add(series);
        } catch (Exception e) {
            System.err.println("Error updating revenue chart: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateStatusChart() {
        if (statusChart == null) return;

        if (allLocations == null || allLocations.isEmpty()) {
            // Show empty chart
            statusChart.getData().clear();
            statusChart.getData().add(new PieChart.Data("Aucune donnée", 1));
            return;
        }

        try {
            Map<String, Long> statusCounts = allLocations.stream()
                .collect(Collectors.groupingBy(
                    l -> getStatusText(l.getStatus()),
                    Collectors.counting()
                ));

            statusChart.getData().clear();
            if (statusCounts.isEmpty()) {
                statusChart.getData().add(new PieChart.Data("Aucune donnée", 1));
            } else {
                statusCounts.forEach((status, count) ->
                    statusChart.getData().add(new PieChart.Data(status, count))
                );
            }
        } catch (Exception e) {
            System.err.println("Error updating status chart: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateTable() {
        if (historyTable != null && filteredLocations != null) {
            historyTable.setItems(filteredLocations);
        }
    }

    private String getStatusText(Location.Status status) {
        if (status == null) return "Inconnu";
        switch (status) {
            case RESERVE: return "Réservé";
            case EN_COURS: return "En cours";
            case TERMINE: return "Terminé";
            case ANNULE: return "Annulé";
            default: return "Inconnu";
        }
    }

    @FXML
    private void handleFilterChange() {
        if (filterComboBox == null || allLocations == null) return;

        String selectedFilter = filterComboBox.getValue();
        if ("Tous".equals(selectedFilter)) {
            filteredLocations = FXCollections.observableArrayList(allLocations);
        } else {
            filteredLocations = allLocations.stream()
                .filter(l -> getStatusText(l.getStatus()).equals(selectedFilter))
                .collect(Collectors.toCollection(FXCollections::observableArrayList));
        }
        updateTable();
        updateTableSummary();
    }

    @FXML
    private void handleRefresh() {
        loadData();
    }

    @FXML
    private void hideDetailPanel() {
        if (detailPanel != null) {
            detailPanel.setVisible(false);
        }
    }

    private void showLocationDetail(Location location) {
        if (detailPanel == null) return;

        // Update detail labels
        detailClientLabel.setText(location.getClient() != null ?
            location.getClient().getNom() + " " + location.getClient().getPrenom() : "N/A");
        detailPhoneLabel.setText(location.getClient() != null ?
            location.getClient().getTelephone() : "N/A");
        detailStartDateLabel.setText(location.getDateDebut() != null ?
            location.getDateDebut().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        detailEndDateLabel.setText(location.getDateFinPrevue() != null ?
            location.getDateFinPrevue().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        detailActualEndLabel.setText(location.getDateFinReelle() != null ?
            location.getDateFinReelle().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "En cours");

        if (location.getDateDebut() != null && location.getDateFinPrevue() != null) {
            long days = ChronoUnit.DAYS.between(location.getDateDebut(), location.getDateFinPrevue());
            detailDurationLabel.setText(days + " jours");
        } else {
            detailDurationLabel.setText("N/A");
        }

        detailPriceLabel.setText(String.format("%.2f €", location.getPrixTotal()));
        detailStatusLabel.setText(getStatusText(location.getStatus()));

        detailPanel.setVisible(true);
    }

    @FXML
    private void handleExportSelection() {
        System.out.println("Export selection requested");
        // TODO: Implement export selected rows functionality
        showInfoAlert("Export Sélection", "Fonctionnalité d'export de sélection à implémenter");
    }

    @FXML
    private void handleContactClient() {
        System.out.println("Contact client requested");
        // TODO: Implement contact client functionality
        showInfoAlert("Contacter Client", "Fonctionnalité de contact client à implémenter");
    }

    @FXML
    private void handleGenerateInvoice() {
        System.out.println("Generate invoice requested");
        // TODO: Implement invoice generation functionality
        showInfoAlert("Générer Facture", "Fonctionnalité de génération de facture à implémenter");
    }

    @FXML
    private void handleEditLocation() {
        System.out.println("Edit location requested");
        // TODO: Implement edit location functionality
        showInfoAlert("Modifier Location", "Fonctionnalité de modification de location à implémenter");
    }

    @FXML
    private void handleViewCalendar() {
        if (vehicule == null) return;

        try {
            // Close current window
            Stage currentStage = (Stage) vehicleInfoLabel.getScene().getWindow();

            // Open calendar view
            Platform.runLater(() -> {
                try {
                    FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/disponibilite.fxml"));
                    javafx.scene.Parent root = loader.load();

                    DisponibiliteController ctrl = loader.getController();
                    if (ctrl != null) {
                        ctrl.setVehicule(vehicule);
                    }

                    Stage dialog = new Stage();
                    dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
                    dialog.setScene(new javafx.scene.Scene(root, 1200, 800));
                    dialog.setTitle("Calendrier de Disponibilité - " + vehicule.getMarque() + " " + vehicule.getModele());

                    // Maximize window
                    javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
                    javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
                    dialog.setX(visualBounds.getMinX());
                    dialog.setY(visualBounds.getMinY());
                    dialog.setWidth(visualBounds.getWidth());
                    dialog.setHeight(visualBounds.getHeight() * 0.95);

                    dialog.show();
                    dialog.toFront();

                    // Close current window
                    currentStage.close();

                } catch (Exception e) {
                    System.err.println("Error opening calendar: " + e.getMessage());
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            System.err.println("Error handling view calendar: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void handleFullReport() {
        System.out.println("Full report requested");
        // TODO: Implement full report functionality
        showInfoAlert("Rapport Complet", "Fonctionnalité de rapport complet à implémenter");
    }

    private void showInfoAlert(String title, String message) {
        Platform.runLater(() -> {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    @FXML
    private void handleClose() {
        try {
            Stage stage = null;
            if (vehicleInfoLabel != null && vehicleInfoLabel.getScene() != null) {
                stage = (Stage) vehicleInfoLabel.getScene().getWindow();
            } else if (totalLocationsLabel != null && totalLocationsLabel.getScene() != null) {
                stage = (Stage) totalLocationsLabel.getScene().getWindow();
            }

            if (stage != null) {
                stage.close();
            } else {
                System.out.println("Warning: Could not find stage to close");
            }
        } catch (Exception e) {
            System.err.println("Error closing historique window: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void handleExport() {
        // Create export dialog
        Dialog<ExportOptions> dialog = new Dialog<>();
        dialog.setTitle("Exporter l'historique");
        dialog.setHeaderText("Choisissez les options d'export");

        // Set the button types
        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(exportButtonType, ButtonType.CANCEL);

        // Create the custom content
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> formatCombo = new ComboBox<>();
        formatCombo.getItems().addAll("CSV", "PDF");
        formatCombo.setValue("CSV");

        grid.add(new Label("Format:"), 0, 0);
        grid.add(formatCombo, 1, 0);

        dialog.getDialogPane().setContent(grid);

        // Request focus on the format combo by default
        Platform.runLater(() -> formatCombo.requestFocus());

        // Convert the result when the export button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == exportButtonType) {
                return new ExportOptions(formatCombo.getValue());
            }
            return null;
        });

        Optional<ExportOptions> result = dialog.showAndWait();

        result.ifPresent(options -> {
            try {
                // File chooser
                FileChooser fileChooser = new FileChooser();
                fileChooser.setTitle("Sauvegarder l'export de l'historique");
                
                String extension = "CSV".equals(options.getFormat()) ? "*.csv" : "*.pdf";
                String formatName = "CSV".equals(options.getFormat()) ? "CSV Files" : "PDF Files";
                fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter(formatName, extension));
                
                File file = fileChooser.showSaveDialog(vehicleInfoLabel.getScene().getWindow());
                
                if (file != null) {
                    // Get all locations for this vehicle
                    List<Location> locations = locationDAO.findAll().stream()
                        .filter(l -> l.getVehicule().equals(vehicule))
                        .collect(Collectors.toList());

                    String[] headers = {"ID", "Client", "Date début", "Date fin", "Prix total", "Statut"};
                    
                    if ("CSV".equals(options.getFormat())) {
                        ExportUtil.exportToCSV(locations, headers, location -> new String[]{
                            String.valueOf(location.getId()),
                            location.getClient() != null ? location.getClient().getNom() + " " + location.getClient().getPrenom() : "",
                            location.getDateDebut() != null ? location.getDateDebut().toString() : "",
                            location.getDateFinReelle() != null ? location.getDateFinReelle().toString() : 
                               (location.getDateFinPrevue() != null ? location.getDateFinPrevue().toString() : ""),
                            String.valueOf(location.getPrixTotal()),
                            location.getDateFinReelle() != null ? "Terminé" : "En cours"
                        }, file.getAbsolutePath());
                    } else {
                        ExportUtil.exportToPDF(locations, headers, location -> new String[]{
                            String.valueOf(location.getId()),
                            location.getClient() != null ? location.getClient().getNom() + " " + location.getClient().getPrenom() : "",
                            location.getDateDebut() != null ? location.getDateDebut().toString() : "",
                            location.getDateFinReelle() != null ? location.getDateFinReelle().toString() : 
                               (location.getDateFinPrevue() != null ? location.getDateFinPrevue().toString() : ""),
                            String.valueOf(location.getPrixTotal()),
                            location.getDateFinReelle() != null ? "Terminé" : "En cours"
                        }, file.getAbsolutePath());
                    }

                    javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
                    alert.setTitle("Export réussi");
                    alert.setHeaderText(null);
                    alert.setContentText("Export réussi!\nFichier sauvegardé: " + file.getAbsolutePath());
                    alert.showAndWait();
                }
            } catch (Exception e) {
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export: " + e.getMessage());
                alert.showAndWait();
            }
        });
    }


} 