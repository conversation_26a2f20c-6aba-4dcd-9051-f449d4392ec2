package controller;

import javafx.event.ActionEvent;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.stage.Stage;

public class NavigationController {
    public static void handleNavigation(ActionEvent event, Stage stage) {
        if (!(event.getSource() instanceof Button)) return;
        Button btn = (Button) event.getSource();
        String id = btn.getId();
        String fxml = null;
        switch (id) {
            case "btnDashboard":
                fxml = "/view/dashboard.fxml";
                break;
            case "btnClients":
                fxml = "/view/client.fxml";
                break;
            case "btnVehicules":
                fxml = "/view/vehicule.fxml";
                break;
            case "btnCatalogue":
                fxml = "/view/catalogue.fxml";
                break;
            case "btnLocations":
                fxml = "/view/location.fxml";
                break;
            case "btnPaiements":
                fxml = "/view/paiement.fxml";
                break;
            case "btnUserManagement":
                fxml = "/view/user_management.fxml";
                break;
            case "btnStats": // Example for a new page
                fxml = "/view/statistiques.fxml";
                break;
            // To add a new page, add a new case here with the button's fx:id and the FXML path
            default:
                return;
        }
        try {
            Parent root = FXMLLoader.load(NavigationController.class.getResource(fxml));
            stage.setScene(new Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 