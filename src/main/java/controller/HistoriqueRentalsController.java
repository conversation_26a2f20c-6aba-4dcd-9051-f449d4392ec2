package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import model.Location;
import model.Client;
import model.Vehicule;
import dao.LocationDAO;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

public class HistoriqueRentalsController {
    @FXML private TextField searchClientField;
    @FXML private TextField searchVehiculeField;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;
    @FXML private TableView<Location> rentalHistoryTable;
    @FXML private TableColumn<Location, Long> colId;
    @FXML private TableColumn<Location, String> colClient;
    @FXML private TableColumn<Location, String> colVehicule;
    @FXML private TableColumn<Location, String> colDateDebut;
    @FXML private TableColumn<Location, String> colDateFin;
    @FXML private TableColumn<Location, String> colStatut;
    @FXML private TableColumn<Location, String> colPrix;
    @FXML private Label detailsLabel;

    private final LocationDAO locationDAO = new LocationDAO();
    private ObservableList<Location> allLocations;

    @FXML
    public void initialize() {
        List<Location> locations = locationDAO.findAll();
        allLocations = FXCollections.observableArrayList(locations);
        colId.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        colClient.setCellValueFactory(data -> {
            Client c = data.getValue().getClient();
            return new javafx.beans.property.SimpleStringProperty(c != null ? c.getNom() + " " + c.getPrenom() : "");
        });
        colVehicule.setCellValueFactory(data -> {
            Vehicule v = data.getValue().getVehicule();
            return new javafx.beans.property.SimpleStringProperty(v != null ? v.getMarque() + " " + v.getModele() : "");
        });
        colDateDebut.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getDateDebut() != null ? data.getValue().getDateDebut().toString() : ""));
        colDateFin.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getDateFinReelle() != null ? data.getValue().getDateFinReelle().toString() : "En cours"));
        colStatut.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().getDateFinReelle() == null ? "En cours" : "Terminée"));
        colPrix.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(String.format("%.2f DH", data.getValue().getPrixTotal())));
        rentalHistoryTable.setItems(allLocations);
        rentalHistoryTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSel, newSel) -> showDetails(newSel));
    }

    @FXML
    private void handleFilter() {
        String client = searchClientField.getText().toLowerCase();
        String vehicule = searchVehiculeField.getText().toLowerCase();
        LocalDate debut = dateDebutFilter.getValue();
        LocalDate fin = dateFinFilter.getValue();
        List<Location> filtered = allLocations.stream().filter(l -> {
            boolean match = true;
            if (!client.isEmpty()) {
                match &= l.getClient() != null && (l.getClient().getNom() + " " + l.getClient().getPrenom()).toLowerCase().contains(client);
            }
            if (!vehicule.isEmpty()) {
                match &= l.getVehicule() != null && (l.getVehicule().getMarque() + " " + l.getVehicule().getModele()).toLowerCase().contains(vehicule);
            }
            if (debut != null) {
                match &= l.getDateDebut() != null && !l.getDateDebut().isBefore(debut);
            }
            if (fin != null) {
                match &= l.getDateFinReelle() != null && !l.getDateFinReelle().isAfter(fin);
            }
            return match;
        }).collect(Collectors.toList());
        rentalHistoryTable.setItems(FXCollections.observableArrayList(filtered));
    }

    @FXML
    private void handleClearFilters() {
        searchClientField.clear();
        searchVehiculeField.clear();
        dateDebutFilter.setValue(null);
        dateFinFilter.setValue(null);
        rentalHistoryTable.setItems(allLocations);
    }

    private void showDetails(Location l) {
        if (l == null) {
            detailsLabel.setText("Sélectionnez une location pour voir les détails.");
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("Client: ").append(l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "").append("\n");
        sb.append("Véhicule: ").append(l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "").append("\n");
        sb.append("Date début: ").append(l.getDateDebut() != null ? l.getDateDebut().toString() : "").append("\n");
        sb.append("Date fin: ").append(l.getDateFinReelle() != null ? l.getDateFinReelle().toString() : "En cours").append("\n");
        sb.append("Prix total: ").append(String.format("%.2f DH", l.getPrixTotal())).append("\n");
        detailsLabel.setText(sb.toString());
    }
} 