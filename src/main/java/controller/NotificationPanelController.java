package controller;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import model.Notification;
import model.User;
import service.NotificationService;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class NotificationPanelController implements Initializable {

    @FXML private Label lblTotalNotifications;
    @FXML private Label lblUnreadNotifications;
    @FXML private Label lblTodayNotifications;
    @FXML private Label lblUrgentNotifications;

    @FXML private ComboBox<String> cmbNotificationType;
    @FXML private ComboBox<String> cmbNotificationStatus;
    @FXML private TextField txtSearch;

    @FXML private Button btnMarkAllRead;
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnRappelRetour;
    @FXML private Button btnLocationConfirmee;
    @FXML private Button btnPaiementsDus;
    @FXML private Button btnMaintenanceDue;

    @FXML private VBox notificationsContainer;

    @FXML private Button btnPrevPage;
    @FXML private Button btnNextPage;
    @FXML private Label lblPageInfo;

    private NotificationService notificationService;
    private User currentUser;
    private List<Notification> allNotifications;
    private List<Notification> filteredNotifications;
    private int currentPage = 1;
    private final int itemsPerPage = 10;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        notificationService = NotificationService.getInstance();
        currentUser = (User) LoginController.loggedInUser;

        setupComboBoxes();
        setupSearchFilter();
        loadNotifications();
        updateStatistics();
    }

    private void setupComboBoxes() {
        // Notification types
        cmbNotificationType.setItems(FXCollections.observableArrayList(
            "Tous les types",
            "Rappel de retour",
            "Location confirmée",
            "Location réservée",
            "Paiement dû",
            "Paiement reçu",
            "Maintenance requise",
            "Véhicule disponible",
            "Bienvenue administrateur",
            "Alerte système"
        ));
        cmbNotificationType.setValue("Tous les types");

        // Notification status
        cmbNotificationStatus.setItems(FXCollections.observableArrayList(
            "Tous les statuts",
            "Non lues",
            "Lues",
            "Urgentes"
        ));
        cmbNotificationStatus.setValue("Tous les statuts");

        // Add listeners for filtering
        cmbNotificationType.setOnAction(e -> applyFilters());
        cmbNotificationStatus.setOnAction(e -> applyFilters());
    }

    private void setupSearchFilter() {
        txtSearch.textProperty().addListener((observable, oldValue, newValue) -> {
            Platform.runLater(this::applyFilters);
        });
    }

    private void loadNotifications() {
        if (currentUser == null) return;

        allNotifications = notificationService.getNotificationsForUser(currentUser);
        applyFilters();
    }

    private void applyFilters() {
        if (allNotifications == null) return;

        filteredNotifications = allNotifications.stream()
            .filter(this::matchesTypeFilter)
            .filter(this::matchesStatusFilter)
            .filter(this::matchesSearchFilter)
            .collect(Collectors.toList());

        currentPage = 1;
        displayNotifications();
        updatePagination();
    }

    private boolean matchesTypeFilter(Notification notification) {
        String selectedType = cmbNotificationType.getValue();
        if ("Tous les types".equals(selectedType)) return true;

        return switch (selectedType) {
            case "Rappel de retour" -> notification.getType() == Notification.NotificationType.RAPPEL_RETOUR;
            case "Location confirmée" -> notification.getType() == Notification.NotificationType.LOCATION_CONFIRMEE;
            case "Location réservée" -> notification.getType() == Notification.NotificationType.LOCATION_RESERVE;
            case "Paiement dû" -> notification.getType() == Notification.NotificationType.PAIEMENT_DU;
            case "Paiement reçu" -> notification.getType() == Notification.NotificationType.PAIEMENT_RECU;
            case "Maintenance requise" -> notification.getType() == Notification.NotificationType.MAINTENANCE_DUE;
            case "Véhicule disponible" -> notification.getType() == Notification.NotificationType.VEHICULE_DISPONIBLE;
            case "Bienvenue administrateur" -> notification.getType() == Notification.NotificationType.WELCOME_ADMIN;
            case "Alerte système" -> notification.getType() == Notification.NotificationType.SYSTEM_ALERT;
            default -> true;
        };
    }

    private boolean matchesStatusFilter(Notification notification) {
        String selectedStatus = cmbNotificationStatus.getValue();
        if ("Tous les statuts".equals(selectedStatus)) return true;

        return switch (selectedStatus) {
            case "Non lues" -> !notification.isRead();
            case "Lues" -> notification.isRead();
            case "Urgentes" -> notification.getPriority() == Notification.Priority.URGENT;
            default -> true;
        };
    }

    private boolean matchesSearchFilter(Notification notification) {
        String searchText = txtSearch.getText();
        if (searchText == null || searchText.trim().isEmpty()) return true;

        String searchLower = searchText.toLowerCase();
        return notification.getTitle().toLowerCase().contains(searchLower) ||
               notification.getMessage().toLowerCase().contains(searchLower);
    }

    private void displayNotifications() {
        notificationsContainer.getChildren().clear();

        if (filteredNotifications.isEmpty()) {
            showEmptyState();
            return;
        }

        int startIndex = (currentPage - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, filteredNotifications.size());

        for (int i = startIndex; i < endIndex; i++) {
            Notification notification = filteredNotifications.get(i);
            VBox notificationItem = createNotificationItem(notification);
            notificationsContainer.getChildren().add(notificationItem);
        }
    }

    private VBox createNotificationItem(Notification notification) {
        VBox item = new VBox();
        item.getStyleClass().add("notification-item");
        
        if (!notification.isRead()) {
            item.getStyleClass().add("unread");
        }
        if (notification.getPriority() == Notification.Priority.URGENT) {
            item.getStyleClass().add("urgent");
        }

        // Header row
        HBox headerRow = new HBox();
        headerRow.getStyleClass().add("notification-header-row");
        headerRow.setAlignment(Pos.CENTER_LEFT);

        Label iconLabel = new Label(notification.getTypeIcon());
        iconLabel.getStyleClass().add("notification-icon");

        Label titleLabel = new Label(notification.getTitle());
        titleLabel.getStyleClass().add("notification-title");

        Region spacer = new Region();
        HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        Label timeLabel = new Label(notification.getFormattedCreatedAt());
        timeLabel.getStyleClass().add("notification-time");

        // Type badge
        Label typeBadge = new Label(notification.getTypeDisplayName());
        typeBadge.getStyleClass().addAll("notification-type-badge", 
            notification.getType().name().toLowerCase().replace("_", "-"));

        headerRow.getChildren().addAll(iconLabel, titleLabel, spacer, typeBadge, timeLabel);

        // Message
        Label messageLabel = new Label(notification.getMessage());
        messageLabel.getStyleClass().add("notification-message");
        messageLabel.setWrapText(true);

        // Actions row
        HBox actionsRow = new HBox();
        actionsRow.getStyleClass().add("notification-actions");
        actionsRow.setAlignment(Pos.CENTER_RIGHT);

        if (!notification.isRead()) {
            Button markReadBtn = new Button("Marquer comme lu");
            markReadBtn.getStyleClass().addAll("btn-notification-action", "primary");
            markReadBtn.setOnAction(e -> markAsRead(notification));
            actionsRow.getChildren().add(markReadBtn);
        }

        if (notification.getActionUrl() != null) {
            Button actionBtn = new Button("Voir détails");
            actionBtn.getStyleClass().add("btn-notification-action");
            actionBtn.setOnAction(e -> handleNotificationAction(notification));
            actionsRow.getChildren().add(actionBtn);
        }

        Button deleteBtn = new Button("Supprimer");
        deleteBtn.getStyleClass().add("btn-notification-action");
        deleteBtn.setOnAction(e -> deleteNotification(notification));
        actionsRow.getChildren().add(deleteBtn);

        item.getChildren().addAll(headerRow, messageLabel, actionsRow);
        return item;
    }

    private void showEmptyState() {
        VBox emptyState = new VBox();
        emptyState.getStyleClass().add("empty-state");
        emptyState.setAlignment(Pos.CENTER);

        Label iconLabel = new Label("🔔");
        iconLabel.getStyleClass().add("empty-state-icon");

        Label titleLabel = new Label("Aucune notification");
        titleLabel.getStyleClass().add("empty-state-title");

        Label messageLabel = new Label("Vous n'avez aucune notification correspondant aux critères sélectionnés.");
        messageLabel.getStyleClass().add("empty-state-message");

        emptyState.getChildren().addAll(iconLabel, titleLabel, messageLabel);
        notificationsContainer.getChildren().add(emptyState);
    }

    private void updateStatistics() {
        if (currentUser == null || allNotifications == null) return;

        int total = allNotifications.size();
        int unread = (int) allNotifications.stream().filter(n -> !n.isRead()).count();
        int today = (int) allNotifications.stream()
            .filter(n -> n.getCreatedAt().toLocalDate().equals(LocalDate.now()))
            .count();
        int urgent = (int) allNotifications.stream()
            .filter(n -> n.getPriority() == Notification.Priority.URGENT)
            .count();

        lblTotalNotifications.setText(String.valueOf(total));
        lblUnreadNotifications.setText(String.valueOf(unread));
        lblTodayNotifications.setText(String.valueOf(today));
        lblUrgentNotifications.setText(String.valueOf(urgent));
    }

    private void updatePagination() {
        if (filteredNotifications == null) return;

        int totalPages = (int) Math.ceil((double) filteredNotifications.size() / itemsPerPage);
        totalPages = Math.max(1, totalPages);

        lblPageInfo.setText(String.format("Page %d sur %d", currentPage, totalPages));
        btnPrevPage.setDisable(currentPage <= 1);
        btnNextPage.setDisable(currentPage >= totalPages);
    }

    private void markAsRead(Notification notification) {
        notificationService.markAsRead(notification.getId());
        notification.markAsRead();
        loadNotifications();
        updateStatistics();
    }

    private void handleNotificationAction(Notification notification) {
        // Navigate to the appropriate page
        if (notification.getActionUrl() != null) {
            // This would need to be implemented to navigate to the correct page
            // For now, just mark as read
            markAsRead(notification);
        }
    }

    private void deleteNotification(Notification notification) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmer la suppression");
        alert.setHeaderText("Supprimer la notification");
        alert.setContentText("Êtes-vous sûr de vouloir supprimer cette notification ?");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // Delete notification (would need to implement in service)
                loadNotifications();
                updateStatistics();
            }
        });
    }

    @FXML
    private void handleMarkAllAsRead() {
        if (currentUser != null) {
            notificationService.markAllAsReadForUser(currentUser);
            loadNotifications();
            updateStatistics();
        }
    }

    @FXML
    private void handleRefresh() {
        loadNotifications();
        updateStatistics();
    }

    @FXML
    private void handleClearFilters() {
        cmbNotificationType.setValue("Tous les types");
        cmbNotificationStatus.setValue("Tous les statuts");
        txtSearch.clear();
        applyFilters();
    }

    @FXML
    private void handleRappelRetour() {
        cmbNotificationType.setValue("Rappel de retour");
        applyFilters();
    }

    @FXML
    private void handleLocationConfirmee() {
        cmbNotificationType.setValue("Location confirmée");
        applyFilters();
    }

    @FXML
    private void handlePaiementsDus() {
        cmbNotificationType.setValue("Paiement dû");
        applyFilters();
    }

    @FXML
    private void handleMaintenanceDue() {
        cmbNotificationType.setValue("Maintenance requise");
        applyFilters();
    }

    @FXML
    private void handlePrevPage() {
        if (currentPage > 1) {
            currentPage--;
            displayNotifications();
            updatePagination();
        }
    }

    @FXML
    private void handleNextPage() {
        int totalPages = (int) Math.ceil((double) filteredNotifications.size() / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            displayNotifications();
            updatePagination();
        }
    }
}
