package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.LocationDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import model.Location;
import model.Client;
import model.Vehicule;
import java.util.List;
import java.util.stream.Collectors;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import java.time.LocalDate;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import javafx.stage.FileChooser;
import java.io.File;
import java.io.IOException;
import javafx.beans.property.SimpleStringProperty;
import javafx.geometry.Pos;
import javafx.geometry.Insets;
import javafx.stage.Modality;
import javafx.scene.Parent;
import javafx.scene.Scene;

public class LocationController {
    @FXML
    private TableView<Location> locationTable;
    @FXML
    private TableColumn<Location, Long> idColumn;
    @FXML
    private TableColumn<Location, String> clientColumn;
    @FXML
    private TableColumn<Location, String> vehiculeColumn;
    @FXML
    private TableColumn<Location, String> dateDebutColumn;
    @FXML
    private TableColumn<Location, String> dateFinColumn;
    @FXML
    private TableColumn<Location, String> statutColumn;
    @FXML
    private TextField searchField;
    @FXML
    private Label lblTotalCount;
    
    // Form elements
    @FXML private ComboBox<Client> txtClientForm;
    @FXML private ComboBox<Vehicule> txtVehiculeForm;
    @FXML private DatePicker txtDateDebutForm;
    @FXML private DatePicker txtDateFinForm;
    @FXML private ComboBox<String> txtStatutForm;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;

    private final LocationDAO locationDAO = new LocationDAO();
    private final ClientDAO clientDAO = new ClientDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private ObservableList<Location> locationList;
    private Location selectedLocation = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        clientColumn.setCellValueFactory(data -> {
            model.Client c = data.getValue().getClient();
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        vehiculeColumn.setCellValueFactory(data -> {
            model.Vehicule v = data.getValue().getVehicule();
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        dateDebutColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateDebut();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        dateFinColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateFinPrevue();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        statutColumn.setCellValueFactory(data -> {
            model.Location l = data.getValue();
            String statut = l.getStatus() != null ? l.getStatus().name() : (l.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
            return new javafx.beans.property.SimpleStringProperty(statut);
        });
        
        // Setup combo boxes
        ObservableList<String> statuts = FXCollections.observableArrayList("En cours", "Terminée", "Annulée");
        txtStatutForm.setItems(statuts);
        
        // Add table selection listener with error handling
        locationTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedLocation = newSelection;
                    loadLocationToForm(newSelection);
                    isEditMode = true;
                    btnSave.setText("Modifier");
                } else {
                    clearForm();
                }
            } catch (Exception e) {
                System.err.println("Error in location selection: " + e.getMessage());
                clearForm();
            }
        });
        
        loadLocations();
        loadComboBoxes();
        clearForm();
    }

    private void loadLocations() {
        List<Location> list = locationDAO.findAll();
        locationList = FXCollections.observableArrayList(list);
        locationTable.setItems(locationList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + list.size());
        }
    }

    private void loadComboBoxes() {
        // Load clients
        List<Client> clients = clientDAO.findAll();
        txtClientForm.setItems(FXCollections.observableArrayList(clients));
        txtClientForm.setCellFactory(param -> new ListCell<Client>() {
            @Override
            protected void updateItem(Client item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getNom() + " " + item.getPrenom() + (item.getEmail() != null && !item.getEmail().isBlank() ? " (" + item.getEmail() + ")" : ""));
                }
            }
        });
        txtClientForm.setButtonCell(txtClientForm.getCellFactory().call(null));
        txtClientForm.setConverter(new javafx.util.StringConverter<Client>() {
            @Override
            public String toString(Client c) {
                if (c == null) return "";
                return c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : "");
            }
            @Override
            public Client fromString(String s) { return null; }
        });

        // Load vehicles
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        txtVehiculeForm.setItems(FXCollections.observableArrayList(vehicules));
        txtVehiculeForm.setCellFactory(param -> new ListCell<Vehicule>() {
            @Override
            protected void updateItem(Vehicule item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMarque() + " " + item.getModele() + (item.getImmatriculation() != null ? " (" + item.getImmatriculation() + ")" : ""));
                }
            }
        });
        txtVehiculeForm.setButtonCell(txtVehiculeForm.getCellFactory().call(null));
        txtVehiculeForm.setConverter(new javafx.util.StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule v) {
                if (v == null) return "";
                return v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : "");
            }
            @Override
            public Vehicule fromString(String s) { return null; }
        });
    }

    private void loadLocationToForm(Location location) {
        if (location == null) {
            clearForm();
            return;
        }

        // Use null-safe operations for all fields
        txtClientForm.setValue(location.getClient());
        txtVehiculeForm.setValue(location.getVehicule());
        txtDateDebutForm.setValue(location.getDateDebut());
        txtDateFinForm.setValue(location.getDateFinPrevue());

        // Handle status safely
        String statut;
        if (location.getStatus() != null) {
            statut = location.getStatus().name();
        } else {
            statut = (location.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
        }
        txtStatutForm.setValue(statut);
    }

    private void clearForm() {
        txtClientForm.setValue(null);
        txtVehiculeForm.setValue(null);
        txtDateDebutForm.setValue(null);
        txtDateFinForm.setValue(null);
        txtStatutForm.setValue(null);
        selectedLocation = null;
        isEditMode = false;
        btnSave.setText("Enregistrer");
    }

    @FXML
    private void handleAjouter() {
        clearForm();
    }

    @FXML
    private void handleModifier() {
        // No longer needed, but keep for compatibility
        if (selectedLocation != null) {
            isEditMode = true;
            btnSave.setText("Modifier");
        } else {
            showAlert("Veuillez sélectionner une location à modifier", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSupprimer() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Supprimer la location");
            alert.setContentText("Êtes-vous sûr de vouloir supprimer cette location ?");
            
            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        locationDAO.delete(selected);
                        loadLocations();
                        clearForm();
                        showAlert("Location supprimée avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de la suppression: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner une location à supprimer", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSave() {
        Client client = txtClientForm.getValue();
        Vehicule vehicule = txtVehiculeForm.getValue();
        LocalDate dateDebut = txtDateDebutForm.getValue();
        LocalDate dateFin = txtDateFinForm.getValue();
        String statut = txtStatutForm.getValue();

        if (client == null || vehicule == null || dateDebut == null || dateFin == null) {
            showAlert("Veuillez remplir tous les champs obligatoires", AlertType.WARNING);
            return;
        }

        if (dateFin.isBefore(dateDebut)) {
            showAlert("La date de fin doit être postérieure à la date de début", AlertType.WARNING);
            return;
        }

        Long excludeId = isEditMode && selectedLocation != null ? selectedLocation.getId() : null;
        boolean available = locationDAO.isVehiculeAvailable(
            vehicule.getId(), dateDebut, dateFin, excludeId
        );
        if (!available) {
            showAlert("Ce véhicule est déjà réservé pour cette période.", AlertType.ERROR);
            return;
        }

        try {
            if (isEditMode && selectedLocation != null) {
                // Update existing location
                selectedLocation.setClient(client);
                selectedLocation.setVehicule(vehicule);
                selectedLocation.setDateDebut(dateDebut);
                selectedLocation.setDateFinPrevue(dateFin);
                if ("Terminée".equals(statut)) {
                    selectedLocation.setDateFinReelle(LocalDate.now());
                }
                selectedLocation.updateStatus();
                locationDAO.save(selectedLocation);
                showAlert("Location modifiée avec succès", AlertType.INFORMATION);
            } else {
                // Create new location
                Location newLocation = new Location();
                newLocation.setClient(client);
                newLocation.setVehicule(vehicule);
                newLocation.setDateDebut(dateDebut);
                newLocation.setDateFinPrevue(dateFin);
                newLocation.updateStatus();
                locationDAO.save(newLocation);
                showAlert("Location ajoutée avec succès", AlertType.INFORMATION);
            }
            loadLocations();
            clearForm();
        } catch (Exception e) {
            showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), AlertType.ERROR);
        }
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    @FXML
    private void handleRefresh() {
        searchField.clear();
        loadLocations();
        clearForm();
    }

    @FXML
    private void handleExport() {
        Alert alert = new Alert(AlertType.INFORMATION);
        alert.setTitle("Export");
        alert.setHeaderText(null);
        alert.setContentText("Fonction d'export non implémentée (stub).");
        alert.showAndWait();
    }

    @FXML
    private void handleExportContract() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une location pour exporter le contrat", AlertType.WARNING);
            return;
        }

        try {
            // Use the same export logic as in LocationCreateController
            exportLocationContract(selected);
        } catch (Exception e) {
            showAlert("Erreur lors de l'export du contrat: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleRechercher() {
        String keyword = searchField.getText().toLowerCase();
        if (keyword.isEmpty()) {
            loadLocations();
            return;
        }
        List<Location> filtered = locationDAO.findAll().stream()
                .filter(l -> {
                    String client = l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "";
                    String vehicule = l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "";
                    String statut = l.getStatus() != null ? l.getStatus().name() : (l.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
                    return client.toLowerCase().contains(keyword)
                        || vehicule.toLowerCase().contains(keyword)
                        || statut.toLowerCase().contains(keyword);
                })
                .collect(Collectors.toList());
        locationList.setAll(filtered);
        locationTable.setItems(locationList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filtered.size());
        }
    }

    @FXML
    private void handleClearFilters() {
        searchField.clear();
        loadLocations();
    }

    @FXML
    private void handleAnnulerLocation() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (selected.getDateFinReelle() != null || (selected.getStatus() != null && selected.getStatus() == model.Location.Status.TERMINE)) {
                showAlert("Impossible d'annuler une location déjà terminée.", AlertType.WARNING);
                return;
            }
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Annuler la location");
            alert.setContentText("Êtes-vous sûr de vouloir annuler cette location ?");
            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        selected.setStatus(model.Location.Status.ANNULE);
                        selected.setDateFinReelle(java.time.LocalDate.now());
                        Vehicule v = selected.getVehicule();
                        if (v != null) {
                            v.setEtat("Disponible");
                            new dao.VehiculeDAO().save(v);
                        }
                        locationDAO.save(selected);
                        loadLocations();
                        showAlert("Location annulée avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de l'annulation: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner une location à annuler", AlertType.WARNING);
        }
    }

    // Extension logic for admin/agent
    @FXML
    private void handleProlongerLocation() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une location à prolonger", AlertType.WARNING);
            return;
        }
        if (selected.getStatus() == model.Location.Status.ANNULE || selected.getStatus() == model.Location.Status.TERMINE) {
            showAlert("Impossible de prolonger une location annulée ou terminée.", AlertType.WARNING);
            return;
        }
        TextInputDialog dialog = new TextInputDialog(selected.getDateFinPrevue() != null ? selected.getDateFinPrevue().toString() : "");
        dialog.setTitle("Prolonger la location");
        dialog.setHeaderText("Nouvelle date de fin prévue");
        dialog.setContentText("Entrez la nouvelle date de fin (AAAA-MM-JJ):");
        dialog.showAndWait().ifPresent(input -> {
            try {
                java.time.LocalDate newDateFin = java.time.LocalDate.parse(input);
                if (newDateFin.isBefore(selected.getDateDebut())) {
                    showAlert("La nouvelle date de fin doit être postérieure à la date de début.", AlertType.ERROR);
                    return;
                }
                boolean available = locationDAO.isVehiculeAvailable(
                    selected.getVehicule().getId(),
                    selected.getDateDebut(),
                    newDateFin,
                    selected.getId()
                );
                if (!available) {
                    showAlert("Ce véhicule est déjà réservé pour cette période.", AlertType.ERROR);
                    return;
                }
                selected.setDateFinPrevue(newDateFin);
                selected.updateStatus();
                locationDAO.save(selected);
                loadLocations();
                showAlert("Location prolongée avec succès", AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Date invalide ou erreur: " + e.getMessage(), AlertType.ERROR);
            }
        });
    }

    @FXML
    private void handleViewDetails() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une location à visualiser", AlertType.WARNING);
            return;
        }
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location_detail.fxml"));
            javafx.scene.Parent detailRoot = loader.load();
            controller.LocationDetailController detailController = loader.getController();
            detailController.setLocation(selected);
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Détails de la Location");
            stage.setScene(new javafx.scene.Scene(detailRoot));
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            stage.setX(visualBounds.getMinX());
            stage.setY(visualBounds.getMinY());
            stage.setWidth(visualBounds.getWidth());
            stage.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            stage.show();
        } catch (Exception e) {
            showAlert("Erreur lors de l'ouverture des détails: " + e.getMessage(), AlertType.ERROR);
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Extension logic stub (to be implemented in UI)
    // public void handleProlongerLocation() { ... }

    private void exportLocationContract(Location location) {
        try {
            // File chooser for PDF
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Sauvegarder le contrat de location");
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
            fileChooser.setInitialFileName("Contrat_Location_" + location.getId() + ".pdf");

            File file = fileChooser.showSaveDialog(locationTable.getScene().getWindow());

            if (file != null) {
                generateLocationContractPDF(location, file);

                Alert alert = new Alert(AlertType.INFORMATION);
                alert.setTitle("Contrat exporté");
                alert.setHeaderText("Succès");
                alert.setContentText("Le contrat a été exporté avec succès vers:\n" + file.getAbsolutePath());
                alert.showAndWait();
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export du contrat: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void generateLocationContractPDF(Location location, File file) throws IOException {
        try (org.apache.pdfbox.pdmodel.PDDocument doc = new org.apache.pdfbox.pdmodel.PDDocument()) {
            org.apache.pdfbox.pdmodel.PDPage page = new org.apache.pdfbox.pdmodel.PDPage(org.apache.pdfbox.pdmodel.common.PDRectangle.A4);
            doc.addPage(page);
            org.apache.pdfbox.pdmodel.PDPageContentStream content = new org.apache.pdfbox.pdmodel.PDPageContentStream(doc, page);

            float y = page.getMediaBox().getHeight() - 40;
            float margin = 40;

            // Title
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 20);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("CONTRAT DE LOCATION DE VÉHICULE");
            content.endText();
            y -= 40;

            // Contract number and date
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Numéro de contrat: LOC-" + location.getId());
            content.endText();
            y -= 25;

            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date: " + java.time.LocalDate.now().toString());
            content.endText();
            y -= 40;

            // Client information
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("INFORMATIONS DU CLIENT");
            content.endText();
            y -= 25;

            model.Client client = location.getClient();
            if (client != null) {
                content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Nom: " + (client.getNom() != null ? client.getNom() : "") + " " + (client.getPrenom() != null ? client.getPrenom() : ""));
                content.endText();
                y -= 20;

                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("CIN: " + (client.getCin() != null ? client.getCin() : ""));
                content.endText();
                y -= 20;

                if (client.getTelephone() != null && !client.getTelephone().isEmpty()) {
                    content.beginText();
                    content.newLineAtOffset(margin, y);
                    content.showText("Téléphone: " + client.getTelephone());
                    content.endText();
                    y -= 20;
                }

                if (client.getEmail() != null && !client.getEmail().isEmpty()) {
                    content.beginText();
                    content.newLineAtOffset(margin, y);
                    content.showText("Email: " + client.getEmail());
                    content.endText();
                    y -= 20;
                }
            }

            y -= 20;

            // Vehicle information
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("INFORMATIONS DU VÉHICULE");
            content.endText();
            y -= 25;

            model.Vehicule vehicule = location.getVehicule();
            if (vehicule != null) {
                content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Marque: " + (vehicule.getMarque() != null ? vehicule.getMarque() : ""));
                content.endText();
                y -= 20;

                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Modèle: " + (vehicule.getModele() != null ? vehicule.getModele() : ""));
                content.endText();
                y -= 20;

                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Immatriculation: " + (vehicule.getImmatriculation() != null ? vehicule.getImmatriculation() : ""));
                content.endText();
                y -= 20;

                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Prix par jour: " + (vehicule.getPrixParJour() != null ? vehicule.getPrixParJour() : 0.0) + " DH");
                content.endText();
                y -= 40;
            }

            // Rental details
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("DÉTAILS DE LA LOCATION");
            content.endText();
            y -= 25;

            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date de début: " + (location.getDateDebut() != null ? location.getDateDebut().toString() : "À définir"));
            content.endText();
            y -= 20;

            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date de fin prévue: " + (location.getDateFinPrevue() != null ? location.getDateFinPrevue().toString() : "À définir"));
            content.endText();
            y -= 20;

            if (location.getDateFinReelle() != null) {
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Date de fin réelle: " + location.getDateFinReelle().toString());
                content.endText();
                y -= 20;
            }

            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Statut: " + (location.getStatus() != null ? location.getStatus().name() : "EN_COURS"));
            content.endText();
            y -= 40;

            // Total price
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("MONTANT TOTAL: " + location.getPrixTotal() + " DH");
            content.endText();
            y -= 20;

            if (location.getPenalite() > 0) {
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Pénalité: " + location.getPenalite() + " DH");
                content.endText();
                y -= 20;
            }

            content.close();
            doc.save(file.getAbsolutePath());
        }
    }

    @FXML
    private void handleRentalHistory() {
        try {
            System.out.println("Opening rental history...");

            // Load the rental history FXML
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/historique_rentals.fxml"));
            javafx.scene.Parent root = loader.load();

            // Create new stage for rental history
            Stage historyStage = new Stage();
            historyStage.setTitle("Historique Complet des Locations");
            historyStage.setScene(new javafx.scene.Scene(root, 1200, 800));
            historyStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);

            // Maximize window
            javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
            javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
            historyStage.setX(visualBounds.getMinX());
            historyStage.setY(visualBounds.getMinY());
            historyStage.setWidth(visualBounds.getWidth());
            historyStage.setHeight(visualBounds.getHeight() * 0.95);

            historyStage.show();

        } catch (Exception e) {
            System.err.println("Error opening rental history: " + e.getMessage());
            e.printStackTrace();

            // Fallback: Create a simple dialog with rental statistics
            createRentalHistoryDialog();
        }
    }

    private void createRentalHistoryDialog() {
        try {
            Dialog<Void> dialog = new Dialog<>();
            dialog.setTitle("Historique des Locations");
            dialog.setHeaderText("Analyse complète des locations");

            // Create content
            javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(15);
            content.setPadding(new javafx.geometry.Insets(20));
            content.setStyle("-fx-background-color: #f8fafc;");

            // Statistics section
            javafx.scene.layout.HBox statsBox = new javafx.scene.layout.HBox(20);
            statsBox.setAlignment(javafx.geometry.Pos.CENTER);

            // Get location statistics
            List<Location> allLocations = locationDAO.findAll();
            long totalLocations = allLocations.size();
            long activeLocations = allLocations.stream().filter(l -> Location.Status.EN_COURS.equals(l.getStatus())).count();
            long completedLocations = allLocations.stream().filter(l -> Location.Status.TERMINE.equals(l.getStatus())).count();
            double totalRevenue = allLocations.stream().mapToDouble(Location::getPrixTotal).sum();

            // Create stat cards
            javafx.scene.layout.VBox totalCard = createStatCard("📊 Total Locations", String.valueOf(totalLocations), "#3b82f6");
            javafx.scene.layout.VBox activeCard = createStatCard("🔄 En Cours", String.valueOf(activeLocations), "#f59e0b");
            javafx.scene.layout.VBox completedCard = createStatCard("✅ Terminées", String.valueOf(completedLocations), "#10b981");
            javafx.scene.layout.VBox revenueCard = createStatCard("💰 Revenus Total", String.format("%.2f DH", totalRevenue), "#8b5cf6");

            statsBox.getChildren().addAll(totalCard, activeCard, completedCard, revenueCard);

            // Recent locations table
            Label recentLabel = new Label("Locations Récentes");
            recentLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;");

            TableView<Location> historyTable = new TableView<>();
            historyTable.setPrefHeight(300);

            // Setup columns
            TableColumn<Location, String> clientCol = new TableColumn<>("Client");
            clientCol.setPrefWidth(150);
            clientCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getClient() != null ?
                    cellData.getValue().getClient().getNom() + " " + cellData.getValue().getClient().getPrenom() :
                    "N/A"
                )
            );

            TableColumn<Location, String> vehiculeCol = new TableColumn<>("Véhicule");
            vehiculeCol.setPrefWidth(150);
            vehiculeCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getVehicule() != null ?
                    cellData.getValue().getVehicule().getMarque() + " " + cellData.getValue().getVehicule().getModele() :
                    "N/A"
                )
            );

            TableColumn<Location, String> dateCol = new TableColumn<>("Date Début");
            dateCol.setPrefWidth(120);
            dateCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getDateDebut() != null ?
                    cellData.getValue().getDateDebut().toString() : "N/A"
                )
            );

            TableColumn<Location, String> statutCol = new TableColumn<>("Statut");
            statutCol.setPrefWidth(100);
            statutCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getStatus() != null ?
                    cellData.getValue().getStatus().toString() : "N/A"
                )
            );

            TableColumn<Location, String> prixCol = new TableColumn<>("Prix Total");
            prixCol.setPrefWidth(100);
            prixCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(String.format("%.2f DH", cellData.getValue().getPrixTotal()))
            );

            historyTable.getColumns().addAll(clientCol, vehiculeCol, dateCol, statutCol, prixCol);

            // Load recent locations (last 20)
            List<Location> recentLocations = allLocations.stream()
                .sorted((l1, l2) -> {
                    if (l1.getDateDebut() == null && l2.getDateDebut() == null) return 0;
                    if (l1.getDateDebut() == null) return 1;
                    if (l2.getDateDebut() == null) return -1;
                    return l2.getDateDebut().compareTo(l1.getDateDebut());
                })
                .limit(20)
                .collect(Collectors.toList());

            historyTable.setItems(FXCollections.observableArrayList(recentLocations));

            content.getChildren().addAll(statsBox, recentLabel, historyTable);

            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

            // Set dialog size
            dialog.getDialogPane().setPrefSize(900, 700);

            dialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error creating rental history dialog: " + e.getMessage());
            e.printStackTrace();

            Alert alert = new Alert(AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture de l'historique");
            alert.setContentText("Une erreur s'est produite: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private javafx.scene.layout.VBox createStatCard(String title, String value, String color) {
        javafx.scene.layout.VBox card = new javafx.scene.layout.VBox(8);
        card.setAlignment(javafx.geometry.Pos.CENTER);
        card.setStyle("-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; " +
                     "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }
}
