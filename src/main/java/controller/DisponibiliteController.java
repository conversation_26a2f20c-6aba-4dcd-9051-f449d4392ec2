package controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.control.Label;
import javafx.scene.control.Button;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import model.Vehicule;
import model.Location;
import dao.LocationDAO;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import javafx.application.Platform;

public class DisponibiliteController {
    // FXML Components
    @FXML private Label vehicleInfoLabel;
    @FXML private Label currentStatusLabel;
    @FXML private Label nextAvailableLabel;
    @FXML private GridPane calendarGrid;
    @FXML private Label lblCalendarMonth;
    @FXML private VBox selectedDateInfo;
    @FXML private Label lblSelectedDate;
    @FXML private Label lblSelectedDateStatus;
    @FXML private Label lblSelectedDateDetails;
    @FXML private Label monthlyStatsLabel;
    @FXML private Label monthlyRevenueLabel;
    @FXML private Label occupancyRateLabel;

    // Data
    private Vehicule vehicule;
    private final LocationDAO locationDAO = new LocationDAO();
    private LocalDate currentCalendarMonth;
    private Map<LocalDate, Location> locationsByDate;
    private LocalDate selectedDate;

    @FXML
    private void initialize() {
        System.out.println("=== INITIALIZING DISPONIBILITE CONTROLLER ===");

        try {
            // Initialize with current month
            currentCalendarMonth = LocalDate.now().withDayOfMonth(1);
            locationsByDate = new HashMap<>();

            // Initialize immediately, then use Platform.runLater for UI updates
            initializeComponents();

            // Schedule UI updates for next JavaFX pulse
            Platform.runLater(() -> {
                try {
                    updateUIComponents();
                    System.out.println("=== DISPONIBILITE CONTROLLER UI UPDATED SUCCESSFULLY ===");
                } catch (Exception e) {
                    System.err.println("ERROR updating UI components: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            System.out.println("=== DISPONIBILITE CONTROLLER INITIALIZED SUCCESSFULLY ===");
        } catch (Exception e) {
            System.err.println("ERROR initializing DisponibiliteController: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeComponents() {
        // Set default text immediately
        if (vehicleInfoLabel != null) {
            vehicleInfoLabel.setText("🚗 Calendrier de Disponibilité - Prêt à utiliser");
            System.out.println("Vehicle info label set successfully");
        } else {
            System.out.println("WARNING: vehicleInfoLabel is null!");
        }

        if (lblCalendarMonth != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM yyyy", java.util.Locale.FRENCH);
            lblCalendarMonth.setText(currentCalendarMonth.format(formatter));
            System.out.println("Calendar month label set successfully");
        } else {
            System.out.println("WARNING: lblCalendarMonth is null!");
        }

        // Hide selected date info initially
        if (selectedDateInfo != null) {
            selectedDateInfo.setVisible(false);
        } else {
            System.out.println("WARNING: selectedDateInfo is null!");
        }
    }

    private void updateUIComponents() {
        // Force update calendar display
        updateCalendarDisplay();

        // Ensure all components are visible
        if (vehicleInfoLabel != null && vehicleInfoLabel.getParent() != null) {
            vehicleInfoLabel.getParent().setVisible(true);
        }

        if (lblCalendarMonth != null && lblCalendarMonth.getParent() != null) {
            lblCalendarMonth.getParent().setVisible(true);
        }

        System.out.println("UI components updated and made visible");
    }

    public void setVehicule(Vehicule v) {
        this.vehicule = v;
        System.out.println("Setting vehicle: " + v.getMarque() + " " + v.getModele());

        Platform.runLater(() -> {
            try {
                // Update vehicle info
                if (vehicleInfoLabel != null) {
                    vehicleInfoLabel.setText("🚗 " + v.getMarque() + " " + v.getModele() + " (" + v.getImmatriculation() + ")");
                }

                // Initialize calendar with current month
                if (currentCalendarMonth == null) {
                    currentCalendarMonth = LocalDate.now().withDayOfMonth(1);
                }

                // Load real location data
                loadLocationData();

                // Update all displays
                updateCalendarDisplay();
                updateVehicleStatus();
                updateMonthlyStats();

                System.out.println("Vehicle data loaded successfully");
            } catch (Exception e) {
                System.err.println("Error setting vehicle: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void loadLocationData() {
        if (vehicule == null) {
            locationsByDate = new HashMap<>();
            return;
        }

        try {
            System.out.println("Loading location data for vehicle ID: " + vehicule.getId());
            List<Location> locations = locationDAO.findByVehiculeId(vehicule.getId());
            locationsByDate = new HashMap<>();

            System.out.println("Found " + locations.size() + " locations for this vehicle");

            for (Location location : locations) {
                // Update status based on current date
                location.updateStatus();

                // Map each date in the location period to the location
                LocalDate start = location.getDateDebut();
                LocalDate end = location.getDateFinPrevue();

                if (start != null && end != null) {
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        locationsByDate.put(current, location);
                        current = current.plusDays(1);
                    }
                }
            }

            System.out.println("Location data loaded: " + locationsByDate.size() + " date entries");
        } catch (Exception e) {
            System.err.println("Error loading location data: " + e.getMessage());
            e.printStackTrace();
            locationsByDate = new HashMap<>();
        }
    }

    private void updateVehicleStatus() {
        if (vehicule == null) return;

        try {
            // Check current status
            LocalDate today = LocalDate.now();
            Location currentLocation = locationsByDate.get(today);

            if (currentLocation != null) {
                Location.Status status = currentLocation.getStatus();
                switch (status) {
                    case EN_COURS:
                        if (currentStatusLabel != null) {
                            currentStatusLabel.setText("Statut: 🔴 En cours de location");
                            currentStatusLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #dc2626;");
                        }
                        break;
                    case RESERVE:
                        if (currentStatusLabel != null) {
                            currentStatusLabel.setText("Statut: 🟠 Réservé");
                            currentStatusLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #d97706;");
                        }
                        break;
                    default:
                        if (currentStatusLabel != null) {
                            currentStatusLabel.setText("Statut: 🟢 Disponible");
                            currentStatusLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #059669;");
                        }
                        break;
                }
            } else {
                if (currentStatusLabel != null) {
                    currentStatusLabel.setText("Statut: 🟢 Disponible");
                    currentStatusLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #059669;");
                }
            }

            // Find next available date if currently unavailable
            if (currentLocation != null && (currentLocation.getStatus() == Location.Status.EN_COURS || currentLocation.getStatus() == Location.Status.RESERVE)) {
                LocalDate nextAvailable = findNextAvailableDate(today);
                if (nextAvailable != null && nextAvailableLabel != null) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    nextAvailableLabel.setText("Disponible à partir du: " + nextAvailable.format(formatter));
                } else if (nextAvailableLabel != null) {
                    nextAvailableLabel.setText("Prochaine disponibilité: À déterminer");
                }
            } else if (nextAvailableLabel != null) {
                nextAvailableLabel.setText("Disponible immédiatement");
            }

        } catch (Exception e) {
            System.err.println("Error updating vehicle status: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private LocalDate findNextAvailableDate(LocalDate fromDate) {
        LocalDate checkDate = fromDate.plusDays(1);
        LocalDate maxDate = fromDate.plusMonths(6); // Check up to 6 months ahead

        while (!checkDate.isAfter(maxDate)) {
            if (!locationsByDate.containsKey(checkDate)) {
                return checkDate;
            }
            checkDate = checkDate.plusDays(1);
        }

        return null; // No availability found in next 6 months
    }

    private void updateMonthlyStats() {
        if (vehicule == null || currentCalendarMonth == null) return;

        try {
            // Calculate stats for current displayed month
            LocalDate monthStart = currentCalendarMonth;
            LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);

            int locationsThisMonth = 0;
            double revenueThisMonth = 0.0;
            int occupiedDays = 0;
            int totalDaysInMonth = monthStart.lengthOfMonth();

            // Count locations and revenue for this month
            for (Map.Entry<LocalDate, Location> entry : locationsByDate.entrySet()) {
                LocalDate date = entry.getKey();
                Location location = entry.getValue();

                if (!date.isBefore(monthStart) && !date.isAfter(monthEnd)) {
                    occupiedDays++;

                    // Count unique locations (avoid double counting multi-day rentals)
                    if (date.equals(location.getDateDebut())) {
                        locationsThisMonth++;
                        revenueThisMonth += location.getPrixTotal();
                    }
                }
            }

            // Calculate occupancy rate
            double occupancyRate = totalDaysInMonth > 0 ? (double) occupiedDays / totalDaysInMonth * 100 : 0;

            // Update labels
            if (monthlyStatsLabel != null) {
                monthlyStatsLabel.setText(locationsThisMonth + " location" + (locationsThisMonth != 1 ? "s" : ""));
            }

            if (monthlyRevenueLabel != null) {
                monthlyRevenueLabel.setText(String.format("%.2f €", revenueThisMonth));
            }

            if (occupancyRateLabel != null) {
                occupancyRateLabel.setText(String.format("%.1f%%", occupancyRate));

                // Color code based on occupancy rate
                String color = occupancyRate >= 70 ? "#059669" : occupancyRate >= 40 ? "#d97706" : "#dc2626";
                occupancyRateLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");
            }

            System.out.println("Monthly stats updated: " + locationsThisMonth + " locations, " + String.format("%.2f", revenueThisMonth) + "€, " + String.format("%.1f", occupancyRate) + "% occupancy");

        } catch (Exception e) {
            System.err.println("Error updating monthly stats: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void hideSelectedDateInfo() {
        if (selectedDateInfo != null) {
            selectedDateInfo.setVisible(false);
        }
    }

    @FXML
    private void handleViewHistory() {
        if (vehicule == null) return;

        try {
            // Close current window
            Stage currentStage = (Stage) vehicleInfoLabel.getScene().getWindow();

            // Open historique view
            Platform.runLater(() -> {
                try {
                    FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/historique.fxml"));
                    javafx.scene.Parent root = loader.load();

                    HistoriqueController ctrl = loader.getController();
                    if (ctrl != null) {
                        ctrl.setVehicule(vehicule);
                    }

                    Stage dialog = new Stage();
                    dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
                    dialog.setScene(new javafx.scene.Scene(root, 1200, 800));
                    dialog.setTitle("Historique Détaillé - " + vehicule.getMarque() + " " + vehicule.getModele());

                    // Maximize window
                    javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
                    javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
                    dialog.setX(visualBounds.getMinX());
                    dialog.setY(visualBounds.getMinY());
                    dialog.setWidth(visualBounds.getWidth());
                    dialog.setHeight(visualBounds.getHeight() * 0.95);

                    dialog.show();
                    dialog.toFront();

                    // Close current window
                    currentStage.close();

                } catch (Exception e) {
                    System.err.println("Error opening historique: " + e.getMessage());
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            System.err.println("Error handling view history: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateCalendarDisplay() {
        if (calendarGrid == null) {
            System.out.println("Warning: calendarGrid is null in updateCalendarDisplay");
            return;
        }

        calendarGrid.getChildren().clear();

        // Update month label
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM yyyy", java.util.Locale.FRENCH);
        if (lblCalendarMonth != null) {
            lblCalendarMonth.setText(currentCalendarMonth.format(monthFormatter));
        } else {
            System.out.println("Warning: lblCalendarMonth is null");
        }

        // Add day headers
        String[] dayHeaders = {"Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim"};
        for (int i = 0; i < dayHeaders.length; i++) {
            Label dayHeader = new Label(dayHeaders[i]);
            dayHeader.setStyle("-fx-font-weight: bold; -fx-text-fill: #6b7280; -fx-font-size: 12px; -fx-alignment: center;");
            calendarGrid.add(dayHeader, i, 0);
        }

        // Calculate first day of month and number of days
        LocalDate firstDay = currentCalendarMonth;
        int daysInMonth = firstDay.lengthOfMonth();
        int startDayOfWeek = firstDay.getDayOfWeek().getValue() - 1; // Monday = 0

        // Add calendar days
        int row = 1;
        int col = startDayOfWeek;

        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = firstDay.withDayOfMonth(day);
            Button dayButton = createCalendarDayButton(date);

            calendarGrid.add(dayButton, col, row);

            col++;
            if (col > 6) {
                col = 0;
                row++;
            }
        }
    }

    private Button createCalendarDayButton(LocalDate date) {
        Button dayButton = new Button(String.valueOf(date.getDayOfMonth()));
        dayButton.setMinSize(35, 35);
        dayButton.setMaxSize(35, 35);

        // Determine status and styling
        String baseStyle = "-fx-font-size: 11px; -fx-font-weight: bold; -fx-background-radius: 4; -fx-border-radius: 4; -fx-cursor: hand;";
        String statusStyle = getDateStatusStyle(date);

        dayButton.setStyle(baseStyle + statusStyle);

        // Add click handler
        dayButton.setOnAction(e -> handleDateClick(date));

        return dayButton;
    }

    private String getDateStatusStyle(LocalDate date) {
        LocalDate today = LocalDate.now();

        // Past dates - gray
        if (date.isBefore(today)) {
            return "-fx-background-color: #f3f4f6; -fx-text-fill: #9ca3af; -fx-border-color: #e5e7eb;";
        }

        // Check if there's a location for this date
        Location location = locationsByDate.get(date);
        if (location == null) {
            // Available - green
            return "-fx-background-color: #dcfce7; -fx-text-fill: #166534; -fx-border-color: #10b981;";
        }

        // Determine status based on location
        Location.Status status = location.getStatus();
        switch (status) {
            case RESERVE:
                // Reserved - orange
                return "-fx-background-color: #fef3c7; -fx-text-fill: #92400e; -fx-border-color: #f59e0b;";
            case EN_COURS:
                // Currently rented - red
                return "-fx-background-color: #fee2e2; -fx-text-fill: #991b1b; -fx-border-color: #ef4444;";
            case TERMINE:
            case ANNULE:
            default:
                // Available - green
                return "-fx-background-color: #dcfce7; -fx-text-fill: #166534; -fx-border-color: #10b981;";
        }
    }

    private void handleDateClick(LocalDate date) {
        selectedDate = date;
        updateSelectedDateInfo();
    }

    private void updateSelectedDateInfo() {
        if (selectedDateInfo == null || selectedDate == null) return;

        selectedDateInfo.setVisible(true);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");
        lblSelectedDate.setText("Date sélectionnée: " + selectedDate.format(formatter));

        Location location = locationsByDate.get(selectedDate);
        LocalDate today = LocalDate.now();

        if (selectedDate.isBefore(today)) {
            lblSelectedDateStatus.setText("Statut: Date passée");
            lblSelectedDateDetails.setText("Cette date est dans le passé.");
        } else if (location == null) {
            lblSelectedDateStatus.setText("Statut: Disponible");
            lblSelectedDateDetails.setText("Le véhicule est disponible pour cette date.");
        } else {
            Location.Status status = location.getStatus();
            String statusText = getStatusText(status);
            lblSelectedDateStatus.setText("Statut: " + statusText);

            String details = String.format("Client: %s %s\nPériode: %s - %s\nPrix total: %.2f €",
                location.getClient().getNom(),
                location.getClient().getPrenom(),
                location.getDateDebut().format(formatter),
                location.getDateFinPrevue().format(formatter),
                location.getPrixTotal());
            lblSelectedDateDetails.setText(details);
        }
    }

    private String getStatusText(Location.Status status) {
        switch (status) {
            case RESERVE:
                return "Réservé";
            case EN_COURS:
                return "En cours de location";
            case TERMINE:
                return "Terminé";
            case ANNULE:
                return "Annulé";
            default:
                return "Inconnu";
        }
    }

    @FXML
    private void handlePrevMonth() {
        currentCalendarMonth = currentCalendarMonth.minusMonths(1);
        updateCalendarDisplay();
        updateMonthlyStats();
        // Hide selected date info when changing months
        if (selectedDateInfo != null) {
            selectedDateInfo.setVisible(false);
        }
    }

    @FXML
    private void handleNextMonth() {
        currentCalendarMonth = currentCalendarMonth.plusMonths(1);
        updateCalendarDisplay();
        updateMonthlyStats();
        // Hide selected date info when changing months
        if (selectedDateInfo != null) {
            selectedDateInfo.setVisible(false);
        }
    }

    @FXML
    private void handleRefresh() {
        System.out.println("Refreshing calendar data...");
        if (vehicule != null) {
            loadLocationData();
            updateCalendarDisplay();
            updateVehicleStatus();
            updateMonthlyStats();
        } else {
            System.out.println("No vehicle set, showing default calendar");
            updateCalendarDisplay();
        }
    }

    @FXML
    private void handleClose() {
        try {
            Stage stage = null;
            if (lblCalendarMonth != null && lblCalendarMonth.getScene() != null) {
                stage = (Stage) lblCalendarMonth.getScene().getWindow();
            } else if (vehicleInfoLabel != null && vehicleInfoLabel.getScene() != null) {
                stage = (Stage) vehicleInfoLabel.getScene().getWindow();
            }

            if (stage != null) {
                stage.close();
            } else {
                System.out.println("Warning: Could not find stage to close");
            }
        } catch (Exception e) {
            System.err.println("Error closing disponibilite window: " + e.getMessage());
            e.printStackTrace();
        }
    }
}