package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.beans.property.SimpleStringProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import dao.LocationDAO;
import dao.VehiculeDAO;
import dao.ClientDAO;
import model.Location;
import model.Vehicule;
import model.Client;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;

public class VehicleReturnPreviewController {
    
    // FXML Components - Enhanced
    @FXML private Label lblTotalReturns;
    @FXML private Label lblTodayReturns;
    @FXML private Label lblOverdueReturns;
    @FXML private Label lblWeekReturns;
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredCount;
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblLastUpdate;

    // Enhanced Filter Components
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterVehicleTypeCombo;
    @FXML private ComboBox<String> filterClientCombo;
    @FXML private DatePicker filterDatePicker;
    @FXML private DatePicker filterDateFromPicker;
    @FXML private DatePicker filterDateToPicker;
    @FXML private ComboBox<String> filterDaysLeftCombo;
    @FXML private ComboBox<String> filterPriceRangeCombo;
    @FXML private ComboBox<String> filterMarqueCombo;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterTodayOnly;
    @FXML private TextField searchField;
    
    // Enhanced Table Components
    @FXML private TableView<ReturnPreviewItem> returnTable;
    @FXML private TableColumn<ReturnPreviewItem, String> colSelect;
    @FXML private TableColumn<ReturnPreviewItem, String> colVehicle;
    @FXML private TableColumn<ReturnPreviewItem, String> colClient;
    @FXML private TableColumn<ReturnPreviewItem, String> colPhone;
    @FXML private TableColumn<ReturnPreviewItem, String> colStartDate;
    @FXML private TableColumn<ReturnPreviewItem, String> colReturnDate;
    @FXML private TableColumn<ReturnPreviewItem, String> colDaysLeft;
    @FXML private TableColumn<ReturnPreviewItem, String> colStatus;
    @FXML private TableColumn<ReturnPreviewItem, String> colTotal;
    @FXML private TableColumn<ReturnPreviewItem, String> colPaid;
    @FXML private TableColumn<ReturnPreviewItem, String> colKm;
    @FXML private TableColumn<ReturnPreviewItem, String> colActions;


    @FXML private GridPane calendarGrid;
    @FXML private Label lblCalendarMonth;
    @FXML private Label lblSelectedDate;
    @FXML private Label lblSelectedDateReturns;
    @FXML private VBox selectedDateInfo;
    
    // Data and Services
    private final LocationDAO locationDAO = new LocationDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private final ClientDAO clientDAO = new ClientDAO();
    
    private ObservableList<ReturnPreviewItem> returnItems;
    private ObservableList<ReturnPreviewItem> filteredItems;
    private LocalDate currentCalendarMonth;
    private Map<LocalDate, Integer> returnCountByDate;
    
    @FXML
    private void initialize() {
        try {
            System.out.println("Initializing VehicleReturnPreviewController...");

            // Initialize data structures
            if (returnItems == null) {
                returnItems = FXCollections.observableArrayList();
            }
            if (filteredItems == null) {
                filteredItems = FXCollections.observableArrayList();
            }
            if (returnCountByDate == null) {
                returnCountByDate = new HashMap<>();
            }

            // Setup components in order
            setupTable();
            setupFilters();
            setupCalendar();
            loadReturnData();
            updateStatistics();

            System.out.println("VehicleReturnPreviewController initialized successfully");

        } catch (Exception e) {
            System.err.println("Error initializing VehicleReturnPreviewController: " + e.getMessage());
            e.printStackTrace();

            // Initialize with empty data to prevent crashes
            if (returnItems == null) returnItems = FXCollections.observableArrayList();
            if (filteredItems == null) filteredItems = FXCollections.observableArrayList();
            if (returnCountByDate == null) returnCountByDate = new HashMap<>();

            showAlert("Erreur lors de l'initialisation: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
    
    private void setupTable() {
        try {
            // Configure table columns with null safety
            if (colVehicle != null) {
                colVehicle.setCellValueFactory(new PropertyValueFactory<>("vehicleInfo"));
            }
            if (colClient != null) {
                colClient.setCellValueFactory(new PropertyValueFactory<>("clientName"));
            }
            if (colStartDate != null) {
                colStartDate.setCellValueFactory(new PropertyValueFactory<>("startDate"));
            }
            if (colReturnDate != null) {
                colReturnDate.setCellValueFactory(new PropertyValueFactory<>("returnDate"));
            }
            if (colDaysLeft != null) {
                colDaysLeft.setCellValueFactory(new PropertyValueFactory<>("daysLeft"));
                // Add custom cell factory for styling overdue items
                colDaysLeft.setCellFactory(column -> new TableCell<ReturnPreviewItem, String>() {
                    @Override
                    protected void updateItem(String item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || item == null) {
                            setText(null);
                            setStyle("");
                        } else {
                            setText(item);
                            // Style overdue items in red
                            if (item.contains("retard")) {
                                setStyle("-fx-text-fill: #ef4444; -fx-font-weight: bold;");
                            } else if (item.equals("Aujourd'hui")) {
                                setStyle("-fx-text-fill: #f59e0b; -fx-font-weight: bold;");
                            } else {
                                setStyle("");
                            }
                        }
                    }
                });
            }
            if (colStatus != null) {
                colStatus.setCellValueFactory(new PropertyValueFactory<>("status"));
            }
            if (colTotal != null) {
                colTotal.setCellValueFactory(new PropertyValueFactory<>("total"));
            }

            // Setup additional columns with null safety
            if (colPhone != null) {
                colPhone.setCellValueFactory(cellData -> {
                    ReturnPreviewItem item = cellData.getValue();
                    if (item != null && item.getLocation() != null && item.getLocation().getClient() != null) {
                        String phone = item.getLocation().getClient().getTelephone();
                        return new SimpleStringProperty(phone != null ? phone : "N/A");
                    }
                    return new SimpleStringProperty("N/A");
                });
            }

            if (colPaid != null) {
                colPaid.setCellValueFactory(cellData -> {
                    // For now, show "Payé" or "En attente" based on status
                    ReturnPreviewItem item = cellData.getValue();
                    if (item != null && item.getLocation() != null) {
                        String status = item.getLocation().getStatus() == Location.Status.TERMINE ? "Payé" : "En attente";
                        return new SimpleStringProperty(status);
                    }
                    return new SimpleStringProperty("En attente");
                });
            }

            if (colKm != null) {
                colKm.setCellValueFactory(cellData -> {
                    ReturnPreviewItem item = cellData.getValue();
                    if (item != null && item.getLocation() != null && item.getLocation().getVehicule() != null) {
                        Integer metrage = item.getLocation().getVehicule().getMetrage();
                        return new SimpleStringProperty(metrage != null ? metrage.toString() + " km" : "N/A");
                    }
                    return new SimpleStringProperty("N/A");
                });
            }

            if (colSelect != null) {
                colSelect.setCellFactory(param -> new TableCell<ReturnPreviewItem, String>() {
                    private final CheckBox checkBox = new CheckBox();

                    {
                        checkBox.setOnAction(e -> {
                            // Handle selection logic here if needed
                            System.out.println("Item selected: " + getIndex());
                        });
                    }

                    @Override
                    protected void updateItem(String item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || getIndex() < 0) {
                            setGraphic(null);
                        } else {
                            setGraphic(checkBox);
                        }
                    }
                });
            }

            // Setup actions column with robust button handling
            if (colActions != null) {
                colActions.setCellFactory(param -> new TableCell<ReturnPreviewItem, String>() {
                    private final Button viewButton = new Button("Voir");
                    private final Button detailButton = new Button("Détails");
                    private final HBox buttonBox = new HBox(5);

                    {
                        // Style buttons
                        viewButton.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; " +
                                          "-fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 11px;");
                        detailButton.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; " +
                                            "-fx-background-radius: 4; -fx-padding: 4 8; -fx-font-size: 11px;");

                        // Set button actions with error handling
                        viewButton.setOnAction(e -> {
                            try {
                                ReturnPreviewItem item = getTableView().getItems().get(getIndex());
                                if (item != null && item.getLocation() != null) {
                                    openLocationDetail(item.getLocation());
                                }
                            } catch (Exception ex) {
                                System.err.println("Error opening location detail: " + ex.getMessage());
                                showAlert("Erreur lors de l'ouverture des détails", Alert.AlertType.ERROR);
                            }
                        });

                        detailButton.setOnAction(e -> {
                            try {
                                ReturnPreviewItem item = getTableView().getItems().get(getIndex());
                                if (item != null) {
                                    showReturnDetails(item);
                                }
                            } catch (Exception ex) {
                                System.err.println("Error showing return details: " + ex.getMessage());
                                showAlert("Erreur lors de l'affichage des détails", Alert.AlertType.ERROR);
                            }
                        });

                        buttonBox.getChildren().addAll(viewButton, detailButton);
                    }

                    @Override
                    protected void updateItem(String item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || getIndex() < 0) {
                            setGraphic(null);
                        } else {
                            setGraphic(buttonBox);
                        }
                    }
                });
            }

            // Add double-click handler for table rows with error handling
            if (returnTable != null) {
                returnTable.setRowFactory(tv -> {
                    TableRow<ReturnPreviewItem> row = new TableRow<>();
                    row.setOnMouseClicked(event -> {
                        try {
                            if (event.getClickCount() == 2 && !row.isEmpty() && row.getItem() != null) {
                                openLocationDetail(row.getItem().getLocation());
                            }
                        } catch (Exception e) {
                            System.err.println("Error handling double-click: " + e.getMessage());
                        }
                    });
                    return row;
                });
            }

        } catch (Exception e) {
            System.err.println("Error setting up table: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupFilters() {
        try {
            // Status filter with null safety
            if (filterStatusCombo != null) {
                filterStatusCombo.setItems(FXCollections.observableArrayList(
                    "Tous", "RESERVE", "EN_COURS", "TERMINE", "ANNULE"
                ));
                filterStatusCombo.setValue("Tous");
                filterStatusCombo.setOnAction(e -> applyFilters());
            }

            // Vehicle filter with error handling
            if (filterVehicleTypeCombo != null && vehiculeDAO != null) {
                try {
                    List<Vehicule> vehicles = vehiculeDAO.findAll();
                    if (vehicles != null) {
                        ObservableList<String> vehicleTypes = FXCollections.observableArrayList();
                        vehicleTypes.add("Tous");
                        // Extract unique vehicle types/brands
                        vehicles.stream()
                            .map(v -> v.getMarque())
                            .filter(marque -> marque != null && !marque.isEmpty())
                            .distinct()
                            .forEach(vehicleTypes::add);
                        filterVehicleTypeCombo.setItems(vehicleTypes);
                        filterVehicleTypeCombo.setValue("Tous");
                        filterVehicleTypeCombo.setOnAction(e -> applyFilters());
                    }
                } catch (Exception e) {
                    System.err.println("Error loading vehicles for filter: " + e.getMessage());
                }
            }

            // Date filter with null safety
            if (filterDatePicker != null) {
                filterDatePicker.setOnAction(e -> applyFilters());
            }

            // Search field setup
            if (searchField != null) {
                searchField.textProperty().addListener((observable, oldValue, newValue) -> {
                    applyFilters();
                });
            }

            // Additional filter components
            if (filterOverdueOnly != null) {
                filterOverdueOnly.setOnAction(e -> applyFilters());
            }

            if (filterTodayOnly != null) {
                filterTodayOnly.setOnAction(e -> applyFilters());
            }

        } catch (Exception e) {
            System.err.println("Error setting up filters: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupCalendar() {
        currentCalendarMonth = LocalDate.now().withDayOfMonth(1);
        returnCountByDate = new HashMap<>();
        updateCalendarDisplay();
    }
    
    private void loadReturnData() {
        try {
            // Initialize collections if null
            if (returnItems == null) {
                returnItems = FXCollections.observableArrayList();
            } else {
                returnItems.clear();
            }

            if (returnCountByDate == null) {
                returnCountByDate = new HashMap<>();
            } else {
                returnCountByDate.clear();
            }

            // Load data with comprehensive error handling
            if (locationDAO != null) {
                List<Location> allLocations = locationDAO.findAll();
                if (allLocations != null) {
                    List<Location> activeLocations = allLocations.stream()
                        .filter(loc -> {
                            try {
                                // Include locations that are in progress or have expected return dates
                                return (loc.getStatus() == Location.Status.EN_COURS ||
                                       loc.getStatus() == Location.Status.RESERVE ||
                                       (loc.getDateFinReelle() == null && loc.getDateFinPrevue() != null)) &&
                                       loc.getVehicule() != null && loc.getClient() != null;
                            } catch (Exception e) {
                                System.err.println("Error filtering location " + loc.getId() + ": " + e.getMessage());
                                return false;
                            }
                        })
                        .collect(Collectors.toList());

                    // Create return preview items
                    for (Location location : activeLocations) {
                        try {
                            ReturnPreviewItem item = new ReturnPreviewItem(location);
                            returnItems.add(item);

                            // Count returns by date for calendar
                            LocalDate returnDate = location.getDateFinPrevue();
                            if (returnDate != null) {
                                returnCountByDate.merge(returnDate, 1, Integer::sum);
                            }
                        } catch (Exception e) {
                            System.err.println("Error creating return preview item for location " +
                                             location.getId() + ": " + e.getMessage());
                        }
                    }
                }
            }

            // Update filtered items and table
            if (filteredItems == null) {
                filteredItems = FXCollections.observableArrayList();
            }
            filteredItems.clear();
            filteredItems.addAll(returnItems);

            if (returnTable != null) {
                returnTable.setItems(filteredItems);
            }

            // Update calendar and statistics
            updateCalendarDisplay();
            updateStatistics();

            System.out.println("Loaded " + returnItems.size() + " return preview items");

        } catch (Exception e) {
            System.err.println("Error loading return data: " + e.getMessage());
            e.printStackTrace();
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), Alert.AlertType.ERROR);

            // Initialize with empty data to prevent crashes
            if (returnItems == null) returnItems = FXCollections.observableArrayList();
            if (filteredItems == null) filteredItems = FXCollections.observableArrayList();
            if (returnCountByDate == null) returnCountByDate = new HashMap<>();
        }
    }

    private void updateStatistics() {
        if (returnItems == null) return;

        LocalDate today = LocalDate.now();
        LocalDate weekEnd = today.plusDays(7);

        int totalReturns = returnItems.size();
        int todayReturns = (int) returnItems.stream()
            .filter(item -> today.equals(item.getLocation().getDateFinPrevue()))
            .count();
        int overdueReturns = (int) returnItems.stream()
            .filter(item -> {
                LocalDate returnDate = item.getLocation().getDateFinPrevue();
                return returnDate != null && returnDate.isBefore(today) &&
                       item.getLocation().getDateFinReelle() == null &&
                       item.getLocation().getStatus() != Location.Status.TERMINE;
            })
            .count();
        int weekReturns = (int) returnItems.stream()
            .filter(item -> {
                LocalDate returnDate = item.getLocation().getDateFinPrevue();
                return returnDate != null && !returnDate.isBefore(today) &&
                       !returnDate.isAfter(weekEnd);
            })
            .count();

        // Update labels with null safety
        if (lblTotalReturns != null) lblTotalReturns.setText(String.valueOf(totalReturns));
        if (lblTodayReturns != null) lblTodayReturns.setText(String.valueOf(todayReturns));
        if (lblOverdueReturns != null) lblOverdueReturns.setText(String.valueOf(overdueReturns));
        if (lblWeekReturns != null) lblWeekReturns.setText(String.valueOf(weekReturns));
        if (lblTotalCount != null) lblTotalCount.setText("Total: " + (filteredItems != null ? filteredItems.size() : 0) + " retours prévus");

        // Update additional statistics
        if (lblSelectedCount != null) lblSelectedCount.setText("Sélectionnés: 0");
        if (lblFilteredCount != null) lblFilteredCount.setText("Filtrés: " + (filteredItems != null ? filteredItems.size() : 0));

        // Calculate total revenue
        double totalRevenue = returnItems.stream()
            .mapToDouble(item -> {
                try {
                    return Double.parseDouble(item.getTotal());
                } catch (NumberFormatException e) {
                    return 0.0;
                }
            })
            .sum();
        if (lblTotalRevenue != null) lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));

        // Update last update time
        if (lblLastUpdate != null) {
            lblLastUpdate.setText("Dernière mise à jour: " +
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
        }
    }

    private void updateCalendarDisplay() {
        if (calendarGrid == null) return;

        calendarGrid.getChildren().clear();

        // Update month label
        if (lblCalendarMonth != null) {
            DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM yyyy");
            lblCalendarMonth.setText(currentCalendarMonth.format(monthFormatter));
        }

        // Add day headers
        String[] dayHeaders = {"Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim"};
        for (int i = 0; i < dayHeaders.length; i++) {
            Label dayHeader = new Label(dayHeaders[i]);
            dayHeader.setStyle("-fx-font-weight: bold; -fx-text-fill: #6b7280; -fx-font-size: 10px; -fx-alignment: center;");
            calendarGrid.add(dayHeader, i, 0);
        }

        // Calculate first day of month and number of days
        LocalDate firstDay = currentCalendarMonth;
        int daysInMonth = firstDay.lengthOfMonth();
        int startDayOfWeek = firstDay.getDayOfWeek().getValue() - 1; // Monday = 0

        // Add calendar days
        int row = 1;
        int col = startDayOfWeek;

        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = firstDay.withDayOfMonth(day);
            Button dayButton = createCalendarDayButton(date);

            calendarGrid.add(dayButton, col, row);

            col++;
            if (col > 6) {
                col = 0;
                row++;
            }
        }
    }

    private Button createCalendarDayButton(LocalDate date) {
        Button dayButton = new Button(String.valueOf(date.getDayOfMonth()));
        dayButton.setMinSize(30, 30);
        dayButton.setMaxSize(30, 30);

        // Base style
        String baseStyle = "-fx-background-radius: 4; -fx-font-size: 10px; -fx-border-radius: 4;";

        // Check if this date has returns
        int returnCount = returnCountByDate.getOrDefault(date, 0);
        LocalDate today = LocalDate.now();

        if (returnCount > 0) {
            if (date.equals(today)) {
                // Today with returns
                dayButton.setStyle(baseStyle + "-fx-background-color: #fbbf24; -fx-text-fill: white; -fx-font-weight: bold;");
            } else if (date.isBefore(today)) {
                // Overdue returns
                dayButton.setStyle(baseStyle + "-fx-background-color: #ef4444; -fx-text-fill: white; -fx-font-weight: bold;");
            } else {
                // Future returns
                dayButton.setStyle(baseStyle + "-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-weight: bold;");
            }
            dayButton.setText(date.getDayOfMonth() + "\n(" + returnCount + ")");
        } else {
            // No returns
            if (date.equals(today)) {
                dayButton.setStyle(baseStyle + "-fx-background-color: #e5e7eb; -fx-text-fill: #1f2937; -fx-border-color: #3b82f6; -fx-border-width: 2;");
            } else {
                dayButton.setStyle(baseStyle + "-fx-background-color: #f9fafb; -fx-text-fill: #6b7280;");
            }
        }

        // Add click handler
        dayButton.setOnAction(e -> selectCalendarDate(date));

        return dayButton;
    }

    private void selectCalendarDate(LocalDate date) {
        if (lblSelectedDate != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");
            lblSelectedDate.setText(date.format(formatter));
        }

        // Find returns for this date
        List<ReturnPreviewItem> dateReturns = returnItems.stream()
            .filter(item -> date.equals(item.getLocation().getDateFinPrevue()))
            .collect(Collectors.toList());

        if (lblSelectedDateReturns != null) {
            if (dateReturns.isEmpty()) {
                lblSelectedDateReturns.setText("Aucun retour prévu pour cette date");
            } else {
                StringBuilder info = new StringBuilder();
                info.append(dateReturns.size()).append(" retour(s) prévu(s):\n");
                for (ReturnPreviewItem item : dateReturns) {
                    info.append("• ").append(item.getVehicleInfo())
                        .append(" - ").append(item.getClientName()).append("\n");
                }
                lblSelectedDateReturns.setText(info.toString());
            }
        }
    }

    private void applyFilters() {
        try {
            if (returnItems == null || returnItems.isEmpty()) {
                if (filteredItems == null) {
                    filteredItems = FXCollections.observableArrayList();
                }
                filteredItems.clear();
                if (returnTable != null) {
                    returnTable.setItems(filteredItems);
                }
                updateStatistics();
                return;
            }

            // Get filter values safely
            String statusFilter = filterStatusCombo != null ? filterStatusCombo.getValue() : "Tous";
            String vehicleTypeFilter = filterVehicleTypeCombo != null ? filterVehicleTypeCombo.getValue() : "Tous";
            LocalDate dateFilter = filterDatePicker != null ? filterDatePicker.getValue() : null;
            String searchText = searchField != null ? searchField.getText() : "";
            boolean overdueOnly = filterOverdueOnly != null && filterOverdueOnly.isSelected();
            boolean todayOnly = filterTodayOnly != null && filterTodayOnly.isSelected();

            if (searchText == null) searchText = "";
            searchText = searchText.toLowerCase().trim();

            // Apply filters
            String finalSearchText = searchText;
            List<ReturnPreviewItem> filtered = returnItems.stream()
                .filter(item -> {
                    try {
                        if (item == null || item.getLocation() == null) {
                            return false;
                        }

                        // Status filter
                        if (statusFilter != null && !"Tous".equals(statusFilter)) {
                            String itemStatus = item.getLocation().getStatus() != null ?
                                item.getLocation().getStatus().name() : "EN_COURS";
                            if (!statusFilter.equals(itemStatus)) {
                                return false;
                            }
                        }

                        // Vehicle type filter
                        if (vehicleTypeFilter != null && !"Tous".equals(vehicleTypeFilter)) {
                            Vehicule vehicule = item.getLocation().getVehicule();
                            if (vehicule == null || !vehicleTypeFilter.equals(vehicule.getMarque())) {
                                return false;
                            }
                        }

                        // Date filter
                        if (dateFilter != null) {
                            if (!dateFilter.equals(item.getLocation().getDateFinPrevue())) {
                                return false;
                            }
                        }

                        // Overdue filter
                        if (overdueOnly && !item.isOverdue()) {
                            return false;
                        }

                        // Today filter
                        if (todayOnly && !item.isReturnToday()) {
                            return false;
                        }

                        // Search filter
                        if (!finalSearchText.isEmpty()) {
                            String vehicleInfo = item.getVehicleInfo() != null ? item.getVehicleInfo().toLowerCase() : "";
                            String clientName = item.getClientName() != null ? item.getClientName().toLowerCase() : "";
                            return vehicleInfo.contains(finalSearchText) || clientName.contains(finalSearchText);
                        }

                        return true;
                    } catch (Exception e) {
                        System.err.println("Error filtering item: " + e.getMessage());
                        return false;
                    }
                })
                .collect(Collectors.toList());

            // Update filtered items
            if (filteredItems == null) {
                filteredItems = FXCollections.observableArrayList();
            }
            filteredItems.clear();
            filteredItems.addAll(filtered);

            // Update table
            if (returnTable != null) {
                returnTable.setItems(filteredItems);
            }

            // Update statistics
            updateStatistics();

        } catch (Exception e) {
            System.err.println("Error applying filters: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void openLocationDetail(Location location) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_detail.fxml"));
            Parent root = loader.load();

            LocationDetailController controller = loader.getController();
            controller.setLocation(location);

            Stage stage = new Stage();
            stage.setTitle("Détails de la Location - " + location.getId());
            stage.setScene(new Scene(root));

            // Center the window
            Screen screen = Screen.getPrimary();
            Rectangle2D bounds = screen.getVisualBounds();
            stage.setX((bounds.getWidth() - 800) / 2);
            stage.setY((bounds.getHeight() - 600) / 2);
            stage.setWidth(800);
            stage.setHeight(600);

            stage.show();
        } catch (Exception e) {
            showAlert("Erreur lors de l'ouverture des détails: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void showReturnDetails(ReturnPreviewItem item) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Détails du Retour");
        alert.setHeaderText("Informations détaillées");

        StringBuilder content = new StringBuilder();
        Location loc = item.getLocation();
        content.append("Véhicule: ").append(item.getVehicleInfo()).append("\n");
        content.append("Client: ").append(item.getClientName()).append("\n");
        content.append("Date de début: ").append(item.getStartDate()).append("\n");
        content.append("Date de retour prévue: ").append(item.getReturnDate()).append("\n");
        content.append("Jours restants: ").append(item.getDaysLeft()).append("\n");
        content.append("Statut: ").append(item.getStatus()).append("\n");
        content.append("Montant total: ").append(item.getTotal()).append("\n");

        if (loc.getPenalite() > 0) {
            content.append("Pénalité: ").append(loc.getPenalite()).append(" DH\n");
        }

        alert.setContentText(content.toString());
        alert.showAndWait();
    }

    // FXML Event Handlers
    @FXML
    private void handleRefresh() {
        loadReturnData();
        updateStatistics();
        showAlert("Données actualisées avec succès", Alert.AlertType.INFORMATION);
    }



    @FXML
    private void handleClearFilters() {
        // Clear all filters
        if (filterStatusCombo != null) filterStatusCombo.setValue("Tous");
        if (filterVehicleTypeCombo != null) filterVehicleTypeCombo.setValue("Tous");
        if (filterDatePicker != null) filterDatePicker.setValue(null);
        if (filterClientCombo != null) filterClientCombo.setValue("Tous");
        if (filterDateFromPicker != null) filterDateFromPicker.setValue(null);
        if (filterDateToPicker != null) filterDateToPicker.setValue(null);
        if (filterDaysLeftCombo != null) filterDaysLeftCombo.setValue("Tous");
        if (filterPriceRangeCombo != null) filterPriceRangeCombo.setValue("Tous");
        if (filterMarqueCombo != null) filterMarqueCombo.setValue("Tous");
        if (filterOverdueOnly != null) filterOverdueOnly.setSelected(false);
        if (filterTodayOnly != null) filterTodayOnly.setSelected(false);
        if (searchField != null) searchField.clear();

        // Refresh data
        loadReturnData();
        updateStatistics();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handlePrevMonth() {
        currentCalendarMonth = currentCalendarMonth.minusMonths(1);
        updateCalendarDisplay();
    }

    @FXML
    private void handleNextMonth() {
        currentCalendarMonth = currentCalendarMonth.plusMonths(1);
        updateCalendarDisplay();
    }



    private void exportToCSV(File file) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            // CSV Header
            writer.append("Véhicule,Client,Date Début,Date Retour Prévue,Jours Restants,Statut,Total (DH)\n");

            // CSV Data
            for (ReturnPreviewItem item : filteredItems) {
                writer.append(item.getVehicleInfo()).append(",");
                writer.append(item.getClientName()).append(",");
                writer.append(item.getStartDate()).append(",");
                writer.append(item.getReturnDate()).append(",");
                writer.append(item.getDaysLeft()).append(",");
                writer.append(item.getStatus()).append(",");
                writer.append(item.getTotal()).append("\n");
            }
        }
    }

    private void showAlert(String message, Alert.AlertType type) {
        try {
            Alert alert = new Alert(type);
            alert.setTitle("Prévisions de Retour des Véhicules");
            alert.setHeaderText(null);
            alert.setContentText(message != null ? message : "Une erreur s'est produite");
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing alert: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Overloaded method for simple error messages
    private void showAlert(String message) {
        showAlert(message, Alert.AlertType.ERROR);
    }

    // Enhanced Export Methods
    @FXML
    private void handleExportCSV() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les prévisions de retour");
            fileChooser.setInitialFileName("previsions_retour.csv");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );

            File file = fileChooser.showSaveDialog(returnTable.getScene().getWindow());
            if (file != null) {
                exportToCSV(file);
                showAlert("Export réussi vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleExportExcel() {
        showAlert("Export Excel - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportPDF() {
        showAlert("Export PDF - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleExportFiltered() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les retours filtrés");
            fileChooser.setInitialFileName("retours_filtres.csv");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );

            File file = fileChooser.showSaveDialog(returnTable.getScene().getWindow());
            if (file != null) {
                exportToCSV(file);
                showAlert("Export filtré réussi vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export filtré: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleExportOverdue() {
        try {
            List<ReturnPreviewItem> overdueItems = filteredItems.stream()
                .filter(item -> item.getDaysLeftNumeric() < 0)
                .collect(Collectors.toList());

            if (overdueItems.isEmpty()) {
                showAlert("Aucun retour en retard trouvé", Alert.AlertType.INFORMATION);
                return;
            }

            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter les retours en retard");
            fileChooser.setInitialFileName("retours_en_retard.csv");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );

            File file = fileChooser.showSaveDialog(returnTable.getScene().getWindow());
            if (file != null) {
                exportOverdueToCSV(file, overdueItems);
                showAlert("Export retards réussi vers: " + file.getAbsolutePath(), Alert.AlertType.INFORMATION);
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export des retards: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleBulkActions() {
        showAlert("Actions Groupées - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleSendReminders() {
        showAlert("Rappels SMS/Email - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleGenerateReports() {
        showAlert("Génération de Rapports - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handlePrintLabels() {
        showAlert("Impression d'Étiquettes - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void handleSettings() {
        showAlert("Paramètres - Fonctionnalité à venir", Alert.AlertType.INFORMATION);
    }

    @FXML
    private void closeWindow() {
        Stage stage = (Stage) returnTable.getScene().getWindow();
        stage.close();
    }

    private void exportOverdueToCSV(File file, List<ReturnPreviewItem> overdueItems) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            // CSV Header
            writer.append("Véhicule,Client,Date Début,Date Retour Prévue,Jours de Retard,Statut,Total (DH)\n");

            // CSV Data
            for (ReturnPreviewItem item : overdueItems) {
                writer.append(item.getVehicleInfo()).append(",");
                writer.append(item.getClientName()).append(",");
                writer.append(item.getStartDate()).append(",");
                writer.append(item.getReturnDate()).append(",");
                writer.append(String.valueOf(Math.abs(item.getDaysLeftNumeric()))).append(",");
                writer.append(item.getStatus()).append(",");
                writer.append(item.getTotal()).append("\n");
            }
        }
    }
}
