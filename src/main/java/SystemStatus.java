import dao.*;
import model.*;
import service.NotificationService;
import service.SystemTrayService;

import java.util.List;

/**
 * System status checker to verify all components are working
 */
public class SystemStatus {
    
    public static void main(String[] args) {
        System.out.println("=== LocationV12 System Status Check ===\n");
        
        try {
            // Initialize database
            System.out.println("1. Database Initialization...");
            util.DatabaseInitializer.initialize();
            System.out.println("   ✓ Database initialized successfully\n");
            
            // Check DAOs
            System.out.println("2. Testing DAOs...");
            testDAOs();
            System.out.println("   ✓ All DAOs working correctly\n");
            
            // Check Services
            System.out.println("3. Testing Services...");
            testServices();
            System.out.println("   ✓ All services working correctly\n");
            
            // Check Notification System
            System.out.println("4. Testing Notification System...");
            testNotificationSystem();
            System.out.println("   ✓ Notification system working correctly\n");
            
            System.out.println("=== ALL SYSTEMS OPERATIONAL ===");
            System.out.println("✅ LocationV12 is ready for use!");
            
        } catch (Exception e) {
            System.err.println("❌ System check failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testDAOs() {
        // Test AdminDAO
        AdminDAO adminDAO = new AdminDAO();
        List<Admin> admins = adminDAO.findAll();
        System.out.println("   - AdminDAO: " + admins.size() + " admins found");
        
        // Test ClientDAO
        ClientDAO clientDAO = new ClientDAO();
        List<Client> clients = clientDAO.findAll();
        System.out.println("   - ClientDAO: " + clients.size() + " clients found");
        
        // Test VehiculeDAO
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        System.out.println("   - VehiculeDAO: " + vehicules.size() + " vehicles found");
        
        // Test LocationDAO
        LocationDAO locationDAO = new LocationDAO();
        List<Location> locations = locationDAO.findAll();
        System.out.println("   - LocationDAO: " + locations.size() + " locations found");
        
        // Test PaiementDAO
        PaiementDAO paiementDAO = new PaiementDAO();
        List<Paiement> paiements = paiementDAO.findAll();
        System.out.println("   - PaiementDAO: " + paiements.size() + " payments found");
        
        // Test NotificationDAO
        NotificationDAO notificationDAO = new NotificationDAO();
        List<Notification> notifications = notificationDAO.findAll();
        System.out.println("   - NotificationDAO: " + notifications.size() + " notifications found");
        
        // Test NotificationSettingsDAO
        NotificationSettingsDAO settingsDAO = new NotificationSettingsDAO();
        List<NotificationSettings> settings = settingsDAO.findAll();
        System.out.println("   - NotificationSettingsDAO: " + settings.size() + " settings found");
    }
    
    private static void testServices() {
        // Test NotificationService
        NotificationService notificationService = NotificationService.getInstance();
        System.out.println("   - NotificationService: Initialized successfully");
        
        // Test SystemTrayService
        SystemTrayService systemTrayService = SystemTrayService.getInstance();
        boolean traySupported = systemTrayService.isSupported();
        System.out.println("   - SystemTrayService: " + (traySupported ? "Supported" : "Not supported on this platform"));
    }
    
    private static void testNotificationSystem() {
        AdminDAO adminDAO = new AdminDAO();
        List<Admin> admins = adminDAO.findAll();
        
        if (!admins.isEmpty()) {
            Admin admin = admins.get(0);
            NotificationService notificationService = NotificationService.getInstance();
            
            // Test getting notifications
            List<Notification> userNotifications = notificationService.getNotificationsForUser(admin);
            System.out.println("   - User notifications: " + userNotifications.size() + " found");
            
            // Test unread count
            long unreadCount = notificationService.getUnreadCountForUser(admin);
            System.out.println("   - Unread notifications: " + unreadCount);
            
            // Test notification settings
            NotificationSettingsDAO settingsDAO = new NotificationSettingsDAO();
            NotificationSettings settings = settingsDAO.findOrCreateByUser(admin);
            System.out.println("   - Notification settings: " + (settings != null ? "Configured" : "Default"));
            
        } else {
            System.out.println("   - No admin users found for testing");
        }
    }
}
