@echo off
echo ========================================
echo Building Simple Fixed Executable
echo ========================================

echo.
echo Creating a simple, portable executable...

echo.
echo Step 1: Creating temp directory...
if exist "C:\temp\loc" rmdir /s /q "C:\temp\loc"
mkdir "C:\temp\loc"

echo.
echo Step 2: Copying JAR to temp location...
copy "target\LocationV1-1.0.0.jar" "C:\temp\loc\"

echo.
echo Step 3: Creating executable in temp location...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input "C:\temp\loc" ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest "C:\temp\dist" ^
    --type exe ^
    --app-version 1.0.0 ^
    --vendor "YourCompany" ^
    --description "Location App" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --win-console

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Step 4: Copying back to project...
    if not exist "target\final" mkdir "target\final"
    copy "C:\temp\dist\LocationV1-1.0.0.exe" "target\final\"
    
    echo.
    echo ========================================
    echo SUCCESS! Fixed executable created!
    echo ========================================
    echo.
    echo Location: target\final\LocationV1-1.0.0.exe
    echo.
    dir "target\final\LocationV1-1.0.0.exe"
    echo.
    echo This version should work on fresh Windows PCs.
    
    echo.
    echo Cleaning temp files...
    rmdir /s /q "C:\temp\loc"
    rmdir /s /q "C:\temp\dist"
) else (
    echo Build failed!
)

echo.
pause
