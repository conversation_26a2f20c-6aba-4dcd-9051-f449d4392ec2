@echo off
echo ========================================
echo Building LocationV1 Executable
echo ========================================

echo.
echo Step 1: Cleaning previous builds...
call mvn clean

echo.
echo Step 2: Compiling and packaging...
call mvn package

echo.
echo Step 3: Creating custom JRE with jlink...
jlink --module-path "%JAVA_HOME%\jmods" ^
      --add-modules java.base,java.desktop,java.logging,java.naming,java.security.jgss,java.instrument,java.management,java.sql,java.xml,jdk.unsupported ^
      --output target\java-runtime ^
      --compress=2 ^
      --no-header-files ^
      --no-man-pages

echo.
echo Step 4: Creating Windows executable with jpackage...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" --input target ^
         --name "LocationV1" ^
         --main-jar "LocationV1-1.0.0.jar" ^
         --main-class Launcher ^
         --dest target\dist ^
         --type exe ^
         --runtime-image target\java-runtime ^
         --app-version "1.0.0" ^
         --vendor "Your Company" ^
         --description "Location de voitures - Application de gestion" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut

echo.
echo ========================================
echo Build completed!
echo ========================================
echo.
echo Your executable is located at: target\dist\LocationV1-1.0.0.exe
echo.
pause
