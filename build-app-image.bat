@echo off
echo ========================================
echo Building LocationV1 App Image (Portable)
echo ========================================

echo.
echo Step 1: Checking JAR file...
if not exist "target\LocationV1-1.0.0.jar" (
    echo ERROR: JAR file not found!
    pause
    exit /b 1
)

echo.
echo Step 2: Creating app-image (portable application)...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input target ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest target\app-image ^
    --type app-image ^
    --app-version 1.0.0 ^
    --vendor "Your Company" ^
    --description "Location de voitures - Application de gestion" ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --java-options "-Djava.awt.headless=false" ^
    --java-options "-Dprism.order=sw" ^
    --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Step 3: Creating installer from app-image...
    "C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
        --app-image target\app-image\LocationV1 ^
        --dest target\dist-final ^
        --type exe ^
        --app-version 1.0.0 ^
        --vendor "Your Company" ^
        --description "Location de voitures - Application de gestion" ^
        --win-dir-chooser ^
        --win-menu ^
        --win-shortcut ^
        --win-console ^
        --verbose
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo SUCCESS! Portable executable created!
        echo ========================================
        echo.
        echo Your files:
        echo 1. Portable app folder: target\app-image\LocationV1\
        echo 2. Installer: target\dist-final\LocationV1-1.0.0.exe
        echo.
        echo For maximum compatibility, use the installer from dist-final.
        echo.
        dir "target\dist-final\LocationV1-1.0.0.exe"
    else (
        echo Failed to create installer from app-image.
    )
) else (
    echo Failed to create app-image.
)

echo.
pause
