@echo off
echo Running jlink to create custom JRE...

"C:\Program Files\Java\jdk-17\bin\jlink.exe" --module-path "C:\Program Files\Java\jdk-17\jmods" --add-modules java.base,java.sql,java.desktop,java.logging,java.naming,java.security.jgss,java.instrument,java.management,java.xml,jdk.unsupported --output target\java-runtime --compress=2 --no-header-files --no-man-pages

if %ERRORLEVEL% EQU 0 (
    echo Custom JRE created successfully in target\java-runtime
) else (
    echo Failed to create custom JRE
)
