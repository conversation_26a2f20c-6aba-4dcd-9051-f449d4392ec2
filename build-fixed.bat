@echo off
echo ========================================
echo Building Fixed LocationV1 Executable
echo ========================================

echo.
echo Creating fixed executable for fresh Windows installations...

echo.
echo Step 1: Cleaning...
if exist "target\fixed" rmdir /s /q "target\fixed"

echo.
echo Step 2: Creating portable executable...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input target ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest target\fixed ^
    --type exe ^
    --app-version 1.0.0 ^
    --vendor "YourCompany" ^
    --description "Location de voitures" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --win-console ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --java-options "-Djava.awt.headless=false"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo FIXED EXECUTABLE CREATED!
    echo ========================================
    echo.
    echo Location: target\fixed\LocationV1-1.0.0.exe
    echo.
    dir "target\fixed\LocationV1-1.0.0.exe"
    echo.
    echo This version should work on fresh Windows installations.
) else (
    echo Build failed with error: %ERRORLEVEL%
)

echo.
pause
