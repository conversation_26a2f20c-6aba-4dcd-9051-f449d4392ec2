@echo off
echo ========================================
echo Creating LocationV1 Executable (Debug)
echo ========================================

echo.
echo Checking JAR file...
if not exist "target\LocationV1-1.0.0.jar" (
    echo ERROR: JAR file not found!
    exit /b 1
)
echo JAR file exists: target\LocationV1-1.0.0.jar

echo.
echo Creating dist directory...
if not exist "target\dist" mkdir "target\dist"

echo.
echo Running jpackage with verbose output...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input target ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest target\dist ^
    --type exe ^
    --app-version 1.0.0 ^
    --vendor "Your Company" ^
    --description "Location de voitures - Application de gestion" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --verbose

echo.
echo Exit code: %ERRORLEVEL%

echo.
echo Checking if executable was created...
if exist "target\dist\LocationV1-1.0.0.exe" (
    echo SUCCESS! Executable created.
    dir "target\dist\LocationV1-1.0.0.exe"
) else (
    echo FAILED! Executable not found.
    echo Contents of target\dist:
    dir "target\dist"
)

pause
