@echo off
echo ========================================
echo LocationV1 - SERVER PC Setup
echo ========================================

echo.
echo This PC will be the DATABASE SERVER.
echo All other PCs will connect to this one.
echo.

echo Step 1: Configure MySQL for network access...
echo.
echo You need to run these MySQL commands:
echo.
echo mysql -u root -p
echo.
echo Then run these commands in MySQL:
echo.
echo CREATE DATABASE IF NOT EXISTS locationdb5;
echo CREATE USER 'locationapp'@'%%' IDENTIFIED BY 'location123';
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%';
echo FLUSH PRIVILEGES;
echo EXIT;
echo.

echo Step 2: Configure Windows Firewall...
echo Opening port 3306 for MySQL...
netsh advfirewall firewall add rule name="MySQL LocationV1" dir=in action=allow protocol=TCP localport=3306

echo.
echo Step 3: Find your IP address...
echo Your IP address (give this to other PCs):
echo.
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do echo %%a

echo.
echo Step 4: Test MySQL network access...
echo.
echo To test from another PC, run:
echo mysql -h YOUR_IP_ADDRESS -u locationapp -p locationdb5
echo (password: location123)
echo.

echo ========================================
echo SERVER PC Setup Instructions:
echo ========================================
echo.
echo 1. Make sure MySQL is installed and running
echo 2. Run the MySQL commands shown above
echo 3. Give your IP address to other PCs
echo 4. Keep this PC running when others use the app
echo.
pause
