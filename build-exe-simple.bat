@echo off
echo ========================================
echo Building LocationV1 Executable (Simple)
echo ========================================

echo.
echo Step 1: Cleaning and building JAR...
call mvn clean package -DskipTests

echo.
echo Step 2: Checking if JAR was created...
if not exist "target\LocationV1-1.0.0.jar" (
    echo ERROR: JAR file not found!
    echo Expected: target\LocationV1-1.0.0.jar
    echo.
    echo Checking what files exist in target:
    dir target\*.jar
    echo.
    echo Please check the build output above for errors.
    pause
    exit /b 1
)

echo JAR file found: target\LocationV1-1.0.0.jar

echo.
echo Step 3: Creating Windows executable with jpackage...
jpackage --input target ^
         --name "LocationV1" ^
         --main-jar "LocationV1-1.0.0.jar" ^
         --main-class Launcher ^
         --dest target\dist ^
         --type exe ^
         --app-version "1.0.0" ^
         --vendor "Your Company" ^
         --description "Location de voitures - Application de gestion" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut ^
         --java-options "-Dfile.encoding=UTF-8"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Your executable is located at: target\dist\LocationV1-1.0.0.exe
    echo.
    echo You can now distribute this .exe file to users.
    echo It includes everything needed to run the application.
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Common issues:
    echo 1. Make sure Java 17+ is installed and JAVA_HOME is set
    echo 2. Make sure the JAR file was created successfully
    echo 3. Check that jpackage is available (comes with Java 17+)
)

echo.
pause
