@echo off
echo ========================================
echo Fixing JVM Launch Issue - LocationV1
echo ========================================

echo.
echo This script will create a new executable that fixes the "Failed to launch JVM" error.
echo The issue occurs when the target PC doesn't have the same Java configuration.
echo.

echo Step 1: Cleaning previous builds...
if exist "target\dist-fixed" rmdir /s /q "target\dist-fixed"
if exist "target\app-image-fixed" rmdir /s /q "target\app-image-fixed"

echo.
echo Step 2: Creating self-contained app with embedded JRE...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input target ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest target\app-image-fixed ^
    --type app-image ^
    --app-version 1.0.0 ^
    --vendor "Your Company" ^
    --description "Location de voitures - Application de gestion" ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --java-options "-Djava.awt.headless=false" ^
    --java-options "-Dprism.order=sw,d3d" ^
    --java-options "-Djavafx.platform=desktop" ^
    --java-options "-Dsun.java2d.d3d=false" ^
    --java-options "-Dprism.verbose=false"

if %ERRORLEVEL% NEQ 0 (
    echo Failed to create app-image!
    pause
    exit /b 1
)

echo.
echo Step 3: Creating final installer with embedded JRE...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --app-image target\app-image-fixed\LocationV1 ^
    --dest target\dist-fixed ^
    --type exe ^
    --name LocationV1-Fixed ^
    --app-version 1.0.0 ^
    --vendor "Your Company" ^
    --description "Location de voitures - Application de gestion (Fixed JVM)" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --win-console

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo JVM ISSUE FIXED!
    echo ========================================
    echo.
    echo New executable created: target\dist-fixed\LocationV1-Fixed-1.0.0.exe
    echo.
    echo This version should work on any Windows PC, including fresh installations.
    echo.
    echo File details:
    dir "target\dist-fixed\LocationV1-Fixed-1.0.0.exe"
    echo.
    echo IMPORTANT: Use this new executable instead of the previous one.
    echo It includes a complete, self-contained Java runtime.
) else (
    echo Failed to create fixed executable!
)

echo.
pause
