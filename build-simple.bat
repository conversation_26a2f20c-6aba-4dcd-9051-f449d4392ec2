@echo off
echo ========================================
echo Simple Build for LocationV1
echo ========================================

echo.
echo Step 1: Clean and compile...
call mvn clean compile

echo.
echo Step 2: Create fat JAR...
call mvn package

echo.
echo Step 3: Create executable JAR that can run with double-click...
echo Creating launcher script...

echo @echo off > target\LocationV1.bat
echo java -jar LocationV1-1.0.0.jar >> target\LocationV1.bat

echo.
echo ========================================
echo Simple build completed!
echo ========================================
echo.
echo You can run your application by:
echo 1. Double-clicking: target\LocationV1.bat
echo 2. Or running: java -jar target\LocationV1-1.0.0.jar
echo.
echo To create a proper .exe, run: build-exe.bat
echo.
pause
