@echo off
echo ========================================
echo Building LocationV1 Executable (Final)
echo ========================================

echo.
echo Step 1: Using existing JAR file...
if not exist "target\LocationV1-1.0.0.jar" (
    echo ERROR: JAR file not found!
    echo Expected: target\LocationV1-1.0.0.jar
    echo Please run Maven build first.
    pause
    exit /b 1
)

echo JAR file found: target\LocationV1-1.0.0.jar

echo.
echo Step 2: Creating Windows executable with jpackage...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" --input target ^
         --name "LocationV1" ^
         --main-jar "LocationV1-1.0.0.jar" ^
         --main-class Launcher ^
         --dest target\dist ^
         --type exe ^
         --app-version "1.0.0" ^
         --vendor "Your Company" ^
         --description "Location de voitures - Application de gestion" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut ^
         --java-options "-Dfile.encoding=UTF-8"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Your executable is located at: target\dist\LocationV1-1.0.0.exe
    echo.
    echo File size and details:
    dir "target\dist\LocationV1-1.0.0.exe"
    echo.
    echo You can now distribute this .exe file to users.
    echo It includes everything needed to run the application.
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo.
    echo jpackage failed with error code: %ERRORLEVEL%
)

echo.
pause
