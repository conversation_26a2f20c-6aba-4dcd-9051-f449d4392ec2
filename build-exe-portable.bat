@echo off
echo ========================================
echo Building Portable LocationV1 Executable
echo ========================================

echo.
echo Step 1: Checking JAR file...
if not exist "target\LocationV1-1.0.0.jar" (
    echo ERROR: JAR file not found!
    echo Please build the project first with Maven.
    pause
    exit /b 1
)
echo JAR file found: target\LocationV1-1.0.0.jar

echo.
echo Step 2: Creating portable executable with full JRE...
"C:\Program Files\Java\jdk-17\bin\jpackage.exe" ^
    --input target ^
    --name LocationV1 ^
    --main-jar LocationV1-1.0.0.jar ^
    --main-class Launcher ^
    --dest target\dist-portable ^
    --type exe ^
    --app-version 1.0.0 ^
    --vendor "Your Company" ^
    --description "Location de voitures - Application de gestion" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --win-console ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --java-options "-Djava.awt.headless=false" ^
    --java-options "-Dprism.order=sw" ^
    --java-options "-Djavafx.platform=desktop" ^
    --java-options "-Dprism.verbose=true" ^
    --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Portable executable created successfully!
    echo ========================================
    echo.
    echo Your executable is located at: target\dist-portable\LocationV1-1.0.0.exe
    echo.
    echo File details:
    dir "target\dist-portable\LocationV1-1.0.0.exe"
    echo.
    echo This version includes a complete JRE and should work on any Windows PC.
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo Check the output above for details.
)

echo.
pause
