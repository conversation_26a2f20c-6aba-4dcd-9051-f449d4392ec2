# LocationV1 - Build Instructions for Creating Executable

## Prerequisites

1. **Java 17 or higher** - Make sure JAVA_HOME is set
2. **Maven** - For building the project
3. **WiX Toolset** (already installed) - For creating Windows installers

## ✅ Configuration Updated

Your project is now configured to use the **Launcher** class as the main entry point. This ensures:
- Consistent behavior between IDE and packaged application
- Proper database initialization
- Better compatibility for executable creation

## Quick Start

### Interactive Build Menu (Recommended)
```bash
# Run this for an interactive menu with all options
build-and-test.bat
```

### Individual Commands

### Option 1: Simple JAR Build (Fastest)
```bash
# Run this to create a runnable JAR
build-simple.bat
```
This creates:
- `target/LocationV1-1.0.0.jar` - Fat JAR with all dependencies
- `target/LocationV1.bat` - Batch file to run the application

### Option 2: Native Windows Executable (Recommended)
```bash
# Run this to create a native .exe file (no modules - more compatible)
build-exe-no-modules.bat
```
This creates:
- `target/dist/LocationV1-1.0.0.exe` - Native Windows executable
- Complete installer with JRE included

### Option 3: Native Executable with Custom JRE (Advanced)
```bash
# Run this for smaller executable with custom JRE
build-exe.bat
```

## Manual Build Steps

If the batch files don't work, follow these manual steps:

### 1. Clean and Compile
```bash
mvn clean compile
```

### 2. Create Fat JAR
```bash
mvn package
```

### 3. Create Custom JRE (for smaller executable)
```bash
jlink --module-path "%JAVA_HOME%\jmods" ^
      --add-modules java.base,java.desktop,java.logging,java.naming,java.security.jgss,java.instrument,java.management,java.sql,java.xml,jdk.unsupported ^
      --output target\java-runtime ^
      --compress=2 ^
      --no-header-files ^
      --no-man-pages
```

### 4. Create Windows Executable
```bash
jpackage --input target ^
         --name "LocationV1" ^
         --main-jar "LocationV1-1.0.0.jar" ^
         --main-class Launcher ^
         --dest target\dist ^
         --type exe ^
         --runtime-image target\java-runtime ^
         --app-version "1.0.0" ^
         --vendor "Your Company" ^
         --description "Location de voitures - Application de gestion" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut
```

## Troubleshooting

### Common Issues:

1. **"jpackage command not found"**
   - Make sure you're using Java 17+ (jpackage is included)
   - Verify JAVA_HOME is set correctly

2. **"Module not found" errors**
   - Try the simple build first: `build-simple.bat`
   - This creates a fat JAR that should work on any system with Java

3. **JavaFX runtime errors**
   - The Launcher class handles JavaFX module loading
   - Make sure all dependencies are included in the fat JAR

### Testing Your Build:

1. **Test the JAR:**
   ```bash
   java -jar target/LocationV1-1.0.0.jar
   ```

2. **Test the executable:**
   - Navigate to `target/dist/`
   - Double-click `LocationV1-1.0.0.exe`

## Distribution

- **For end users:** Distribute the `.exe` file from `target/dist/`
- **For developers:** Share the fat JAR from `target/`

The executable includes everything needed to run (Java runtime + your application).

## IDE Configuration

### IntelliJ IDEA / Eclipse
Your IDE should now automatically use the **Launcher** class. If you need to update run configurations:

1. **Main Class:** `Launcher` (not `MainApp`)
2. **Module:** Your project module
3. **VM Options:** `-Dfile.encoding=UTF-8` (optional)

### Maven Commands for Development
```bash
# Run the application
mvn compile exec:java

# Run with JavaFX plugin (alternative)
mvn javafx:run

# Quick test run
run-app.bat
```

## Why Use Launcher?

- **Database Initialization:** Ensures database is set up before JavaFX starts
- **Module Compatibility:** Avoids module path issues in packaged applications
- **Consistent Entry Point:** Same behavior in IDE and executable
- **Better Error Handling:** Cleaner startup process
