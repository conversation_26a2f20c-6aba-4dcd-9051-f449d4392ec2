@echo off
echo ========================================
echo LocationV1 - CLIENT PC Configuration
echo ========================================

echo.
echo This creates configuration for PCs that connect to your server.
echo.

set /p server_ip="Enter SERVER PC IP address: "

echo.
echo Creating hibernate configuration for client PC...

echo ^<?xml version='1.0' encoding='utf-8'?^> > hibernate-client.cfg.xml
echo ^<!DOCTYPE hibernate-configuration PUBLIC >> hibernate-client.cfg.xml
echo         "-//Hibernate/Hibernate Configuration DTD 3.0//EN" >> hibernate-client.cfg.xml
echo         "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd"^> >> hibernate-client.cfg.xml
echo ^<hibernate-configuration^> >> hibernate-client.cfg.xml
echo     ^<session-factory^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.driver_class"^>com.mysql.cj.jdbc.Driver^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.url"^>****************************************************************************************^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.username"^>locationapp^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.password"^>location123^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.dialect"^>org.hibernate.dialect.MySQL8Dialect^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.hbm2ddl.auto"^>update^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="show_sql"^>false^</property^> >> hibernate-client.cfg.xml
echo         ^<!-- Mapping des entités --^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Vehicule"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Client"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Location"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Paiement"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Admin"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Agent"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.VehicleMaintenance"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.VehicleFailure"/^> >> hibernate-client.cfg.xml
echo     ^</session-factory^> >> hibernate-client.cfg.xml
echo ^</hibernate-configuration^> >> hibernate-client.cfg.xml

echo.
echo ========================================
echo Client Configuration Created!
echo ========================================
echo.
echo File created: hibernate-client.cfg.xml
echo.
echo Instructions for CLIENT PC:
echo 1. Install MySQL Workbench (or just MySQL client)
echo 2. Replace hibernate.cfg.xml with hibernate-client.cfg.xml
echo 3. Rebuild your application
echo 4. Run the application
echo.
echo The client will connect to server at: %server_ip%
echo.
pause
