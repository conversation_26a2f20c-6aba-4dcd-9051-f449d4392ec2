@echo off
echo ========================================
echo Building LocationV1 Executable (No Modules)
echo ========================================

echo.
echo Step 1: Cleaning previous builds...
call mvn clean

echo.
echo Step 2: Compiling and packaging...
call mvn package

echo.
echo Step 3: Creating Windows executable with jpackage (no modules)...
jpackage --input target ^
         --name "LocationV1" ^
         --main-jar "LocationV1-1.0.0.jar" ^
         --main-class Launcher ^
         --dest target\dist ^
         --type exe ^
         --app-version "1.0.0" ^
         --vendor "Your Company" ^
         --description "Location de voitures - Application de gestion" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut ^
         --java-options "-Dfile.encoding=UTF-8"

echo.
echo ========================================
echo Build completed!
echo ========================================
echo.
echo Your executable is located at: target\dist\LocationV1-1.0.0.exe
echo.
echo This version includes a full JRE and should work on any Windows system.
echo.
pause
