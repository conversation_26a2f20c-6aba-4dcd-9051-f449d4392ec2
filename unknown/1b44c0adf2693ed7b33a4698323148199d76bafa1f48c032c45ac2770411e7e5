<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.VehicleReturnPreviewController">
   <top>
      <VBox style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
         <HBox alignment="CENTER_LEFT" spacing="20">
            <Label style="-fx-text-fill: white; -fx-font-size: 28px; -fx-font-weight: bold;" text="📅 Prévisions de Retour des Véhicules" />
            <Region HBox.hgrow="ALWAYS" />
            <MenuButton style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="📊 Exporter">
               <items>
                  <MenuItem fx:id="exportCSV" onAction="#handleExportCSV" text="📄 Export CSV" />
                  <MenuItem fx:id="exportExcel" onAction="#handleExportExcel" text="📊 Export Excel" />
                  <MenuItem fx:id="exportPDF" onAction="#handleExportPDF" text="📋 Export PDF" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="exportFiltered" onAction="#handleExportFiltered" text="🔍 Export Filtré" />
                  <MenuItem fx:id="exportOverdue" onAction="#handleExportOverdue" text="⚠️ Export Retards" />
               </items>
            </MenuButton>
            <Button fx:id="btnBulkActions" onAction="#handleBulkActions" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="⚡ Actions Groupées" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
         </HBox>
         
         <!-- Enhanced Filter Section -->
         <VBox spacing="10" style="-fx-padding: 10 0 0 0;">
            <!-- Primary Filters Row -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="🔍 Filtres Principaux:" />
               <ComboBox fx:id="filterStatusCombo" promptText="Statut Location" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterVehicleTypeCombo" promptText="Type Véhicule" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterClientCombo" promptText="Client" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <DatePicker fx:id="filterDateFromPicker" promptText="Date début" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
               <DatePicker fx:id="filterDateToPicker" promptText="Date fin" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            </HBox>

            <!-- Advanced Filters Row -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="⚙️ Filtres Avancés:" />
               <ComboBox fx:id="filterDaysLeftCombo" promptText="Jours Restants" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterPriceRangeCombo" promptText="Gamme Prix" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterMarqueCombo" promptText="Marque" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <CheckBox fx:id="filterOverdueOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="Retards uniquement" />
               <CheckBox fx:id="filterTodayOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="Aujourd'hui uniquement" />
               <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✖ Effacer Tout" />
            </HBox>
         </VBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.7" orientation="HORIZONTAL">
         <!-- Main Content Area -->
         <VBox spacing="10" style="-fx-padding: 20;">
            <!-- Statistics Cards -->
            <HBox spacing="15">
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #f8fafc; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalReturns" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e40af;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b;" text="Retours Prévus" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #fef3c7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #fbbf24; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTodayReturns" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #d97706;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #92400e;" text="Aujourd'hui" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #fee2e2; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #f87171; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblOverdueReturns" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #dc2626;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #991b1b;" text="En Retard" />
               </VBox>
               
               <VBox alignment="CENTER" spacing="5" style="-fx-background-color: #dcfce7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #4ade80; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblWeekReturns" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #16a34a;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #15803d;" text="Cette Semaine" />
               </VBox>
            </HBox>
            
            <!-- Return Schedule Table -->
            <VBox spacing="10" VBox.vgrow="ALWAYS">
               <HBox alignment="CENTER_LEFT" spacing="10">
                  <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📋 Planning des Retours" />
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="searchField" onKeyReleased="#handleSearch" promptText="🔍 Rechercher..." style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-border-color: #d1d5db; -fx-border-radius: 8;" />
               </HBox>
               
               <TableView fx:id="returnTable" prefHeight="400.0" prefWidth="897.0" style="-fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12;" VBox.vgrow="ALWAYS">
                  <columns>
                     <TableColumn fx:id="colSelect" prefWidth="40.0" text="☑" />
                     <TableColumn fx:id="colVehicle" prefWidth="110.0" text="Véhicule" />
                     <TableColumn fx:id="colClient" prefWidth="100.0" text="Client" />
                     <TableColumn fx:id="colPhone" prefWidth="90.0" text="Téléphone" />
                     <TableColumn fx:id="colStartDate" prefWidth="85.0" text="Début" />
                     <TableColumn fx:id="colReturnDate" prefWidth="85.0" text="Retour Prévu" />
                     <TableColumn fx:id="colDaysLeft" prefWidth="70.0" text="Jours" />
                     <TableColumn fx:id="colStatus" prefWidth="80.0" text="Statut" />
                     <TableColumn fx:id="colTotal" prefWidth="70.0" text="Total" />
                     <TableColumn fx:id="colPaid" prefWidth="60.0" text="Payé" />
                     <TableColumn fx:id="colKm" prefWidth="60.0" text="KM" />
                     <TableColumn fx:id="colActions" prefWidth="120.0" text="Actions" />
                  </columns>
               </TableView>
            </VBox>
         </VBox>
         
         <!-- Calendar Sidebar -->
         <VBox spacing="15" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 0 1;">
            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📅 Calendrier des Retours" />
            
            <!-- Calendar Component -->
            <VBox fx:id="calendarContainer" spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 15; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
               <!-- Calendar Header -->
               <HBox alignment="CENTER" spacing="10">
                  <Button fx:id="btnPrevMonth" onAction="#handlePrevMonth" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-font-size: 16px;" text="◀" />
                  <Label fx:id="lblCalendarMonth" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Janvier 2024" HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnNextMonth" onAction="#handleNextMonth" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-font-size: 16px;" text="▶" />
               </HBox>
               
               <!-- Calendar Grid -->
               <GridPane fx:id="calendarGrid" hgap="2" vgap="2">
                  <!-- Calendar days will be populated programmatically -->
               </GridPane>
            </VBox>
            
            <!-- Legend -->
            <VBox spacing="8">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Légende:" />
               <HBox alignment="CENTER_LEFT" spacing="8">
                  <Label style="-fx-background-color: #dcfce7; -fx-padding: 4 8; -fx-background-radius: 4; -fx-font-size: 10px;" text="●" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #374151;" text="Retour Normal" />
               </HBox>
               <HBox alignment="CENTER_LEFT" spacing="8">
                  <Label style="-fx-background-color: #fef3c7; -fx-padding: 4 8; -fx-background-radius: 4; -fx-font-size: 10px;" text="●" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #374151;" text="Retour Aujourd'hui" />
               </HBox>
               <HBox alignment="CENTER_LEFT" spacing="8">
                  <Label style="-fx-background-color: #fee2e2; -fx-padding: 4 8; -fx-background-radius: 4; -fx-font-size: 10px;" text="●" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #374151;" text="En Retard" />
               </HBox>
            </VBox>
            
            <!-- Selected Date Info -->
            <VBox fx:id="selectedDateInfo" spacing="8" style="-fx-background-color: white; -fx-background-radius: 8; -fx-padding: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-border-width: 1;">
               <Label fx:id="lblSelectedDate" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Aucune date sélectionnée" />
               <Label fx:id="lblSelectedDateReturns" style="-fx-font-size: 12px; -fx-text-fill: #6b7280;" text="Cliquez sur une date pour voir les retours" />
            </VBox>
         </VBox>
      </SplitPane>
   </center>
   
   <bottom>
      <VBox style="-fx-background-color: #f8fafc; -fx-border-color: #e5e7eb; -fx-border-width: 1 0 0 0;">
         <!-- Detailed Statistics Bar -->
         <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-padding: 10 15;">
            <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-weight: bold;" text="Total: 0 retours prévus" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblSelectedCount" style="-fx-font-size: 12px; -fx-text-fill: #3b82f6;" text="Sélectionnés: 0" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblFilteredCount" style="-fx-font-size: 12px; -fx-text-fill: #059669;" text="Filtrés: 0" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblTotalRevenue" style="-fx-font-size: 12px; -fx-text-fill: #dc2626; -fx-font-weight: bold;" text="Revenus: 0.00 DH" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblLastUpdate" style="-fx-font-size: 11px; -fx-text-fill: #9ca3af;" text="Dernière mise à jour: --:--" />
         </HBox>

         <!-- Action Buttons Bar -->
         <HBox alignment="CENTER_RIGHT" spacing="12" style="-fx-padding: 10 15;">
            <Button fx:id="btnSendReminders" onAction="#handleSendReminders" style="-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="📧 Rappels SMS/Email" />
            <Button fx:id="btnGenerateReports" onAction="#handleGenerateReports" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="📊 Rapports" />
            <Button fx:id="btnPrintLabels" onAction="#handlePrintLabels" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="🏷️ Étiquettes" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnSettings" onAction="#handleSettings" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="⚙️ Paramètres" />
            <Button onAction="#closeWindow" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="❌ Fermer" />
         </HBox>
      </VBox>
   </bottom>
</BorderPane>
